"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Giveaways_GiveawayShopList_tsx";
exports.ids = ["components_Giveaways_GiveawayShopList_tsx"];
exports.modules = {

/***/ "./components/EmptyData/EmptyDataSubscribe.tsx":
/*!*****************************************************!*\
  !*** ./components/EmptyData/EmptyDataSubscribe.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmptyDataSubscribe)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_SubscribeForm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/SubscribeForm */ \"./components/SubscribeForm.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_SubscribeForm__WEBPACK_IMPORTED_MODULE_1__]);\n_Components_SubscribeForm__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst url = \"https://one.us17.list-manage.com/subscribe/post-json?u=ad9a89cf545cb75a43916e338&id=43f530f860\";\nfunction EmptyDataSubscribe({ title, subtitle, icon, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center justify-center text-center flex-col p-4 ${className || \"\"}`,\n        children: [\n            icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-ch leading-9\",\n                children: [\n                    \" \",\n                    title\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\EmptyData\\\\EmptyDataSubscribe.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-base text-cl max-w-lg mb-4\",\n                children: subtitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\EmptyData\\\\EmptyDataSubscribe.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 flex max-w-lg gap-4 flex-wrap\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_SubscribeForm__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    url: url\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\EmptyData\\\\EmptyDataSubscribe.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\EmptyData\\\\EmptyDataSubscribe.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\EmptyData\\\\EmptyDataSubscribe.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/EmptyData/EmptyDataSubscribe.tsx\n");

/***/ }),

/***/ "./components/Giveaways/GiveawayRendererWrapper.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/GiveawayRendererWrapper.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawayRendererWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Tasks/components/TaskTileLoader */ \"./components/Tasks/components/TaskTileLoader.tsx\");\n/* harmony import */ var _Root_context_Actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/context/Actions */ \"./context/Actions.ts\");\n/* harmony import */ var _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Root/context/AppContext */ \"./context/AppContext.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_GiveawayRenderer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/GiveawayRenderer */ \"./components/Giveaways/components/GiveawayRenderer.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__]);\n_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction GiveawayRendererWrapper({ giveaway, projectEvent, size }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { state: { isAuthenticated }, dispatch } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__.useAppContext)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GiveawayRenderer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        giveawayType: giveaway.giveawayType,\n        distributionType: giveaway.distributionType,\n        renderer: \"summary\",\n        placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                style: {\n                    background: \"transparent\",\n                    border: 0,\n                    padding: 0\n                },\n                loaderProps: {\n                    viewBox: \"0 0 300 32\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n                lineNumber: 30,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n            lineNumber: 29,\n            columnNumber: 9\n        }, void 0),\n        props: {\n            giveaway: giveaway,\n            projectEvent,\n            size,\n            onClick: ()=>{\n                if (!isAuthenticated) {\n                    dispatch({\n                        type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_2__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                        payload: null\n                    });\n                    return;\n                }\n                const newQuery = {\n                    ...router.query,\n                    taskid: \"claim\",\n                    giveaway: giveaway.id\n                };\n                router.push({\n                    pathname: router.pathname,\n                    query: newQuery\n                }, undefined, {\n                    shallow: true\n                });\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/GiveawayRendererWrapper.tsx\n");

/***/ }),

/***/ "./components/Giveaways/GiveawayShopList.tsx":
/*!***************************************************!*\
  !*** ./components/Giveaways/GiveawayShopList.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawayShopList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_EmptyData_EmptyDataSubscribe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/EmptyData/EmptyDataSubscribe */ \"./components/EmptyData/EmptyDataSubscribe.tsx\");\n/* harmony import */ var _Components_EmptyData_EmptyQuest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/EmptyData/EmptyQuest */ \"./components/EmptyData/EmptyQuest.tsx\");\n/* harmony import */ var _Components_ExpandableList_ExpandableModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/ExpandableList/ExpandableModal */ \"./components/ExpandableList/ExpandableModal.tsx\");\n/* harmony import */ var _Components_Loaders_ListSkeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Loaders/ListSkeleton */ \"./components/Loaders/ListSkeleton.tsx\");\n/* harmony import */ var _Components_Tasks_Claim_ClaimRewardBody__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/Tasks/Claim/ClaimRewardBody */ \"./components/Tasks/Claim/ClaimRewardBody.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _GiveawayRendererWrapper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./GiveawayRendererWrapper */ \"./components/Giveaways/GiveawayRendererWrapper.tsx\");\n/* harmony import */ var _hooks_useGetGiveaways__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useGetGiveaways */ \"./components/Giveaways/hooks/useGetGiveaways.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_10__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_EmptyData_EmptyDataSubscribe__WEBPACK_IMPORTED_MODULE_1__, _Components_Tasks_Claim_ClaimRewardBody__WEBPACK_IMPORTED_MODULE_5__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_6__, _GiveawayRendererWrapper__WEBPACK_IMPORTED_MODULE_8__, _hooks_useGetGiveaways__WEBPACK_IMPORTED_MODULE_9__]);\n([_Components_EmptyData_EmptyDataSubscribe__WEBPACK_IMPORTED_MODULE_1__, _Components_Tasks_Claim_ClaimRewardBody__WEBPACK_IMPORTED_MODULE_5__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_6__, _GiveawayRendererWrapper__WEBPACK_IMPORTED_MODULE_8__, _hooks_useGetGiveaways__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\nfunction GiveawayShopList({ projectEvent }) {\n    const { data: giveawayData, loading } = (0,_hooks_useGetGiveaways__WEBPACK_IMPORTED_MODULE_9__.useGetGiveaways)(projectEvent.id);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { taskid } = router.query;\n    const giveaways = giveawayData?.pGiveaways || [];\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_10__.useTranslation)(\"ssr\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_10__.useTranslation)(\"ssr\", {\n        keyPrefix: \"global\"\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"rounded-2xl overflow-hidden component relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 text-ch relative z-1 items-center flex gap-2 border-b border-c\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_6__.Gift, {\n                            className: \"text-[#d0457b]\",\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        t(\"giveaway.shopList.heading\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 divide-y divide-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_ListSkeleton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        total: 3\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    if (giveaways.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_EmptyData_EmptyDataSubscribe__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            className: \"min-h-[400px] mb-[50px]\",\n            title: t(\"giveaway.shopList.subheading\", {\n                projectPoints: globalT(\"projectPoints\")\n            }),\n            subtitle: t(\"giveaway.shopList.emptyDescription\", {\n                platform: globalT(\"platform\")\n            }),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_EmptyData_EmptyQuest__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                height: 180\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                lineNumber: 49,\n                columnNumber: 15\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this);\n    }\n    const expandTpl = ()=>{\n        if (!taskid) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ExpandableList_ExpandableModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_Claim_ClaimRewardBody__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                projectEvent: projectEvent,\n                onClose: ()=>{\n                    delete router.query[\"taskid\"];\n                    router.push(router, undefined, {\n                        shallow: true\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-ch relative z-1 items-center flex gap-2 mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_6__.Gift, {\n                                className: \"text-[#d0457b]\",\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            t(\"giveaway.shopList.subheading\", {\n                                projectPoints: globalT(\"projectPoints\")\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-cl text-sm\",\n                        children: t(\"giveaway.shopList.description\", {\n                            platform: globalT(\"platform\")\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 mb-10 ease-out grid-cols-1 sm:grid-cols-2 lg:grid-cols-3\",\n                children: giveaways.map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `overflow-hidden rounded-2xl component p-4`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayRendererWrapper__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            giveaway: item,\n                            size: \"large\",\n                            projectEvent: projectEvent\n                        }, key, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    }, key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            expandTpl()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayShopList.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/GiveawayShopList.tsx\n");

/***/ }),

/***/ "./components/Loaders/ListItemSkeleton.tsx":
/*!*************************************************!*\
  !*** ./components/Loaders/ListItemSkeleton.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-content-loader */ \"react-content-loader\");\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_content_loader__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ListItemSkeleton = (props)=>{\n    if (props.size === \"small\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_content_loader__WEBPACK_IMPORTED_MODULE_1___default()), {\n                height: 20,\n                width: \"100%\",\n                className: \"\",\n                backgroundColor: \"var(--skeleton-background)\",\n                foregroundColor: \"var(--skeleton-foreground)\",\n                uniqueKey: \"list-item-skeleton\",\n                ...props,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        cx: \"10\",\n                        cy: \"10\",\n                        r: \"10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        height: \"12\",\n                        rx: \"6\",\n                        ry: \"6\",\n                        width: \"95%\",\n                        x: \"36\",\n                        y: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_content_loader__WEBPACK_IMPORTED_MODULE_1___default()), {\n            height: 86,\n            width: \"100%\",\n            backgroundColor: \"var(--skeleton-background)\",\n            foregroundColor: \"var(--skeleton-foreground)\",\n            uniqueKey: \"list-item-skeleton\",\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"103\",\n                    y: \"16\",\n                    rx: \"3\",\n                    ry: \"3\",\n                    width: \"123\",\n                    height: \"8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"105\",\n                    y: \"36\",\n                    rx: \"3\",\n                    ry: \"3\",\n                    width: \"171\",\n                    height: \"8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"105\",\n                    y: \"56\",\n                    rx: \"3\",\n                    ry: \"3\",\n                    width: \"90\",\n                    height: \"8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"44\",\n                    cy: \"42\",\n                    r: \"32\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ListItemSkeleton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvTGlzdEl0ZW1Ta2VsZXRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWlEO0FBRWpELE1BQU1DLG1CQUFtQixDQUFDQztJQUN4QixJQUFJQSxNQUFNQyxJQUFJLEtBQUssU0FBUztRQUMxQixxQkFDRSw4REFBQ0M7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0wsNkRBQWFBO2dCQUNaTSxRQUFRO2dCQUNSQyxPQUFPO2dCQUNQRixXQUFVO2dCQUNWRyxpQkFBZ0I7Z0JBQ2hCQyxpQkFBZ0I7Z0JBQ2hCQyxXQUFVO2dCQUNULEdBQUdSLEtBQUs7O2tDQUVULDhEQUFDUzt3QkFBT0MsSUFBRzt3QkFBS0MsSUFBRzt3QkFBS0MsR0FBRTs7Ozs7O2tDQUMxQiw4REFBQ0M7d0JBQUtULFFBQU87d0JBQUtVLElBQUc7d0JBQUlDLElBQUc7d0JBQUlWLE9BQU07d0JBQU1XLEdBQUU7d0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSTdEO0lBRUEscUJBQ0UsOERBQUNmO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNMLDZEQUFhQTtZQUNaTSxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsaUJBQWdCO1lBQ2hCQyxpQkFBZ0I7WUFDaEJDLFdBQVU7WUFDVCxHQUFHUixLQUFLOzs4QkFFVCw4REFBQ2E7b0JBQUtHLEdBQUU7b0JBQU1DLEdBQUU7b0JBQUtILElBQUc7b0JBQUlDLElBQUc7b0JBQUlWLE9BQU07b0JBQU1ELFFBQU87Ozs7Ozs4QkFDdEQsOERBQUNTO29CQUFLRyxHQUFFO29CQUFNQyxHQUFFO29CQUFLSCxJQUFHO29CQUFJQyxJQUFHO29CQUFJVixPQUFNO29CQUFNRCxRQUFPOzs7Ozs7OEJBQ3RELDhEQUFDUztvQkFBS0csR0FBRTtvQkFBTUMsR0FBRTtvQkFBS0gsSUFBRztvQkFBSUMsSUFBRztvQkFBSVYsT0FBTTtvQkFBS0QsUUFBTzs7Ozs7OzhCQUNyRCw4REFBQ0s7b0JBQU9DLElBQUc7b0JBQUtDLElBQUc7b0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSWxDO0FBRUEsaUVBQWViLGdCQUFnQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvTG9hZGVycy9MaXN0SXRlbVNrZWxldG9uLnRzeD9kNDQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBDb250ZW50TG9hZGVyIGZyb20gJ3JlYWN0LWNvbnRlbnQtbG9hZGVyJztcclxuXHJcbmNvbnN0IExpc3RJdGVtU2tlbGV0b24gPSAocHJvcHM6IGFueSkgPT4ge1xyXG4gIGlmIChwcm9wcy5zaXplID09PSAnc21hbGwnKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTRcIj5cclxuICAgICAgICA8Q29udGVudExvYWRlclxyXG4gICAgICAgICAgaGVpZ2h0PXsyMH1cclxuICAgICAgICAgIHdpZHRoPXsnMTAwJSd9XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJcIlxyXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yPVwidmFyKC0tc2tlbGV0b24tYmFja2dyb3VuZClcIlxyXG4gICAgICAgICAgZm9yZWdyb3VuZENvbG9yPVwidmFyKC0tc2tlbGV0b24tZm9yZWdyb3VuZClcIlxyXG4gICAgICAgICAgdW5pcXVlS2V5PVwibGlzdC1pdGVtLXNrZWxldG9uXCJcclxuICAgICAgICAgIHsuLi5wcm9wc31cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8Y2lyY2xlIGN4PVwiMTBcIiBjeT1cIjEwXCIgcj1cIjEwXCIgLz5cclxuICAgICAgICAgIDxyZWN0IGhlaWdodD1cIjEyXCIgcng9XCI2XCIgcnk9XCI2XCIgd2lkdGg9XCI5NSVcIiB4PVwiMzZcIiB5PVwiNFwiIC8+XHJcbiAgICAgICAgPC9Db250ZW50TG9hZGVyPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJweS0yXCI+XHJcbiAgICAgIDxDb250ZW50TG9hZGVyXHJcbiAgICAgICAgaGVpZ2h0PXs4Nn1cclxuICAgICAgICB3aWR0aD17JzEwMCUnfVxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcj1cInZhcigtLXNrZWxldG9uLWJhY2tncm91bmQpXCJcclxuICAgICAgICBmb3JlZ3JvdW5kQ29sb3I9XCJ2YXIoLS1za2VsZXRvbi1mb3JlZ3JvdW5kKVwiXHJcbiAgICAgICAgdW5pcXVlS2V5PVwibGlzdC1pdGVtLXNrZWxldG9uXCJcclxuICAgICAgICB7Li4ucHJvcHN9XHJcbiAgICAgID5cclxuICAgICAgICA8cmVjdCB4PVwiMTAzXCIgeT1cIjE2XCIgcng9XCIzXCIgcnk9XCIzXCIgd2lkdGg9XCIxMjNcIiBoZWlnaHQ9XCI4XCIgLz5cclxuICAgICAgICA8cmVjdCB4PVwiMTA1XCIgeT1cIjM2XCIgcng9XCIzXCIgcnk9XCIzXCIgd2lkdGg9XCIxNzFcIiBoZWlnaHQ9XCI4XCIgLz5cclxuICAgICAgICA8cmVjdCB4PVwiMTA1XCIgeT1cIjU2XCIgcng9XCIzXCIgcnk9XCIzXCIgd2lkdGg9XCI5MFwiIGhlaWdodD1cIjhcIiAvPlxyXG4gICAgICAgIDxjaXJjbGUgY3g9XCI0NFwiIGN5PVwiNDJcIiByPVwiMzJcIiAvPlxyXG4gICAgICA8L0NvbnRlbnRMb2FkZXI+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgTGlzdEl0ZW1Ta2VsZXRvbjtcclxuIl0sIm5hbWVzIjpbIkNvbnRlbnRMb2FkZXIiLCJMaXN0SXRlbVNrZWxldG9uIiwicHJvcHMiLCJzaXplIiwiZGl2IiwiY2xhc3NOYW1lIiwiaGVpZ2h0Iiwid2lkdGgiLCJiYWNrZ3JvdW5kQ29sb3IiLCJmb3JlZ3JvdW5kQ29sb3IiLCJ1bmlxdWVLZXkiLCJjaXJjbGUiLCJjeCIsImN5IiwiciIsInJlY3QiLCJyeCIsInJ5IiwieCIsInkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Loaders/ListItemSkeleton.tsx\n");

/***/ }),

/***/ "./components/Loaders/ListSkeleton.tsx":
/*!*********************************************!*\
  !*** ./components/Loaders/ListSkeleton.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ListItemSkeleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ListItemSkeleton */ \"./components/Loaders/ListItemSkeleton.tsx\");\n\n\n\nconst ListSkeleton = ({ total = 4, size })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            ...Array(total)\n        ].map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ListItemSkeleton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                size: size\n            }, key, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListSkeleton.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ListSkeleton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvTGlzdFNrZWxldG9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBCO0FBQ3dCO0FBRWxELE1BQU1FLGVBQWUsQ0FBQyxFQUNwQkMsUUFBUSxDQUFDLEVBQ1RDLElBQUksRUFJTDtJQUNDLHFCQUNFO2tCQUNHO2VBQUlDLE1BQU1GO1NBQU8sQ0FBQ0csR0FBRyxDQUFDLENBQUNDLE1BQU1DLG9CQUM1Qiw4REFBQ1AseURBQWdCQTtnQkFBV0csTUFBTUE7ZUFBWEk7Ozs7OztBQUkvQjtBQUVBLGlFQUFlTixZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9Mb2FkZXJzL0xpc3RTa2VsZXRvbi50c3g/YWFiOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgTGlzdEl0ZW1Ta2VsZXRvbiBmcm9tICcuL0xpc3RJdGVtU2tlbGV0b24nO1xyXG5cclxuY29uc3QgTGlzdFNrZWxldG9uID0gKHtcclxuICB0b3RhbCA9IDQsXHJcbiAgc2l6ZSxcclxufToge1xyXG4gIHRvdGFsPzogbnVtYmVyO1xyXG4gIHNpemU/OiAnc21hbGwnIHwgJ2RlZmF1bHQnO1xyXG59KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIHtbLi4uQXJyYXkodG90YWwpXS5tYXAoKGl0ZW0sIGtleSkgPT4gKFxyXG4gICAgICAgIDxMaXN0SXRlbVNrZWxldG9uIGtleT17a2V5fSBzaXplPXtzaXplfSAvPlxyXG4gICAgICApKX1cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBMaXN0U2tlbGV0b247XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxpc3RJdGVtU2tlbGV0b24iLCJMaXN0U2tlbGV0b24iLCJ0b3RhbCIsInNpemUiLCJBcnJheSIsIm1hcCIsIml0ZW0iLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Loaders/ListSkeleton.tsx\n");

/***/ }),

/***/ "./components/SubscribeForm.tsx":
/*!**************************************!*\
  !*** ./components/SubscribeForm.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_ui_button__WEBPACK_IMPORTED_MODULE_2__, _ui_input__WEBPACK_IMPORTED_MODULE_3__]);\n([_ui_button__WEBPACK_IMPORTED_MODULE_2__, _ui_input__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction CustomForm({ status, onSubmit, setEmail }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"ssr\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                id: \"email-address\",\n                name: \"email\",\n                type: \"email\",\n                required: true,\n                className: \"w-full lg:w-[320px] h-11\",\n                placeholder: t(\"newsletter.placeholder\"),\n                onChange: (e)=>setEmail(e.target.value)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SubscribeForm.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                size: \"lg\",\n                onClick: onSubmit,\n                type: \"submit\",\n                className: \"w-full lg:w-auto\",\n                children: status === \"sending\" ? \"Subscribing...\" : \"Subscribe\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SubscribeForm.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-destructive\",\n                children: t(\"newsletter.errorMessage\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SubscribeForm.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this),\n            status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-ch\",\n                children: t(\"newsletter.successMessage\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SubscribeForm.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\nconst SubscribeForm = ({ url })=>{\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const submit = ()=>{\n        setStatus(\"sending\");\n        fetch(`${url}?email=${email}`, {\n            mode: \"no-cors\"\n        }).then((result)=>setStatus(\"success\")).catch((error)=>setStatus(\"error\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomForm, {\n        onSubmit: submit,\n        setEmail: setEmail,\n        status: status\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SubscribeForm.tsx\",\n        lineNumber: 57,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SubscribeForm);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SubscribeForm.tsx\n");

/***/ }),

/***/ "./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__]);\n_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex h-10 w-full rounded-md border border-input component-bg px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXVDO0FBQ1I7QUFLL0IsTUFBTUUsc0JBQVFELDZDQUFnQixDQUM1QixDQUFDLEVBQUVHLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdKLHFEQUFFQSxDQUNYLCtWQUNBSTtRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy91aS9pbnB1dC50c3g/ZGE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbiB9IGZyb20gJ0BSb290L3V0aWxzL3V0aWxzJztcclxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXHJcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XHJcblxyXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXHJcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxpbnB1dFxyXG4gICAgICAgIHR5cGU9e3R5cGV9XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICdmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBjb21wb25lbnQtYmcgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MCcsXHJcbiAgICAgICAgICBjbGFzc05hbWUsXHJcbiAgICAgICAgKX1cclxuICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICB7Li4ucHJvcHN9XHJcbiAgICAgIC8+XHJcbiAgICApO1xyXG4gIH0sXHJcbik7XHJcbklucHV0LmRpc3BsYXlOYW1lID0gJ0lucHV0JztcclxuXHJcbmV4cG9ydCB7IElucHV0IH07XHJcbiJdLCJuYW1lcyI6WyJjbiIsIlJlYWN0IiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/ui/input.tsx\n");

/***/ })

};
;
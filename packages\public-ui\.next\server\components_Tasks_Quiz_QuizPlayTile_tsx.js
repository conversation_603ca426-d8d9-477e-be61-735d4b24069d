"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Tasks_Quiz_QuizPlayTile_tsx";
exports.ids = ["components_Tasks_Quiz_QuizPlayTile_tsx"];
exports.modules = {

/***/ "./components/Loaders/SingleDot.tsx":
/*!******************************************!*\
  !*** ./components/Loaders/SingleDot.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-content-loader */ \"react-content-loader\");\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_content_loader__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst SingleDotLoader = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_content_loader__WEBPACK_IMPORTED_MODULE_2___default()), {\n        speed: 2,\n        width: 32,\n        height: 32,\n        viewBox: \"0 0 32 32\",\n        uniqueKey: \"single-dot-loader\",\n        backgroundColor: \"#cdcdcd\",\n        foregroundColor: \"#ddd\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n            cx: \"16\",\n            cy: \"16\",\n            r: \"16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n            lineNumber: 15,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SingleDotLoader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvU2luZ2xlRG90LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUN1QjtBQUVqRCxNQUFNRSxrQkFBa0IsQ0FBQ0Msc0JBQ3ZCLDhEQUFDRiw2REFBYUE7UUFDWkcsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsU0FBUTtRQUNSQyxXQUFVO1FBQ1ZDLGlCQUFnQjtRQUNoQkMsaUJBQWdCO1FBQ2YsR0FBR1AsS0FBSztrQkFFVCw0RUFBQ1E7WUFBT0MsSUFBRztZQUFLQyxJQUFHO1lBQUtDLEdBQUU7Ozs7Ozs7Ozs7O0FBSTlCLGlFQUFlWixlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdC50c3g/YTE5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgQ29udGVudExvYWRlciBmcm9tICdyZWFjdC1jb250ZW50LWxvYWRlcic7XHJcblxyXG5jb25zdCBTaW5nbGVEb3RMb2FkZXIgPSAocHJvcHM6IGFueSkgPT4gKFxyXG4gIDxDb250ZW50TG9hZGVyXHJcbiAgICBzcGVlZD17Mn1cclxuICAgIHdpZHRoPXszMn1cclxuICAgIGhlaWdodD17MzJ9XHJcbiAgICB2aWV3Qm94PVwiMCAwIDMyIDMyXCJcclxuICAgIHVuaXF1ZUtleT1cInNpbmdsZS1kb3QtbG9hZGVyXCJcclxuICAgIGJhY2tncm91bmRDb2xvcj1cIiNjZGNkY2RcIlxyXG4gICAgZm9yZWdyb3VuZENvbG9yPVwiI2RkZFwiXHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgPlxyXG4gICAgPGNpcmNsZSBjeD1cIjE2XCIgY3k9XCIxNlwiIHI9XCIxNlwiIC8+XHJcbiAgPC9Db250ZW50TG9hZGVyPlxyXG4pO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU2luZ2xlRG90TG9hZGVyO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb250ZW50TG9hZGVyIiwiU2luZ2xlRG90TG9hZGVyIiwicHJvcHMiLCJzcGVlZCIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsInVuaXF1ZUtleSIsImJhY2tncm91bmRDb2xvciIsImZvcmVncm91bmRDb2xvciIsImNpcmNsZSIsImN4IiwiY3kiLCJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Loaders/SingleDot.tsx\n");

/***/ }),

/***/ "./components/RadialProgress/index.tsx":
/*!*********************************************!*\
  !*** ./components/RadialProgress/index.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RadialProgress: () => (/* binding */ RadialProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst RadialProgress = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/recharts\"), __webpack_require__.e(\"components_RadialProgress_RadialProgress_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! @Components/RadialProgress/RadialProgress */ \"./components/RadialProgress/RadialProgress.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\RadialProgress\\\\index.tsx -> \" + \"@Components/RadialProgress/RadialProgress\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            height: 66,\n            width: 66\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RadialProgress\\\\index.tsx\",\n            lineNumber: 8,\n            columnNumber: 20\n        }, undefined)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTREO0FBQ3pCO0FBRTVCLE1BQU1FLGlCQUFpQkQsbURBQU9BLENBQ25DLElBQU0sMlNBQU87Ozs7OztJQUVYRSxLQUFLO0lBQ0xDLFNBQVMsa0JBQU0sOERBQUNKLHFFQUFlQTtZQUFDSyxRQUFRO1lBQUlDLE9BQU87Ozs7OztHQUVyRCIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvUmFkaWFsUHJvZ3Jlc3MvaW5kZXgudHN4P2ZmMDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFNpbmdsZURvdExvYWRlciBmcm9tICdAQ29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdCc7XHJcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYyc7XHJcblxyXG5leHBvcnQgY29uc3QgUmFkaWFsUHJvZ3Jlc3MgPSBkeW5hbWljKFxyXG4gICgpID0+IGltcG9ydCgnQENvbXBvbmVudHMvUmFkaWFsUHJvZ3Jlc3MvUmFkaWFsUHJvZ3Jlc3MnKSxcclxuICB7XHJcbiAgICBzc3I6IGZhbHNlLFxyXG4gICAgbG9hZGluZzogKCkgPT4gPFNpbmdsZURvdExvYWRlciBoZWlnaHQ9ezY2fSB3aWR0aD17NjZ9IC8+LFxyXG4gIH0sXHJcbik7XHJcbiJdLCJuYW1lcyI6WyJTaW5nbGVEb3RMb2FkZXIiLCJkeW5hbWljIiwiUmFkaWFsUHJvZ3Jlc3MiLCJzc3IiLCJsb2FkaW5nIiwiaGVpZ2h0Iiwid2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/RadialProgress/index.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/QuizPlayTile.tsx":
/*!************************************************!*\
  !*** ./components/Tasks/Quiz/QuizPlayTile.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var _Components_RadialProgress__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/RadialProgress */ \"./components/RadialProgress/index.tsx\");\n/* harmony import */ var _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Hooks/useTaskParticipation */ \"./hooks/useTaskParticipation.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _components_TaskTile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TaskTile */ \"./components/Tasks/components/TaskTile.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__, _components_TaskTile__WEBPACK_IMPORTED_MODULE_5__]);\n([_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__, _components_TaskTile__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst QuizPlayTile = ({ projectEventId, eventTask, onClick, locked, lockedReason })=>{\n    const { title, subTaskStats, iconUrl } = eventTask;\n    const { data, loading } = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__.useUserTaskParticipationList)(projectEventId);\n    const completedCount = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__.useChildCompletedCount)(data?.userTaskParticipation, eventTask?.id);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TaskTile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        onClick: onClick,\n        title: title,\n        locked: locked,\n        lockedReason: lockedReason,\n        isVerified: completedCount === subTaskStats.count,\n        points: subTaskStats.totalPoints || 0,\n        xp: subTaskStats.totalXp || 0,\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__.PuzzlePiece, {\n            className: \"h-6 w-6 text-green-500\",\n            weight: \"regular\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayTile.tsx\",\n            lineNumber: 35,\n            columnNumber: 13\n        }, void 0),\n        renderExtra: ()=>loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                width: 54,\n                height: 54\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayTile.tsx\",\n                lineNumber: 38,\n                columnNumber: 11\n            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RadialProgress__WEBPACK_IMPORTED_MODULE_2__.RadialProgress, {\n                progress: (completedCount || 0) / (subTaskStats.count || 1) * 100,\n                text: `${completedCount || 0}/${subTaskStats.count}`,\n                circleSize: 54,\n                textClass: \"fill-foreground\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayTile.tsx\",\n                lineNumber: 40,\n                columnNumber: 11\n            }, void 0),\n        iconUrl: iconUrl\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayTile.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuizPlayTile);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/QuizPlayTile.tsx\n");

/***/ })

};
;
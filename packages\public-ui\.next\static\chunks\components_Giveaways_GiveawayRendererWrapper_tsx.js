"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Giveaways_GiveawayRendererWrapper_tsx"],{

/***/ "./components/Giveaways/GiveawayRendererWrapper.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/GiveawayRendererWrapper.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GiveawayRendererWrapper; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Tasks/components/TaskTileLoader */ \"./components/Tasks/components/TaskTileLoader.tsx\");\n/* harmony import */ var _Root_context_Actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/context/Actions */ \"./context/Actions.ts\");\n/* harmony import */ var _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Root/context/AppContext */ \"./context/AppContext.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_GiveawayRenderer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/GiveawayRenderer */ \"./components/Giveaways/components/GiveawayRenderer.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GiveawayRendererWrapper(param) {\n    let { giveaway, projectEvent, size } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { state: { isAuthenticated }, dispatch } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__.useAppContext)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GiveawayRenderer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        giveawayType: giveaway.giveawayType,\n        distributionType: giveaway.distributionType,\n        renderer: \"summary\",\n        placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                style: {\n                    background: \"transparent\",\n                    border: 0,\n                    padding: 0\n                },\n                loaderProps: {\n                    viewBox: \"0 0 300 32\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n                lineNumber: 30,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n            lineNumber: 29,\n            columnNumber: 9\n        }, void 0),\n        props: {\n            giveaway: giveaway,\n            projectEvent,\n            size,\n            onClick: ()=>{\n                if (!isAuthenticated) {\n                    dispatch({\n                        type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_2__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                        payload: null\n                    });\n                    return;\n                }\n                const newQuery = {\n                    ...router.query,\n                    taskid: \"claim\",\n                    giveaway: giveaway.id\n                };\n                router.push({\n                    pathname: router.pathname,\n                    query: newQuery\n                }, undefined, {\n                    shallow: true\n                });\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_s(GiveawayRendererWrapper, \"3L2fxezn6yFNzkELYsPAHo7dtOU=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__.useAppContext\n    ];\n});\n_c = GiveawayRendererWrapper;\nvar _c;\n$RefreshReg$(_c, \"GiveawayRendererWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/GiveawayRendererWrapper.tsx\n"));

/***/ })

}]);
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Tasks_Rest_RestRawBody_tsx";
exports.ids = ["components_Tasks_Rest_RestRawBody_tsx"];
exports.modules = {

/***/ "./components/Tasks/Rest/BaseRestBody.tsx":
/*!************************************************!*\
  !*** ./components/Tasks/Rest/BaseRestBody.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_InputField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/InputField */ \"./components/InputField.tsx\");\n/* harmony import */ var _Components_TextEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/TextEditor */ \"./components/TextEditor.tsx\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! formik */ \"formik\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(formik__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_CollapsibleInfo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/CollapsibleInfo */ \"./components/Tasks/components/CollapsibleInfo.tsx\");\n/* harmony import */ var _components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/TaskCompletedCard */ \"./components/Tasks/components/TaskCompletedCard.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_InputField__WEBPACK_IMPORTED_MODULE_1__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_2__, _components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_6__]);\n([_Components_InputField__WEBPACK_IMPORTED_MODULE_1__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_2__, _components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction BaseRestBody({ scoredPoints, task, verified, verifyButton, onVerify }) {\n    const { description } = task;\n    const { validation, queryParams, headers, bodyParams } = task.info;\n    const participationMapper = (values)=>{\n        return values.map((item)=>{\n            return {\n                key: Object.keys(item)[0],\n                value: item[Object.keys(item)[0]]\n            };\n        });\n    };\n    const secureFieldFilter = (param)=>param?.value === \"secure-user-field\";\n    const atLeastOne = queryParams?.filter(secureFieldFilter).length || bodyParams?.filter(secureFieldFilter).length || headers?.filter(secureFieldFilter).length;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation)(\"translation\", {\n        keyPrefix: \"tasks.rest\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n        children: [\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextEditor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    defaultValue: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm\",\n                children: t(\"note\", {\n                    event: globalT(\"event_one\")\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-2.5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_3__.Formik, {\n                    initialValues: {},\n                    validate: (values)=>{},\n                    onSubmit: async (values, { setSubmitting, resetForm })=>{\n                        onVerify({\n                            queryParams: values.queryParams && participationMapper(values.queryParams),\n                            headers: values.headers && participationMapper(values.headers),\n                            bodyParams: values.bodyParams && participationMapper(values.bodyParams)\n                        });\n                    },\n                    children: ({ values, errors, touched, handleChange, handleBlur, handleSubmit, isSubmitting, dirty, isValid })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                !verified && !!queryParams?.length && queryParams.filter(secureFieldFilter).map((query, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_InputField__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        id: `queryParams.${index}.${query?.key}`,\n                                        name: `queryParams.${index}.${query?.key}`,\n                                        label: query?.key || \"\",\n                                        required: true,\n                                        requiredMark: true,\n                                        placeholder: `Enter your ${query?.key}`,\n                                        onChange: handleChange,\n                                        onBlur: handleBlur\n                                    }, `queryParams.${index}.${query?.key}`, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 21\n                                    }, this)),\n                                !verified && !!bodyParams?.length && bodyParams.filter(secureFieldFilter).map((query, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_InputField__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        id: `bodyParams.${index}.${query?.key}`,\n                                        label: query?.key || \"\",\n                                        required: true,\n                                        requiredMark: true,\n                                        placeholder: `Enter your ${query?.key}`,\n                                        onChange: handleChange,\n                                        onBlur: handleBlur\n                                    }, `bodyParams.${index}.${query?.key}`, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 21\n                                    }, this)),\n                                !verified && !!headers?.length && headers.filter(secureFieldFilter).map((query, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_InputField__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        id: `headers.${index}.${query?.key}`,\n                                        label: query?.key || \"\",\n                                        required: true,\n                                        requiredMark: true,\n                                        placeholder: `Enter your ${query?.key}`,\n                                        onChange: handleChange,\n                                        onBlur: handleBlur\n                                    }, `headers.${index}.${query?.key}`, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 21\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CollapsibleInfo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    initialState: false,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                        className: \"prose prose-slate prose-sm mb-5\",\n                                        children: [\n                                            \"Validation\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"mt-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"language-js\",\n                                                    children: validation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                verified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    points: scoredPoints,\n                                    frequency: task.frequency\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 17\n                                }, this) : verifyButton(atLeastOne ? !(isValid && dirty) : false)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\BaseRestBody.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BaseRestBody);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1Rhc2tzL1Jlc3QvQmFzZVJlc3RCb2R5LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFnRDtBQUNBO0FBUWhCO0FBQ1k7QUFDZ0I7QUFDSTtBQUNsQjtBQVU5QyxTQUFTTyxhQUFhLEVBQ3BCQyxZQUFZLEVBQ1pDLElBQUksRUFDSkMsUUFBUSxFQUNSQyxZQUFZLEVBQ1pDLFFBQVEsRUFDVTtJQUNsQixNQUFNLEVBQUVDLFdBQVcsRUFBRSxHQUFHSjtJQUN4QixNQUFNLEVBQUVLLFVBQVUsRUFBRUMsV0FBVyxFQUFFQyxPQUFPLEVBQUVDLFVBQVUsRUFBRSxHQUNwRFIsS0FBS1MsSUFBSTtJQUNYLE1BQU1DLHNCQUFzQixDQUFDQztRQUMzQixPQUFPQSxPQUFPQyxHQUFHLENBQUMsQ0FBQ0M7WUFDakIsT0FBTztnQkFDTEMsS0FBS0MsT0FBT0MsSUFBSSxDQUFDSCxLQUFLLENBQUMsRUFBRTtnQkFDekJJLE9BQU9KLElBQUksQ0FBQ0UsT0FBT0MsSUFBSSxDQUFDSCxLQUFLLENBQUMsRUFBRSxDQUFDO1lBQ25DO1FBQ0Y7SUFDRjtJQUVBLE1BQU1LLG9CQUFvQixDQUFDQyxRQUN6QkEsT0FBT0YsVUFBVTtJQUVuQixNQUFNRyxhQUNKZCxhQUFhZSxPQUFPSCxtQkFBbUJJLFVBQ3ZDZCxZQUFZYSxPQUFPSCxtQkFBbUJJLFVBQ3RDZixTQUFTYyxPQUFPSCxtQkFBbUJJO0lBRXJDLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUcxQiw0REFBY0EsQ0FBQyxlQUFlO1FBQUUyQixXQUFXO0lBQWE7SUFDdEUsTUFBTSxFQUFFRCxHQUFHRSxPQUFPLEVBQUUsR0FBRzVCLDREQUFjQSxDQUFDLGVBQWU7UUFBRTJCLFdBQVc7SUFBUztJQUUzRSxxQkFDRSw4REFBQzlCLDJDQUFRQTs7WUFDTlUsNkJBQ0MsOERBQUNzQjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ25DLDhEQUFVQTtvQkFBQ29DLGNBQWN4Qjs7Ozs7Ozs7Ozs7MEJBRzlCLDhEQUFDc0I7Z0JBQUlDLFdBQVU7MEJBQ1pKLEVBQUUsUUFBUTtvQkFBRU0sT0FBT0osUUFBUTtnQkFBYTs7Ozs7OzBCQUUzQyw4REFBQ0M7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNsQywwQ0FBTUE7b0JBQ0xxQyxlQUFlLENBQUM7b0JBQ2hCQyxVQUFVLENBQUNwQixVQUFZO29CQUN2QnFCLFVBQVUsT0FBT3JCLFFBQWEsRUFBRXNCLGFBQWEsRUFBRUMsU0FBUyxFQUFFO3dCQUN4RC9CLFNBQVM7NEJBQ1BHLGFBQ0VLLE9BQU9MLFdBQVcsSUFBSUksb0JBQW9CQyxPQUFPTCxXQUFXOzRCQUM5REMsU0FBU0ksT0FBT0osT0FBTyxJQUFJRyxvQkFBb0JDLE9BQU9KLE9BQU87NEJBQzdEQyxZQUNFRyxPQUFPSCxVQUFVLElBQUlFLG9CQUFvQkMsT0FBT0gsVUFBVTt3QkFDOUQ7b0JBQ0Y7OEJBRUMsQ0FBQyxFQUNBRyxNQUFNLEVBQ053QixNQUFNLEVBQ05DLE9BQU8sRUFDUEMsWUFBWSxFQUNaQyxVQUFVLEVBQ1ZDLFlBQVksRUFDWkMsWUFBWSxFQUNaQyxLQUFLLEVBQ0xDLE9BQU8sRUFDUixpQkFDQyw4REFBQ0M7NEJBQUtYLFVBQVVPOzRCQUFjWixXQUFVOztnQ0FDckMsQ0FBQzFCLFlBQ0EsQ0FBQyxDQUFDSyxhQUFhZ0IsVUFDZmhCLFlBQ0dlLE1BQU0sQ0FBQ0gsbUJBQ1BOLEdBQUcsQ0FBQyxDQUFDZ0MsT0FBT0Msc0JBQ1gsOERBQUN0RCw4REFBVUE7d0NBRVR1RCxJQUFJLENBQUMsWUFBWSxFQUFFRCxNQUFNLENBQUMsRUFBRUQsT0FBTzlCLElBQUksQ0FBQzt3Q0FDeENpQyxNQUFNLENBQUMsWUFBWSxFQUFFRixNQUFNLENBQUMsRUFBRUQsT0FBTzlCLElBQUksQ0FBQzt3Q0FDMUNrQyxPQUFPSixPQUFPOUIsT0FBTzt3Q0FDckJtQyxRQUFRO3dDQUNSQyxZQUFZO3dDQUNaQyxhQUFhLENBQUMsV0FBVyxFQUFFUCxPQUFPOUIsSUFBSSxDQUFDO3dDQUN2Q3NDLFVBQVVmO3dDQUNWZ0IsUUFBUWY7dUNBUkgsQ0FBQyxZQUFZLEVBQUVPLE1BQU0sQ0FBQyxFQUFFRCxPQUFPOUIsSUFBSSxDQUFDOzs7OztnQ0FXaEQsQ0FBQ2IsWUFDQSxDQUFDLENBQUNPLFlBQVljLFVBQ2RkLFdBQ0dhLE1BQU0sQ0FBQ0gsbUJBQ1BOLEdBQUcsQ0FBQyxDQUFDZ0MsT0FBT0Msc0JBQ1gsOERBQUN0RCw4REFBVUE7d0NBRVR1RCxJQUFJLENBQUMsV0FBVyxFQUFFRCxNQUFNLENBQUMsRUFBRUQsT0FBTzlCLElBQUksQ0FBQzt3Q0FDdkNrQyxPQUFPSixPQUFPOUIsT0FBTzt3Q0FDckJtQyxRQUFRO3dDQUNSQyxZQUFZO3dDQUNaQyxhQUFhLENBQUMsV0FBVyxFQUFFUCxPQUFPOUIsSUFBSSxDQUFDO3dDQUN2Q3NDLFVBQVVmO3dDQUNWZ0IsUUFBUWY7dUNBUEgsQ0FBQyxXQUFXLEVBQUVPLE1BQU0sQ0FBQyxFQUFFRCxPQUFPOUIsSUFBSSxDQUFDOzs7OztnQ0FVL0MsQ0FBQ2IsWUFDQSxDQUFDLENBQUNNLFNBQVNlLFVBQ1hmLFFBQ0djLE1BQU0sQ0FBQ0gsbUJBQ1BOLEdBQUcsQ0FBQyxDQUFDZ0MsT0FBT0Msc0JBQ1gsOERBQUN0RCw4REFBVUE7d0NBRVR1RCxJQUFJLENBQUMsUUFBUSxFQUFFRCxNQUFNLENBQUMsRUFBRUQsT0FBTzlCLElBQUksQ0FBQzt3Q0FDcENrQyxPQUFPSixPQUFPOUIsT0FBTzt3Q0FDckJtQyxRQUFRO3dDQUNSQyxZQUFZO3dDQUNaQyxhQUFhLENBQUMsV0FBVyxFQUFFUCxPQUFPOUIsSUFBSSxDQUFDO3dDQUN2Q3NDLFVBQVVmO3dDQUNWZ0IsUUFBUWY7dUNBUEgsQ0FBQyxRQUFRLEVBQUVPLE1BQU0sQ0FBQyxFQUFFRCxPQUFPOUIsSUFBSSxDQUFDOzs7Ozs4Q0FVN0MsOERBQUNuQixtRUFBZUE7b0NBQUMyRCxjQUFjOzhDQUM3Qiw0RUFBQ0M7d0NBQVE1QixXQUFVOzs0Q0FBa0M7MERBRW5ELDhEQUFDNkI7Z0RBQUk3QixXQUFVOzBEQUNiLDRFQUFDOEI7b0RBQUs5QixXQUFVOzhEQUFldEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBSXBDSix5QkFDQyw4REFBQ0wscUVBQWlCQTtvQ0FDaEI4RCxRQUFRM0Q7b0NBQ1I0RCxXQUFXM0QsS0FBSzJELFNBQVM7Ozs7OzJDQUczQnpELGFBQWFrQixhQUFhLENBQUVzQixDQUFBQSxXQUFXRCxLQUFJLElBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWhFO0FBRUEsaUVBQWUzQyxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9UYXNrcy9SZXN0L0Jhc2VSZXN0Qm9keS50c3g/Nzg1YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSW5wdXRGaWVsZCBmcm9tICdAQ29tcG9uZW50cy9JbnB1dEZpZWxkJztcclxuaW1wb3J0IFRleHRFZGl0b3IgZnJvbSAnQENvbXBvbmVudHMvVGV4dEVkaXRvcic7XHJcbmltcG9ydCB7XHJcbiAgTWF5YmUsXHJcbiAgUmVzdFBhcmFtLFxyXG4gIFJlc3RSYXdUYXNrRGF0YSxcclxuICBSZXN0VGFza1BhcnRpY2lwYXRpb25JbnB1dCxcclxuICBUYXNrLFxyXG59IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuaW1wb3J0IHsgRm9ybWlrIH0gZnJvbSAnZm9ybWlrJztcclxuaW1wb3J0IHsgRnJhZ21lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IENvbGxhcHNpYmxlSW5mbyBmcm9tICcuLi9jb21wb25lbnRzL0NvbGxhcHNpYmxlSW5mbyc7XHJcbmltcG9ydCBUYXNrQ29tcGxldGVkQ2FyZCBmcm9tICcuLi9jb21wb25lbnRzL1Rhc2tDb21wbGV0ZWRDYXJkJztcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICduZXh0LWkxOG5leHQnO1xyXG5cclxuaW50ZXJmYWNlIEJhc2VSZXN0Qm9keVByb3BzIHtcclxuICB0YXNrOiBUYXNrO1xyXG4gIHZlcmlmaWVkOiBib29sZWFuO1xyXG4gIHZlcmlmeUJ1dHRvbjogKGlzRGlzYWJsZWQ6IGJvb2xlYW4pID0+IFJlYWN0Tm9kZTtcclxuICBzY29yZWRQb2ludHM6IG51bWJlciB8IHVuZGVmaW5lZDtcclxuICBvblZlcmlmeTogKGRhdGE/OiBSZXN0VGFza1BhcnRpY2lwYXRpb25JbnB1dCkgPT4gdm9pZDtcclxufVxyXG5cclxuZnVuY3Rpb24gQmFzZVJlc3RCb2R5KHtcclxuICBzY29yZWRQb2ludHMsXHJcbiAgdGFzayxcclxuICB2ZXJpZmllZCxcclxuICB2ZXJpZnlCdXR0b24sXHJcbiAgb25WZXJpZnksXHJcbn06IEJhc2VSZXN0Qm9keVByb3BzKSB7XHJcbiAgY29uc3QgeyBkZXNjcmlwdGlvbiB9ID0gdGFzaztcclxuICBjb25zdCB7IHZhbGlkYXRpb24sIHF1ZXJ5UGFyYW1zLCBoZWFkZXJzLCBib2R5UGFyYW1zIH0gPVxyXG4gICAgdGFzay5pbmZvIGFzIFJlc3RSYXdUYXNrRGF0YTtcclxuICBjb25zdCBwYXJ0aWNpcGF0aW9uTWFwcGVyID0gKHZhbHVlczogYW55KSA9PiB7XHJcbiAgICByZXR1cm4gdmFsdWVzLm1hcCgoaXRlbTogYW55KSA9PiB7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAga2V5OiBPYmplY3Qua2V5cyhpdGVtKVswXSxcclxuICAgICAgICB2YWx1ZTogaXRlbVtPYmplY3Qua2V5cyhpdGVtKVswXV0sXHJcbiAgICAgIH07XHJcbiAgICB9KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBzZWN1cmVGaWVsZEZpbHRlciA9IChwYXJhbTogTWF5YmU8UmVzdFBhcmFtPikgPT5cclxuICAgIHBhcmFtPy52YWx1ZSA9PT0gJ3NlY3VyZS11c2VyLWZpZWxkJztcclxuXHJcbiAgY29uc3QgYXRMZWFzdE9uZSA9XHJcbiAgICBxdWVyeVBhcmFtcz8uZmlsdGVyKHNlY3VyZUZpZWxkRmlsdGVyKS5sZW5ndGggfHxcclxuICAgIGJvZHlQYXJhbXM/LmZpbHRlcihzZWN1cmVGaWVsZEZpbHRlcikubGVuZ3RoIHx8XHJcbiAgICBoZWFkZXJzPy5maWx0ZXIoc2VjdXJlRmllbGRGaWx0ZXIpLmxlbmd0aDtcclxuXHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigndHJhbnNsYXRpb24nLCB7IGtleVByZWZpeDogJ3Rhc2tzLnJlc3QnIH0pO1xyXG4gIGNvbnN0IHsgdDogZ2xvYmFsVCB9ID0gdXNlVHJhbnNsYXRpb24oJ3RyYW5zbGF0aW9uJywgeyBrZXlQcmVmaXg6ICdnbG9iYWwnIH0pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPEZyYWdtZW50PlxyXG4gICAgICB7ZGVzY3JpcHRpb24gJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxyXG4gICAgICAgICAgPFRleHRFZGl0b3IgZGVmYXVsdFZhbHVlPXtkZXNjcmlwdGlvbn0gLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XHJcbiAgICAgICAge3QoJ25vdGUnLCB7IGV2ZW50OiBnbG9iYWxUKCdldmVudF9vbmUnKSB9KX1cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXktMi41XCI+XHJcbiAgICAgICAgPEZvcm1pa1xyXG4gICAgICAgICAgaW5pdGlhbFZhbHVlcz17e319XHJcbiAgICAgICAgICB2YWxpZGF0ZT17KHZhbHVlcykgPT4ge319XHJcbiAgICAgICAgICBvblN1Ym1pdD17YXN5bmMgKHZhbHVlczogYW55LCB7IHNldFN1Ym1pdHRpbmcsIHJlc2V0Rm9ybSB9KSA9PiB7XHJcbiAgICAgICAgICAgIG9uVmVyaWZ5KHtcclxuICAgICAgICAgICAgICBxdWVyeVBhcmFtczpcclxuICAgICAgICAgICAgICAgIHZhbHVlcy5xdWVyeVBhcmFtcyAmJiBwYXJ0aWNpcGF0aW9uTWFwcGVyKHZhbHVlcy5xdWVyeVBhcmFtcyksXHJcbiAgICAgICAgICAgICAgaGVhZGVyczogdmFsdWVzLmhlYWRlcnMgJiYgcGFydGljaXBhdGlvbk1hcHBlcih2YWx1ZXMuaGVhZGVycyksXHJcbiAgICAgICAgICAgICAgYm9keVBhcmFtczpcclxuICAgICAgICAgICAgICAgIHZhbHVlcy5ib2R5UGFyYW1zICYmIHBhcnRpY2lwYXRpb25NYXBwZXIodmFsdWVzLmJvZHlQYXJhbXMpLFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgeyh7XHJcbiAgICAgICAgICAgIHZhbHVlcyxcclxuICAgICAgICAgICAgZXJyb3JzLFxyXG4gICAgICAgICAgICB0b3VjaGVkLFxyXG4gICAgICAgICAgICBoYW5kbGVDaGFuZ2UsXHJcbiAgICAgICAgICAgIGhhbmRsZUJsdXIsXHJcbiAgICAgICAgICAgIGhhbmRsZVN1Ym1pdCxcclxuICAgICAgICAgICAgaXNTdWJtaXR0aW5nLFxyXG4gICAgICAgICAgICBkaXJ0eSxcclxuICAgICAgICAgICAgaXNWYWxpZCxcclxuICAgICAgICAgIH0pID0+IChcclxuICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgICAgICAgICAgeyF2ZXJpZmllZCAmJlxyXG4gICAgICAgICAgICAgICAgISFxdWVyeVBhcmFtcz8ubGVuZ3RoICYmXHJcbiAgICAgICAgICAgICAgICBxdWVyeVBhcmFtc1xyXG4gICAgICAgICAgICAgICAgICAuZmlsdGVyKHNlY3VyZUZpZWxkRmlsdGVyKVxyXG4gICAgICAgICAgICAgICAgICAubWFwKChxdWVyeSwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAga2V5PXtgcXVlcnlQYXJhbXMuJHtpbmRleH0uJHtxdWVyeT8ua2V5fWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBpZD17YHF1ZXJ5UGFyYW1zLiR7aW5kZXh9LiR7cXVlcnk/LmtleX1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgbmFtZT17YHF1ZXJ5UGFyYW1zLiR7aW5kZXh9LiR7cXVlcnk/LmtleX1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e3F1ZXJ5Py5rZXkgfHwgJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRNYXJrXHJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17YEVudGVyIHlvdXIgJHtxdWVyeT8ua2V5fWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25CbHVyPXtoYW5kbGVCbHVyfVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIHshdmVyaWZpZWQgJiZcclxuICAgICAgICAgICAgICAgICEhYm9keVBhcmFtcz8ubGVuZ3RoICYmXHJcbiAgICAgICAgICAgICAgICBib2R5UGFyYW1zXHJcbiAgICAgICAgICAgICAgICAgIC5maWx0ZXIoc2VjdXJlRmllbGRGaWx0ZXIpXHJcbiAgICAgICAgICAgICAgICAgIC5tYXAoKHF1ZXJ5LCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2Bib2R5UGFyYW1zLiR7aW5kZXh9LiR7cXVlcnk/LmtleX1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgaWQ9e2Bib2R5UGFyYW1zLiR7aW5kZXh9LiR7cXVlcnk/LmtleX1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e3F1ZXJ5Py5rZXkgfHwgJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRNYXJrXHJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17YEVudGVyIHlvdXIgJHtxdWVyeT8ua2V5fWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25CbHVyPXtoYW5kbGVCbHVyfVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIHshdmVyaWZpZWQgJiZcclxuICAgICAgICAgICAgICAgICEhaGVhZGVycz8ubGVuZ3RoICYmXHJcbiAgICAgICAgICAgICAgICBoZWFkZXJzXHJcbiAgICAgICAgICAgICAgICAgIC5maWx0ZXIoc2VjdXJlRmllbGRGaWx0ZXIpXHJcbiAgICAgICAgICAgICAgICAgIC5tYXAoKHF1ZXJ5LCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2BoZWFkZXJzLiR7aW5kZXh9LiR7cXVlcnk/LmtleX1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgaWQ9e2BoZWFkZXJzLiR7aW5kZXh9LiR7cXVlcnk/LmtleX1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e3F1ZXJ5Py5rZXkgfHwgJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRNYXJrXHJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17YEVudGVyIHlvdXIgJHtxdWVyeT8ua2V5fWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25CbHVyPXtoYW5kbGVCbHVyfVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDxDb2xsYXBzaWJsZUluZm8gaW5pdGlhbFN0YXRlPXtmYWxzZX0+XHJcbiAgICAgICAgICAgICAgICA8YXJ0aWNsZSBjbGFzc05hbWU9XCJwcm9zZSBwcm9zZS1zbGF0ZSBwcm9zZS1zbSBtYi01XCI+XHJcbiAgICAgICAgICAgICAgICAgIFZhbGlkYXRpb25cclxuICAgICAgICAgICAgICAgICAgPHByZSBjbGFzc05hbWU9XCJtdC0wXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGNvZGUgY2xhc3NOYW1lPVwibGFuZ3VhZ2UtanNcIj57dmFsaWRhdGlvbn08L2NvZGU+XHJcbiAgICAgICAgICAgICAgICAgIDwvcHJlPlxyXG4gICAgICAgICAgICAgICAgPC9hcnRpY2xlPlxyXG4gICAgICAgICAgICAgIDwvQ29sbGFwc2libGVJbmZvPlxyXG4gICAgICAgICAgICAgIHt2ZXJpZmllZCA/IChcclxuICAgICAgICAgICAgICAgIDxUYXNrQ29tcGxldGVkQ2FyZFxyXG4gICAgICAgICAgICAgICAgICBwb2ludHM9e3Njb3JlZFBvaW50c31cclxuICAgICAgICAgICAgICAgICAgZnJlcXVlbmN5PXt0YXNrLmZyZXF1ZW5jeX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIHZlcmlmeUJ1dHRvbihhdExlYXN0T25lID8gIShpc1ZhbGlkICYmIGRpcnR5KSA6IGZhbHNlKVxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZm9ybT5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9Gb3JtaWs+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9GcmFnbWVudD5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBCYXNlUmVzdEJvZHk7XHJcbiJdLCJuYW1lcyI6WyJJbnB1dEZpZWxkIiwiVGV4dEVkaXRvciIsIkZvcm1payIsIkZyYWdtZW50IiwiQ29sbGFwc2libGVJbmZvIiwiVGFza0NvbXBsZXRlZENhcmQiLCJ1c2VUcmFuc2xhdGlvbiIsIkJhc2VSZXN0Qm9keSIsInNjb3JlZFBvaW50cyIsInRhc2siLCJ2ZXJpZmllZCIsInZlcmlmeUJ1dHRvbiIsIm9uVmVyaWZ5IiwiZGVzY3JpcHRpb24iLCJ2YWxpZGF0aW9uIiwicXVlcnlQYXJhbXMiLCJoZWFkZXJzIiwiYm9keVBhcmFtcyIsImluZm8iLCJwYXJ0aWNpcGF0aW9uTWFwcGVyIiwidmFsdWVzIiwibWFwIiwiaXRlbSIsImtleSIsIk9iamVjdCIsImtleXMiLCJ2YWx1ZSIsInNlY3VyZUZpZWxkRmlsdGVyIiwicGFyYW0iLCJhdExlYXN0T25lIiwiZmlsdGVyIiwibGVuZ3RoIiwidCIsImtleVByZWZpeCIsImdsb2JhbFQiLCJkaXYiLCJjbGFzc05hbWUiLCJkZWZhdWx0VmFsdWUiLCJldmVudCIsImluaXRpYWxWYWx1ZXMiLCJ2YWxpZGF0ZSIsIm9uU3VibWl0Iiwic2V0U3VibWl0dGluZyIsInJlc2V0Rm9ybSIsImVycm9ycyIsInRvdWNoZWQiLCJoYW5kbGVDaGFuZ2UiLCJoYW5kbGVCbHVyIiwiaGFuZGxlU3VibWl0IiwiaXNTdWJtaXR0aW5nIiwiZGlydHkiLCJpc1ZhbGlkIiwiZm9ybSIsInF1ZXJ5IiwiaW5kZXgiLCJpZCIsIm5hbWUiLCJsYWJlbCIsInJlcXVpcmVkIiwicmVxdWlyZWRNYXJrIiwicGxhY2Vob2xkZXIiLCJvbkNoYW5nZSIsIm9uQmx1ciIsImluaXRpYWxTdGF0ZSIsImFydGljbGUiLCJwcmUiLCJjb2RlIiwicG9pbnRzIiwiZnJlcXVlbmN5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Tasks/Rest/BaseRestBody.tsx\n");

/***/ }),

/***/ "./components/Tasks/Rest/RestRawBody.tsx":
/*!***********************************************!*\
  !*** ./components/Tasks/Rest/RestRawBody.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Button */ \"./components/Button.tsx\");\n/* harmony import */ var _components_TaskToaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/TaskToaster */ \"./components/Tasks/components/TaskToaster.tsx\");\n/* harmony import */ var _BaseRestBody__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BaseRestBody */ \"./components/Tasks/Rest/BaseRestBody.tsx\");\n/* harmony import */ var _rest_gql__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./rest.gql */ \"./components/Tasks/Rest/rest.gql.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Button__WEBPACK_IMPORTED_MODULE_1__, _components_TaskToaster__WEBPACK_IMPORTED_MODULE_2__, _BaseRestBody__WEBPACK_IMPORTED_MODULE_3__, _rest_gql__WEBPACK_IMPORTED_MODULE_4__]);\n([_Components_Button__WEBPACK_IMPORTED_MODULE_1__, _components_TaskToaster__WEBPACK_IMPORTED_MODULE_2__, _BaseRestBody__WEBPACK_IMPORTED_MODULE_3__, _rest_gql__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction RestRawBody({ projectEventId, task, verified, scoredPoints, onSuccess, onError }) {\n    const { id } = task;\n    const [participateRestRaw, { loading: verifying }] = (0,_rest_gql__WEBPACK_IMPORTED_MODULE_4__.useParticipateRestRaw)();\n    const verifyButton = (isDisabled)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            disabled: isDisabled,\n            loading: verifying,\n            block: true,\n            size: \"large\",\n            rounded: \"full\",\n            type: \"submit\",\n            children: \"Submit\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\RestRawBody.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    };\n    const onVerify = (data)=>{\n        participateRestRaw({\n            variables: {\n                eventId: projectEventId,\n                taskId: id,\n                data: {\n                    ...data\n                }\n            },\n            context: {\n                points: task.points\n            },\n            onCompleted: ()=>{\n                onSuccess?.();\n            },\n            onError: (error)=>{\n                (0,_components_TaskToaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                    title: \"Verification Failed\",\n                    defaultText: `We were not able to verify that you have performed the action. Please contact the event host if you have performed this action.`,\n                    type: \"error\",\n                    error\n                });\n                onError?.();\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseRestBody__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        scoredPoints: scoredPoints,\n        verified: verified,\n        task: task,\n        onVerify: onVerify,\n        verifyButton: verifyButton\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Rest\\\\RestRawBody.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RestRawBody);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Rest/RestRawBody.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/CollapsibleInfo.tsx":
/*!*********************************************************!*\
  !*** ./components/Tasks/components/CollapsibleInfo.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollapsibleInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @heroicons/react/solid */ \"@heroicons/react/solid\");\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction CollapsibleInfo({ children, initialState }) {\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialState);\n    if (!children) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"py-2.5 bg-transparent text-cl font-medium text-xs leading-tight uppercase rounded focus:outline-none focus:ring-0 transition duration-150 ease-in-out\",\n                type: \"button\",\n                onClick: ()=>setVisible(!visible),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: \"Advanced Information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CollapsibleInfo.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        \" \",\n                        visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1__.ChevronUpIcon, {\n                            className: \"h-4 ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CollapsibleInfo.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1__.ChevronDownIcon, {\n                            className: \"h-4 ml-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CollapsibleInfo.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CollapsibleInfo.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CollapsibleInfo.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            visible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CollapsibleInfo.tsx\",\n                lineNumber: 29,\n                columnNumber: 19\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CollapsibleInfo.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/CollapsibleInfo.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/CountDownTimer.tsx":
/*!********************************************************!*\
  !*** ./components/Tasks/components/CountDownTimer.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountdownTimer: () => (/* binding */ CountdownTimer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"moment\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction CountdownTimer({ target }) {\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let intervalId = setInterval(()=>{\n            const now = moment__WEBPACK_IMPORTED_MODULE_2___default()();\n            const duration = moment__WEBPACK_IMPORTED_MODULE_2___default().duration(target.diff(now));\n            const days = Math.floor(duration.asDays());\n            const hours = Math.floor(duration.hours());\n            const minutes = Math.floor(duration.minutes());\n            const seconds = Math.floor(duration.seconds());\n            setTimeLeft(`${days ? days.toString() + \" days \" : \"\"}${hours.toString().padStart(2, \"0\")}:${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`);\n        }, 1000);\n        return ()=>clearInterval(intervalId);\n    }, [\n        target\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: timeLeft\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CountDownTimer.tsx\",\n        lineNumber: 29,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/CountDownTimer.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/TaskCompletedCard.tsx":
/*!***********************************************************!*\
  !*** ./components/Tasks/components/TaskCompletedCard.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/helpers/frequency */ \"./helpers/frequency.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _CountDownTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CountDownTimer */ \"./components/Tasks/components/CountDownTimer.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nconst TaskCompletedCard = ({ points, title, subTitle, frequency, status })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"tasks.completedCard\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const getBackground = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return \"linear-gradient(90deg, #c2410c 0%, #c2640c 50%, #c2800c 100%)\";\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return \"linear-gradient(90deg, #f16363 0%, #f65c5c 50%, #ef4646 100%)\";\n            default:\n                return \"\";\n        }\n    };\n    const getIcon = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Warning, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    function getTitle(title, points, globalT) {\n        if (title) {\n            return title;\n        }\n        if (points) {\n            if ((0,_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__.isFeatureEnabled)(\"POINTS\")) {\n                return `${t(\"title.points\", {\n                    points: points,\n                    projectPoints: globalT(\"projectPoints\")\n                })}`;\n            }\n            return `${t(\"title.noPoints\")}`;\n        }\n        return `${t(\"title.noPoints\")}`;\n    }\n    const getSubTitle = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n                return `${t(\"subtitle.inReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return `${t(\"subtitle.inAIReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return `${t(\"subtitle.valid\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return `${t(\"subtitle.invalid\")}`;\n            default:\n                return `${t(\"subtitle.valid\")}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col p-4 py-6 bg-primary rounded-xl relative overflow-hidden gradient-primary text-primary-foreground shadow\",\n        style: {\n            background: getBackground()\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg  mb-0\",\n                                children: getTitle(title, points, globalT)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: frequency && frequency !== _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.Frequency.NONE ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        \"Resets in \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountDownTimer__WEBPACK_IMPORTED_MODULE_4__.CountdownTimer, {\n                                                target: _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__.frequencyConfig[frequency].cutOff()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : subTitle ? subTitle : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: getSubTitle()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskCompletedCard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/TaskCompletedCard.tsx\n");

/***/ }),

/***/ "./components/TextEditor.tsx":
/*!***********************************!*\
  !*** ./components/TextEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! draft-js */ \"draft-js\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(draft_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! draft-js/dist/Draft.css */ \"./node_modules/draft-js/dist/Draft.css\");\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction TextEditor({ readonly = true, defaultValue, expandable = false, maxHeight = 100, className }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n    const [isOverflow, setIsOverflow] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        const container = ref.current;\n        if (!container || !setIsOverflow) return;\n        if (expandable && container.offsetHeight >= maxHeight) {\n            setIsOverflow(true);\n        }\n    }, [\n        ref,\n        setIsOverflow\n    ]);\n    function Link(props) {\n        const { url } = props.contentState.getEntity(props.entityKey).getData();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: url,\n            target: \"_blank\",\n            rel: \"noreferrer nofollow\",\n            referrerPolicy: \"no-referrer\",\n            style: {\n                color: currentTheme === \"dark\" ? \"#93cefe\" : \"#3b5998\",\n                textDecoration: \"underline\"\n            },\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    function findLinkEntities(contentBlock, callback, contentState) {\n        contentBlock.findEntityRanges((character)=>{\n            const entityKey = character.getEntity();\n            return entityKey !== null && contentState.getEntity(entityKey).getType() === \"LINK\";\n        }, callback);\n    }\n    const decorator = new draft_js__WEBPACK_IMPORTED_MODULE_3__.CompositeDecorator([\n        {\n            strategy: findLinkEntities,\n            component: Link\n        }\n    ]);\n    let editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createEmpty(decorator);\n    if (!defaultValue) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    if (defaultValue) {\n        try {\n            const parsedJson = JSON.parse(defaultValue);\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createWithContent((0,draft_js__WEBPACK_IMPORTED_MODULE_3__.convertFromRaw)(parsedJson), decorator);\n        } catch (err) {\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createWithContent(draft_js__WEBPACK_IMPORTED_MODULE_3__.ContentState.createFromText(defaultValue), decorator);\n        }\n    }\n    if (!editorState.getCurrentContent().hasText()) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            isOverflow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 z-10 h-[40px] w-full bg-gradient-to-t from-background/60 to-background/0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute z-20 flex items-center w-full justify-center -bottom-3`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-2 z-10 rounded-full border bg-background/90 text-cs text-sm font-extrabold\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.CaretUp, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.CaretDown, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`text-base text-ch leading-normal overflow-hidden`, className, isExpanded ? \"mb-10\" : \"\"),\n                ref: ref,\n                style: expandable && !isExpanded ? {\n                    maxHeight,\n                    marginBottom: 0\n                } : {},\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(draft_js__WEBPACK_IMPORTED_MODULE_3__.Editor, {\n                    editorState: editorState,\n                    readOnly: readonly,\n                    onChange: ()=>{}\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1RleHRFZGl0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVDO0FBQ29CO0FBT3pDO0FBQ2U7QUFDTTtBQUNhO0FBRXJDLFNBQVNZLFdBQVcsRUFDakNDLFdBQVcsSUFBSSxFQUNmQyxZQUFZLEVBQ1pDLGFBQWEsS0FBSyxFQUNsQkMsWUFBWSxHQUFHLEVBQ2ZDLFNBQVMsRUFPVjtJQUNDLE1BQU1DLE1BQU1SLDZDQUFNQSxDQUFNO0lBQ3hCLE1BQU0sQ0FBQ1MsWUFBWUMsY0FBYyxHQUFHVCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNVLFlBQVlDLGNBQWMsR0FBR1gsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxFQUFFWSxLQUFLLEVBQUVDLFdBQVcsRUFBRSxHQUFHaEIscURBQVFBO0lBQ3ZDLE1BQU1pQixlQUFlRixVQUFVLFdBQVdDLGNBQWNEO0lBRXhEZCxnREFBU0EsQ0FBQztRQUNSLE1BQU1pQixZQUFZUixJQUFJUyxPQUFPO1FBRTdCLElBQUksQ0FBQ0QsYUFBYSxDQUFDTixlQUFlO1FBRWxDLElBQUlMLGNBQWNXLFVBQVVFLFlBQVksSUFBSVosV0FBVztZQUNyREksY0FBYztRQUNoQjtJQUNGLEdBQUc7UUFBQ0Y7UUFBS0U7S0FBYztJQUV2QixTQUFTUyxLQUFLQyxLQUFVO1FBQ3RCLE1BQU0sRUFBRUMsR0FBRyxFQUFFLEdBQUdELE1BQU1FLFlBQVksQ0FBQ0MsU0FBUyxDQUFDSCxNQUFNSSxTQUFTLEVBQUVDLE9BQU87UUFDckUscUJBQ0UsOERBQUNDO1lBQ0NDLE1BQU1OO1lBQ05PLFFBQU87WUFDUEMsS0FBSTtZQUNKQyxnQkFBZTtZQUNmQyxPQUFPO2dCQUNMQyxPQUFPakIsaUJBQWlCLFNBQVMsWUFBWTtnQkFDN0NrQixnQkFBZ0I7WUFDbEI7c0JBRUNiLE1BQU1jLFFBQVE7Ozs7OztJQUdyQjtJQUNBLFNBQVNDLGlCQUNQQyxZQUFpQixFQUNqQkMsUUFBYSxFQUNiZixZQUFpQjtRQUVqQmMsYUFBYUUsZ0JBQWdCLENBQUMsQ0FBQ0M7WUFDN0IsTUFBTWYsWUFBWWUsVUFBVWhCLFNBQVM7WUFDckMsT0FDRUMsY0FBYyxRQUNkRixhQUFhQyxTQUFTLENBQUNDLFdBQVdnQixPQUFPLE9BQU87UUFFcEQsR0FBR0g7SUFDTDtJQUVBLE1BQU1JLFlBQVksSUFBSWhELHdEQUFrQkEsQ0FBQztRQUN2QztZQUNFaUQsVUFBVVA7WUFDVlEsV0FBV3hCO1FBQ2I7S0FDRDtJQUNELElBQUl5QixjQUFjL0MsaURBQVdBLENBQUNnRCxXQUFXLENBQUNKO0lBRTFDLElBQUksQ0FBQ3JDLGNBQWMscUJBQU87SUFFMUIsSUFBSUEsY0FBYztRQUNoQixJQUFJO1lBQ0YsTUFBTTBDLGFBQWFDLEtBQUtDLEtBQUssQ0FBQzVDO1lBQzlCd0MsY0FBYy9DLGlEQUFXQSxDQUFDb0QsaUJBQWlCLENBQ3pDdEQsd0RBQWNBLENBQUNtRCxhQUNmTDtRQUVKLEVBQUUsT0FBT1MsS0FBSztZQUNaTixjQUFjL0MsaURBQVdBLENBQUNvRCxpQkFBaUIsQ0FDekN2RCxrREFBWUEsQ0FBQ3lELGNBQWMsQ0FBQy9DLGVBQzVCcUM7UUFFSjtJQUNGO0lBRUEsSUFBSSxDQUFDRyxZQUFZUSxpQkFBaUIsR0FBR0MsT0FBTyxJQUFJLHFCQUFPO0lBRXZELHFCQUNFLDhEQUFDQztRQUFJL0MsV0FBVTs7WUFDWkUsNEJBQ0M7O29CQUNHLENBQUNFLDRCQUNBLDhEQUFDMkM7d0JBQUkvQyxXQUFVOzs7Ozs7a0NBRWpCLDhEQUFDK0M7d0JBQ0MvQyxXQUFXLENBQUMsK0RBQStELENBQUM7a0NBRTVFLDRFQUFDZ0Q7NEJBQ0NDLFNBQVMsSUFBTTVDLGNBQWMsQ0FBQ0Q7NEJBQzlCSixXQUFVO3NDQUVUSSwyQkFDQyw4REFBQ25CLDBEQUFPQTtnQ0FBQ2lFLE1BQU07Z0NBQUlDLFFBQU87Ozs7O3FEQUUxQiw4REFBQ25FLDREQUFTQTtnQ0FBQ2tFLE1BQU07Z0NBQUlDLFFBQU87Ozs7Ozs7Ozs7Ozs7Ozs7b0JBRzNCOzs7MEJBSVgsOERBQUNKO2dCQUNDL0MsV0FBV2pCLHFEQUFFQSxDQUNYLENBQUMsZ0RBQWdELENBQUMsRUFDbERpQixXQUNBSSxhQUFhLFVBQVU7Z0JBRXpCSCxLQUFLQTtnQkFDTHVCLE9BQ0UxQixjQUFjLENBQUNNLGFBQ1g7b0JBQ0VMO29CQUNBcUQsY0FBYztnQkFDaEIsSUFDQSxDQUFDOzBCQUdQLDRFQUFDL0QsNENBQU1BO29CQUNMZ0QsYUFBYUE7b0JBQ2JnQixVQUFVekQ7b0JBQ1YwRCxVQUFVLEtBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9UZXh0RWRpdG9yLnRzeD9lYjkzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNuIH0gZnJvbSAnQFJvb3QvdXRpbHMvdXRpbHMnO1xyXG5pbXBvcnQgeyBDYXJldERvd24sIENhcmV0VXAgfSBmcm9tICdAcGhvc3Bob3ItaWNvbnMvcmVhY3QnO1xyXG5pbXBvcnQge1xyXG4gIENvbXBvc2l0ZURlY29yYXRvcixcclxuICBDb250ZW50U3RhdGUsXHJcbiAgY29udmVydEZyb21SYXcsXHJcbiAgRWRpdG9yLFxyXG4gIEVkaXRvclN0YXRlLFxyXG59IGZyb20gJ2RyYWZ0LWpzJztcclxuaW1wb3J0ICdkcmFmdC1qcy9kaXN0L0RyYWZ0LmNzcyc7XHJcbmltcG9ydCB7IHVzZVRoZW1lIH0gZnJvbSAnbmV4dC10aGVtZXMnO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUZXh0RWRpdG9yKHtcclxuICByZWFkb25seSA9IHRydWUsXHJcbiAgZGVmYXVsdFZhbHVlLFxyXG4gIGV4cGFuZGFibGUgPSBmYWxzZSxcclxuICBtYXhIZWlnaHQgPSAxMDAsXHJcbiAgY2xhc3NOYW1lLFxyXG59OiB7XHJcbiAgZGVmYXVsdFZhbHVlOiBzdHJpbmc7XHJcbiAgcmVhZG9ubHk/OiBib29sZWFuO1xyXG4gIGV4cGFuZGFibGU/OiBib29sZWFuO1xyXG4gIG1heEhlaWdodD86IG51bWJlcjtcclxuICBjbGFzc05hbWU/OiBzdHJpbmc7XHJcbn0pIHtcclxuICBjb25zdCByZWYgPSB1c2VSZWY8YW55PihudWxsKTtcclxuICBjb25zdCBbaXNPdmVyZmxvdywgc2V0SXNPdmVyZmxvd10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzRXhwYW5kZWQsIHNldElzRXhwYW5kZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IHsgdGhlbWUsIHN5c3RlbVRoZW1lIH0gPSB1c2VUaGVtZSgpO1xyXG4gIGNvbnN0IGN1cnJlbnRUaGVtZSA9IHRoZW1lID09PSAnc3lzdGVtJyA/IHN5c3RlbVRoZW1lIDogdGhlbWU7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBjb250YWluZXIgPSByZWYuY3VycmVudDtcclxuXHJcbiAgICBpZiAoIWNvbnRhaW5lciB8fCAhc2V0SXNPdmVyZmxvdykgcmV0dXJuO1xyXG5cclxuICAgIGlmIChleHBhbmRhYmxlICYmIGNvbnRhaW5lci5vZmZzZXRIZWlnaHQgPj0gbWF4SGVpZ2h0KSB7XHJcbiAgICAgIHNldElzT3ZlcmZsb3codHJ1ZSk7XHJcbiAgICB9XHJcbiAgfSwgW3JlZiwgc2V0SXNPdmVyZmxvd10pO1xyXG5cclxuICBmdW5jdGlvbiBMaW5rKHByb3BzOiBhbnkpIHtcclxuICAgIGNvbnN0IHsgdXJsIH0gPSBwcm9wcy5jb250ZW50U3RhdGUuZ2V0RW50aXR5KHByb3BzLmVudGl0eUtleSkuZ2V0RGF0YSgpO1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGFcclxuICAgICAgICBocmVmPXt1cmx9XHJcbiAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcclxuICAgICAgICByZWw9XCJub3JlZmVycmVyIG5vZm9sbG93XCJcclxuICAgICAgICByZWZlcnJlclBvbGljeT1cIm5vLXJlZmVycmVyXCJcclxuICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgY29sb3I6IGN1cnJlbnRUaGVtZSA9PT0gJ2RhcmsnID8gJyM5M2NlZmUnIDogJyMzYjU5OTgnLFxyXG4gICAgICAgICAgdGV4dERlY29yYXRpb246ICd1bmRlcmxpbmUnLFxyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICB7cHJvcHMuY2hpbGRyZW59XHJcbiAgICAgIDwvYT5cclxuICAgICk7XHJcbiAgfVxyXG4gIGZ1bmN0aW9uIGZpbmRMaW5rRW50aXRpZXMoXHJcbiAgICBjb250ZW50QmxvY2s6IGFueSxcclxuICAgIGNhbGxiYWNrOiBhbnksXHJcbiAgICBjb250ZW50U3RhdGU6IGFueSxcclxuICApIHtcclxuICAgIGNvbnRlbnRCbG9jay5maW5kRW50aXR5UmFuZ2VzKChjaGFyYWN0ZXI6IGFueSkgPT4ge1xyXG4gICAgICBjb25zdCBlbnRpdHlLZXkgPSBjaGFyYWN0ZXIuZ2V0RW50aXR5KCk7XHJcbiAgICAgIHJldHVybiAoXHJcbiAgICAgICAgZW50aXR5S2V5ICE9PSBudWxsICYmXHJcbiAgICAgICAgY29udGVudFN0YXRlLmdldEVudGl0eShlbnRpdHlLZXkpLmdldFR5cGUoKSA9PT0gJ0xJTksnXHJcbiAgICAgICk7XHJcbiAgICB9LCBjYWxsYmFjayk7XHJcbiAgfVxyXG5cclxuICBjb25zdCBkZWNvcmF0b3IgPSBuZXcgQ29tcG9zaXRlRGVjb3JhdG9yKFtcclxuICAgIHtcclxuICAgICAgc3RyYXRlZ3k6IGZpbmRMaW5rRW50aXRpZXMsXHJcbiAgICAgIGNvbXBvbmVudDogTGluayxcclxuICAgIH0sXHJcbiAgXSk7XHJcbiAgbGV0IGVkaXRvclN0YXRlID0gRWRpdG9yU3RhdGUuY3JlYXRlRW1wdHkoZGVjb3JhdG9yKTtcclxuXHJcbiAgaWYgKCFkZWZhdWx0VmFsdWUpIHJldHVybiA8PjwvPjtcclxuXHJcbiAgaWYgKGRlZmF1bHRWYWx1ZSkge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcGFyc2VkSnNvbiA9IEpTT04ucGFyc2UoZGVmYXVsdFZhbHVlKTtcclxuICAgICAgZWRpdG9yU3RhdGUgPSBFZGl0b3JTdGF0ZS5jcmVhdGVXaXRoQ29udGVudChcclxuICAgICAgICBjb252ZXJ0RnJvbVJhdyhwYXJzZWRKc29uKSxcclxuICAgICAgICBkZWNvcmF0b3IsXHJcbiAgICAgICk7XHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgZWRpdG9yU3RhdGUgPSBFZGl0b3JTdGF0ZS5jcmVhdGVXaXRoQ29udGVudChcclxuICAgICAgICBDb250ZW50U3RhdGUuY3JlYXRlRnJvbVRleHQoZGVmYXVsdFZhbHVlKSxcclxuICAgICAgICBkZWNvcmF0b3IsXHJcbiAgICAgICk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBpZiAoIWVkaXRvclN0YXRlLmdldEN1cnJlbnRDb250ZW50KCkuaGFzVGV4dCgpKSByZXR1cm4gPD48Lz47XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgIHtpc092ZXJmbG93ICYmIChcclxuICAgICAgICA8PlxyXG4gICAgICAgICAgeyFpc0V4cGFuZGVkICYmIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMCB6LTEwIGgtWzQwcHhdIHctZnVsbCBiZy1ncmFkaWVudC10by10IGZyb20tYmFja2dyb3VuZC82MCB0by1iYWNrZ3JvdW5kLzBcIj48L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGFic29sdXRlIHotMjAgZmxleCBpdGVtcy1jZW50ZXIgdy1mdWxsIGp1c3RpZnktY2VudGVyIC1ib3R0b20tM2B9XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0V4cGFuZGVkKCFpc0V4cGFuZGVkKX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgei0xMCByb3VuZGVkLWZ1bGwgYm9yZGVyIGJnLWJhY2tncm91bmQvOTAgdGV4dC1jcyB0ZXh0LXNtIGZvbnQtZXh0cmFib2xkXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHtpc0V4cGFuZGVkID8gKFxyXG4gICAgICAgICAgICAgICAgPENhcmV0VXAgc2l6ZT17MTZ9IHdlaWdodD1cImJvbGRcIiAvPlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8Q2FyZXREb3duIHNpemU9ezE2fSB3ZWlnaHQ9XCJib2xkXCIgLz5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PnsnICd9XHJcbiAgICAgICAgPC8+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICA8ZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgIGB0ZXh0LWJhc2UgdGV4dC1jaCBsZWFkaW5nLW5vcm1hbCBvdmVyZmxvdy1oaWRkZW5gLFxyXG4gICAgICAgICAgY2xhc3NOYW1lLFxyXG4gICAgICAgICAgaXNFeHBhbmRlZCA/ICdtYi0xMCcgOiAnJyxcclxuICAgICAgICApfVxyXG4gICAgICAgIHJlZj17cmVmfVxyXG4gICAgICAgIHN0eWxlPXtcclxuICAgICAgICAgIGV4cGFuZGFibGUgJiYgIWlzRXhwYW5kZWRcclxuICAgICAgICAgICAgPyB7XHJcbiAgICAgICAgICAgICAgICBtYXhIZWlnaHQsXHJcbiAgICAgICAgICAgICAgICBtYXJnaW5Cb3R0b206IDAsXHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICA6IHt9XHJcbiAgICAgICAgfVxyXG4gICAgICA+XHJcbiAgICAgICAgPEVkaXRvclxyXG4gICAgICAgICAgZWRpdG9yU3RhdGU9e2VkaXRvclN0YXRlfVxyXG4gICAgICAgICAgcmVhZE9ubHk9e3JlYWRvbmx5fVxyXG4gICAgICAgICAgb25DaGFuZ2U9eygpID0+IHt9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiY24iLCJDYXJldERvd24iLCJDYXJldFVwIiwiQ29tcG9zaXRlRGVjb3JhdG9yIiwiQ29udGVudFN0YXRlIiwiY29udmVydEZyb21SYXciLCJFZGl0b3IiLCJFZGl0b3JTdGF0ZSIsInVzZVRoZW1lIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlU3RhdGUiLCJUZXh0RWRpdG9yIiwicmVhZG9ubHkiLCJkZWZhdWx0VmFsdWUiLCJleHBhbmRhYmxlIiwibWF4SGVpZ2h0IiwiY2xhc3NOYW1lIiwicmVmIiwiaXNPdmVyZmxvdyIsInNldElzT3ZlcmZsb3ciLCJpc0V4cGFuZGVkIiwic2V0SXNFeHBhbmRlZCIsInRoZW1lIiwic3lzdGVtVGhlbWUiLCJjdXJyZW50VGhlbWUiLCJjb250YWluZXIiLCJjdXJyZW50Iiwib2Zmc2V0SGVpZ2h0IiwiTGluayIsInByb3BzIiwidXJsIiwiY29udGVudFN0YXRlIiwiZ2V0RW50aXR5IiwiZW50aXR5S2V5IiwiZ2V0RGF0YSIsImEiLCJocmVmIiwidGFyZ2V0IiwicmVsIiwicmVmZXJyZXJQb2xpY3kiLCJzdHlsZSIsImNvbG9yIiwidGV4dERlY29yYXRpb24iLCJjaGlsZHJlbiIsImZpbmRMaW5rRW50aXRpZXMiLCJjb250ZW50QmxvY2siLCJjYWxsYmFjayIsImZpbmRFbnRpdHlSYW5nZXMiLCJjaGFyYWN0ZXIiLCJnZXRUeXBlIiwiZGVjb3JhdG9yIiwic3RyYXRlZ3kiLCJjb21wb25lbnQiLCJlZGl0b3JTdGF0ZSIsImNyZWF0ZUVtcHR5IiwicGFyc2VkSnNvbiIsIkpTT04iLCJwYXJzZSIsImNyZWF0ZVdpdGhDb250ZW50IiwiZXJyIiwiY3JlYXRlRnJvbVRleHQiLCJnZXRDdXJyZW50Q29udGVudCIsImhhc1RleHQiLCJkaXYiLCJidXR0b24iLCJvbkNsaWNrIiwic2l6ZSIsIndlaWdodCIsIm1hcmdpbkJvdHRvbSIsInJlYWRPbmx5Iiwib25DaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/TextEditor.tsx\n");

/***/ })

};
;
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Tasks_Quiz_QuizPlayTile_tsx"],{

/***/ "./components/Loaders/SingleDot.tsx":
/*!******************************************!*\
  !*** ./components/Loaders/SingleDot.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-content-loader */ \"./node_modules/react-content-loader/dist/react-content-loader.es.js\");\n\n\n\nconst SingleDotLoader = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_content_loader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        speed: 2,\n        width: 32,\n        height: 32,\n        viewBox: \"0 0 32 32\",\n        uniqueKey: \"single-dot-loader\",\n        backgroundColor: \"#cdcdcd\",\n        foregroundColor: \"#ddd\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n            cx: \"16\",\n            cy: \"16\",\n            r: \"16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n            lineNumber: 15,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\n_c = SingleDotLoader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SingleDotLoader);\nvar _c;\n$RefreshReg$(_c, \"SingleDotLoader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvU2luZ2xlRG90LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTBCO0FBQ3VCO0FBRWpELE1BQU1FLGtCQUFrQixDQUFDQyxzQkFDdkIsOERBQUNGLDREQUFhQTtRQUNaRyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxTQUFRO1FBQ1JDLFdBQVU7UUFDVkMsaUJBQWdCO1FBQ2hCQyxpQkFBZ0I7UUFDZixHQUFHUCxLQUFLO2tCQUVULDRFQUFDUTtZQUFPQyxJQUFHO1lBQUtDLElBQUc7WUFBS0MsR0FBRTs7Ozs7Ozs7Ozs7S0FYeEJaO0FBZU4sK0RBQWVBLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdC50c3g/YTE5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgQ29udGVudExvYWRlciBmcm9tICdyZWFjdC1jb250ZW50LWxvYWRlcic7XHJcblxyXG5jb25zdCBTaW5nbGVEb3RMb2FkZXIgPSAocHJvcHM6IGFueSkgPT4gKFxyXG4gIDxDb250ZW50TG9hZGVyXHJcbiAgICBzcGVlZD17Mn1cclxuICAgIHdpZHRoPXszMn1cclxuICAgIGhlaWdodD17MzJ9XHJcbiAgICB2aWV3Qm94PVwiMCAwIDMyIDMyXCJcclxuICAgIHVuaXF1ZUtleT1cInNpbmdsZS1kb3QtbG9hZGVyXCJcclxuICAgIGJhY2tncm91bmRDb2xvcj1cIiNjZGNkY2RcIlxyXG4gICAgZm9yZWdyb3VuZENvbG9yPVwiI2RkZFwiXHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgPlxyXG4gICAgPGNpcmNsZSBjeD1cIjE2XCIgY3k9XCIxNlwiIHI9XCIxNlwiIC8+XHJcbiAgPC9Db250ZW50TG9hZGVyPlxyXG4pO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU2luZ2xlRG90TG9hZGVyO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb250ZW50TG9hZGVyIiwiU2luZ2xlRG90TG9hZGVyIiwicHJvcHMiLCJzcGVlZCIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsInVuaXF1ZUtleSIsImJhY2tncm91bmRDb2xvciIsImZvcmVncm91bmRDb2xvciIsImNpcmNsZSIsImN4IiwiY3kiLCJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Loaders/SingleDot.tsx\n"));

/***/ }),

/***/ "./components/RadialProgress/index.tsx":
/*!*********************************************!*\
  !*** ./components/RadialProgress/index.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RadialProgress: function() { return /* binding */ RadialProgress; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst RadialProgress = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c = ()=>__webpack_require__.e(/*! import() */ \"components_RadialProgress_RadialProgress_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @Components/RadialProgress/RadialProgress */ \"./components/RadialProgress/RadialProgress.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\RadialProgress\\\\index.tsx -> \" + \"@Components/RadialProgress/RadialProgress\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            height: 66,\n            width: 66\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RadialProgress\\\\index.tsx\",\n            lineNumber: 8,\n            columnNumber: 20\n        }, undefined)\n});\n_c1 = RadialProgress;\nvar _c, _c1;\n$RefreshReg$(_c, \"RadialProgress$dynamic\");\n$RefreshReg$(_c1, \"RadialProgress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTREO0FBQ3pCO0FBRTVCLE1BQU1FLGlCQUFpQkQsbURBQU9BLE1BQ25DLElBQU0sNE9BQU87Ozs7OztJQUVYRSxLQUFLO0lBQ0xDLFNBQVMsa0JBQU0sOERBQUNKLHFFQUFlQTtZQUFDSyxRQUFRO1lBQUlDLE9BQU87Ozs7OztHQUVyRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL2luZGV4LnRzeD9mZjA5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTaW5nbGVEb3RMb2FkZXIgZnJvbSAnQENvbXBvbmVudHMvTG9hZGVycy9TaW5nbGVEb3QnO1xyXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnO1xyXG5cclxuZXhwb3J0IGNvbnN0IFJhZGlhbFByb2dyZXNzID0gZHluYW1pYyhcclxuICAoKSA9PiBpbXBvcnQoJ0BDb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL1JhZGlhbFByb2dyZXNzJyksXHJcbiAge1xyXG4gICAgc3NyOiBmYWxzZSxcclxuICAgIGxvYWRpbmc6ICgpID0+IDxTaW5nbGVEb3RMb2FkZXIgaGVpZ2h0PXs2Nn0gd2lkdGg9ezY2fSAvPixcclxuICB9LFxyXG4pO1xyXG4iXSwibmFtZXMiOlsiU2luZ2xlRG90TG9hZGVyIiwiZHluYW1pYyIsIlJhZGlhbFByb2dyZXNzIiwic3NyIiwibG9hZGluZyIsImhlaWdodCIsIndpZHRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/RadialProgress/index.tsx\n"));

/***/ }),

/***/ "./components/Tasks/Quiz/QuizPlayTile.tsx":
/*!************************************************!*\
  !*** ./components/Tasks/Quiz/QuizPlayTile.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var _Components_RadialProgress__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/RadialProgress */ \"./components/RadialProgress/index.tsx\");\n/* harmony import */ var _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Hooks/useTaskParticipation */ \"./hooks/useTaskParticipation.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var _components_TaskTile__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/TaskTile */ \"./components/Tasks/components/TaskTile.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst QuizPlayTile = (param)=>{\n    let { projectEventId, eventTask, onClick, locked, lockedReason } = param;\n    _s();\n    const { title, subTaskStats, iconUrl } = eventTask;\n    const { data, loading } = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__.useUserTaskParticipationList)(projectEventId);\n    const completedCount = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__.useChildCompletedCount)(data === null || data === void 0 ? void 0 : data.userTaskParticipation, eventTask === null || eventTask === void 0 ? void 0 : eventTask.id);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TaskTile__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        onClick: onClick,\n        title: title,\n        locked: locked,\n        lockedReason: lockedReason,\n        isVerified: completedCount === subTaskStats.count,\n        points: subTaskStats.totalPoints || 0,\n        xp: subTaskStats.totalXp || 0,\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.PuzzlePiece, {\n            className: \"h-6 w-6 text-green-500\",\n            weight: \"regular\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayTile.tsx\",\n            lineNumber: 35,\n            columnNumber: 13\n        }, void 0),\n        renderExtra: ()=>loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                width: 54,\n                height: 54\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayTile.tsx\",\n                lineNumber: 38,\n                columnNumber: 11\n            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RadialProgress__WEBPACK_IMPORTED_MODULE_2__.RadialProgress, {\n                progress: (completedCount || 0) / (subTaskStats.count || 1) * 100,\n                text: \"\".concat(completedCount || 0, \"/\").concat(subTaskStats.count),\n                circleSize: 54,\n                textClass: \"fill-foreground\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayTile.tsx\",\n                lineNumber: 40,\n                columnNumber: 11\n            }, void 0),\n        iconUrl: iconUrl\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayTile.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QuizPlayTile, \"8SgNFZXggqmZ7K7coaqw+jDzKL4=\", false, function() {\n    return [\n        _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__.useUserTaskParticipationList,\n        _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__.useChildCompletedCount\n    ];\n});\n_c = QuizPlayTile;\n/* harmony default export */ __webpack_exports__[\"default\"] = (QuizPlayTile);\nvar _c;\n$RefreshReg$(_c, \"QuizPlayTile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/QuizPlayTile.tsx\n"));

/***/ })

}]);
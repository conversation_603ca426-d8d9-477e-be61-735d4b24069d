"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Giveaways_GiveawayRendererWrapper_tsx";
exports.ids = ["components_Giveaways_GiveawayRendererWrapper_tsx"];
exports.modules = {

/***/ "./components/Giveaways/GiveawayRendererWrapper.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/GiveawayRendererWrapper.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawayRendererWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Tasks/components/TaskTileLoader */ \"./components/Tasks/components/TaskTileLoader.tsx\");\n/* harmony import */ var _Root_context_Actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/context/Actions */ \"./context/Actions.ts\");\n/* harmony import */ var _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Root/context/AppContext */ \"./context/AppContext.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_GiveawayRenderer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/GiveawayRenderer */ \"./components/Giveaways/components/GiveawayRenderer.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__]);\n_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction GiveawayRendererWrapper({ giveaway, projectEvent, size }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { state: { isAuthenticated }, dispatch } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__.useAppContext)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GiveawayRenderer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        giveawayType: giveaway.giveawayType,\n        distributionType: giveaway.distributionType,\n        renderer: \"summary\",\n        placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                style: {\n                    background: \"transparent\",\n                    border: 0,\n                    padding: 0\n                },\n                loaderProps: {\n                    viewBox: \"0 0 300 32\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n                lineNumber: 30,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n            lineNumber: 29,\n            columnNumber: 9\n        }, void 0),\n        props: {\n            giveaway: giveaway,\n            projectEvent,\n            size,\n            onClick: ()=>{\n                if (!isAuthenticated) {\n                    dispatch({\n                        type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_2__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                        payload: null\n                    });\n                    return;\n                }\n                const newQuery = {\n                    ...router.query,\n                    taskid: \"claim\",\n                    giveaway: giveaway.id\n                };\n                router.push({\n                    pathname: router.pathname,\n                    query: newQuery\n                }, undefined, {\n                    shallow: true\n                });\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/GiveawayRendererWrapper.tsx\n");

/***/ })

};
;
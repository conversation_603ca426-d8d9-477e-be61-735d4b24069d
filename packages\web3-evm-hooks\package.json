{"name": "@airlyft/web3-evm-hooks", "version": "1.0.0", "description": "", "type": "commonjs", "types": "./dist/index.d.ts", "main": "./dist/index.js", "exports": "./dist/index.js", "scripts": {"build": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "files": ["dist/*"], "devDependencies": {"@airlyft/types": "^1.0.0", "@airlyft/web3-evm": "^1.0.0", "react": "link:../account-ui/node_modules/react"}, "peerDependencies": {"@types/react": "^18.2.6", "ethers": "^5.7.2", "react": "^18.2.0"}, "dependencies": {"@ethersproject/providers": "^5.7.2", "@metamask/detect-provider": "^2.0.0", "@polkadot/extension-dapp": "^0.44.8", "@walletconnect/ethereum-provider": "^2.8.2", "@walletconnect/modal": "^2.5.2", "ethers": "^5.7.2", "zustand": "^4.1.4"}}
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Giveaways_Distribution_TokenFCFSRangeDistribution_tsx"],{

/***/ "./components/Giveaways/Distribution/TokenFCFSRangeDistribution.tsx":
/*!**************************************************************************!*\
  !*** ./components/Giveaways/Distribution/TokenFCFSRangeDistribution.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_List__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/List */ \"./components/List/index.tsx\");\n/* harmony import */ var _Components_Paragraph__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Paragraph */ \"./components/Paragraph.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var _components_TokenFCFSUserProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/TokenFCFSUserProgress */ \"./components/Giveaways/components/TokenFCFSUserProgress.tsx\");\n/* harmony import */ var _Root_utils_date_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Root/utils/date-utils */ \"./utils/date-utils.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst TokenFCFSRangeDistribution = (param)=>{\n    let { projectEvent, rules, rewardType, stats, token, showDetails, hasWhitelist, distributionType } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)(\"translation\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const isProjectEnded = new Date() > new Date(projectEvent.endTime);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Paragraph__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Flask, {\n                    weight: \"fill\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSRangeDistribution.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 17\n                }, void 0),\n                title: t(\"giveaway.distribution.title\"),\n                description: hasWhitelist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        t(\"giveaway.distribution.descriptionFCFSRange.hasWhitelist.pre\"),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                            children: t(\"giveaway.distribution.descriptionFCFSRange.hasWhitelist.mid1\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSRangeDistribution.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 17\n                        }, void 0),\n                        t(\"giveaway.distribution.descriptionFCFSRange.hasWhitelist.mid2\"),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                            children: t(\"giveaway.distribution.descriptionFCFSRange.hasWhitelist.mid3\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSRangeDistribution.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 17\n                        }, void 0),\n                        t(\"giveaway.distribution.descriptionFCFSRange.hasWhitelist.post\"),\n                        distributionType == _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.DistributionType.FCFS_RANGE_END && !isProjectEnded ? \"Please check for the results \".concat((0,_Root_utils_date_utils__WEBPACK_IMPORTED_MODULE_5__.formatHumanize)(projectEvent.endTime), \".\") : \"Results announced \".concat((0,_Root_utils_date_utils__WEBPACK_IMPORTED_MODULE_5__.formatHumanize)(projectEvent.endTime), \".\")\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        t(\"giveaway.distribution.descriptionFCFSRange.hasNoWhitelist.pre\", {\n                            event: globalT(\"event_one\")\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                            children: t(\"giveaway.distribution.descriptionFCFSRange.hasNoWhitelist.mid\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSRangeDistribution.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 17\n                        }, void 0),\n                        t(\"giveaway.distribution.descriptionFCFSRange.hasNoWhitelist.post\"),\n                        distributionType == _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.DistributionType.FCFS_RANGE_END && !isProjectEnded ? \"Please check for the results \".concat((0,_Root_utils_date_utils__WEBPACK_IMPORTED_MODULE_5__.formatHumanize)(projectEvent.endTime), \".\") : \"Results announced \".concat((0,_Root_utils_date_utils__WEBPACK_IMPORTED_MODULE_5__.formatHumanize)(projectEvent.endTime), \".\")\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSRangeDistribution.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_List__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"space-y-5\",\n                data: stats || [],\n                render: (item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenFCFSUserProgress__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        data: item,\n                        token: token,\n                        showDetails: showDetails,\n                        rule: item.raw\n                    }, key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSRangeDistribution.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSRangeDistribution.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TokenFCFSRangeDistribution, \"OkqBUa8ZQsz6JZ9DDKpS2vbBUzs=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c = TokenFCFSRangeDistribution;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TokenFCFSRangeDistribution);\nvar _c;\n$RefreshReg$(_c, \"TokenFCFSRangeDistribution\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/Distribution/TokenFCFSRangeDistribution.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/components/GiveawayFCFSRangeTag.tsx":
/*!******************************************************************!*\
  !*** ./components/Giveaways/components/GiveawayFCFSRangeTag.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GiveawayFCFSRangeTag; },\n/* harmony export */   formatUserProgressTitle: function() { return /* binding */ formatUserProgressTitle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction formatUserProgressTitle(param) {\n    let { rule, completed, total, pointsSuffix, pointsCompletedText } = param;\n    const isCompleted = total === completed;\n    let suffix;\n    let completedText;\n    switch(rule.ruleType){\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.POINTS:\n            suffix = pointsSuffix;\n            completedText = pointsCompletedText;\n            break;\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.TASK_COUNT:\n        default:\n            suffix = \"Quest Completed\";\n            completedText = \"Task Completed\";\n    }\n    return isCompleted ? completedText : \"\".concat(completed, \" / \").concat(total, \" \").concat(suffix);\n}\nfunction GiveawayFCFSRangeTag(param) {\n    let { rule } = param;\n    _s();\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    switch(rule.ruleType){\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.TASK_COUNT:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        \" Complete any \",\n                        rule.min,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 18\n                }, void 0),\n                className: \"!inline-flex\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this);\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.POINTS:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        \" \",\n                        globalT(\"projectPoints\"),\n                        \" greater than \",\n                        rule.min,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 13\n                }, void 0),\n                className: \"!inline-flex !bg-primary/10 !border-primary/60 !font-semibold\",\n                rounded: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this);\n        default:\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n        lineNumber: 64,\n        columnNumber: 10\n    }, this);\n}\n_s(GiveawayFCFSRangeTag, \"LmsnZQLxSh8WbZGko4pwvOtj7/c=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = GiveawayFCFSRangeTag;\nvar _c;\n$RefreshReg$(_c, \"GiveawayFCFSRangeTag\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawayFCFSRangeTag.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/components/TokenFCFSUserProgress.tsx":
/*!*******************************************************************!*\
  !*** ./components/Giveaways/components/TokenFCFSUserProgress.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TokenGiveawayUserProgress; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_List_ListItem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/List/ListItem */ \"./components/List/ListItem.tsx\");\n/* harmony import */ var _Components_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Loaders/SpinLoader */ \"./components/Loaders/SpinLoader.tsx\");\n/* harmony import */ var _Components_RadialProgress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/RadialProgress */ \"./components/RadialProgress/index.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _GiveawayFCFSRangeTag__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./GiveawayFCFSRangeTag */ \"./components/Giveaways/components/GiveawayFCFSRangeTag.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction TokenGiveawayUserProgress(param) {\n    let { showDetails, token, description, data, rule } = param;\n    _s();\n    const { claimed, percentage, amount, isCompleted, completed, total, status } = data;\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    //User Progress only makes sense if total is non-zero, else this giveaway probably just has a whitelist\n    if (total == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_List_ListItem__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        icon: isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_10__.Check, {\n            size: 22,\n            className: \"text-primary-foreground bg-primary rounded-full p-1\",\n            weight: \"bold\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 42,\n            columnNumber: 11\n        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RadialProgress__WEBPACK_IMPORTED_MODULE_3__.RadialProgress, {\n            progress: percentage,\n            text: \"\",\n            circleSize: 26,\n            barSize: 3\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 48,\n            columnNumber: 11\n        }, void 0),\n        title: \"\".concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__.formatAmount)(amount === null || amount === void 0 ? void 0 : amount.toString(), token.decimals), \" \").concat(token.ticker),\n        subtitle: (0,_GiveawayFCFSRangeTag__WEBPACK_IMPORTED_MODULE_8__.formatUserProgressTitle)({\n            rule,\n            total,\n            completed,\n            pointsSuffix: \"\".concat(globalT(\"projectPoints\"), \" Scored\"),\n            pointsCompletedText: \"All \".concat(globalT(\"projectPoints\"), \" Scored\")\n        }),\n        extra: showDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_7__.Fragment, {\n            children: [\n                claimed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-primary text-xs text-primary-foreground rounded-full py-1 px-2 flex items-center space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_10__.Check, {\n                            className: \"h-3\",\n                            weight: \"bold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 17\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Claimed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 17\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 15\n                }, void 0),\n                status && status === _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.RewardStatus.PROCESSING ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 15\n                }, void 0) : isCompleted && !claimed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_4__.Tag, {\n                    title: \"Claimable\",\n                    size: \"small\",\n                    className: \"!font-bold\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 17\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 68,\n            columnNumber: 11\n        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_7__.Fragment, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 86,\n            columnNumber: 11\n        }, void 0),\n        description: description\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenGiveawayUserProgress, \"LmsnZQLxSh8WbZGko4pwvOtj7/c=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation\n    ];\n});\n_c = TokenGiveawayUserProgress;\nvar _c;\n$RefreshReg$(_c, \"TokenGiveawayUserProgress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenFCFSUserProgress.tsx\n"));

/***/ }),

/***/ "./components/Loaders/SingleDot.tsx":
/*!******************************************!*\
  !*** ./components/Loaders/SingleDot.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-content-loader */ \"./node_modules/react-content-loader/dist/react-content-loader.es.js\");\n\n\n\nconst SingleDotLoader = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_content_loader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        speed: 2,\n        width: 32,\n        height: 32,\n        viewBox: \"0 0 32 32\",\n        uniqueKey: \"single-dot-loader\",\n        backgroundColor: \"#cdcdcd\",\n        foregroundColor: \"#ddd\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n            cx: \"16\",\n            cy: \"16\",\n            r: \"16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n            lineNumber: 15,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\n_c = SingleDotLoader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SingleDotLoader);\nvar _c;\n$RefreshReg$(_c, \"SingleDotLoader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvU2luZ2xlRG90LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTBCO0FBQ3VCO0FBRWpELE1BQU1FLGtCQUFrQixDQUFDQyxzQkFDdkIsOERBQUNGLDREQUFhQTtRQUNaRyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxTQUFRO1FBQ1JDLFdBQVU7UUFDVkMsaUJBQWdCO1FBQ2hCQyxpQkFBZ0I7UUFDZixHQUFHUCxLQUFLO2tCQUVULDRFQUFDUTtZQUFPQyxJQUFHO1lBQUtDLElBQUc7WUFBS0MsR0FBRTs7Ozs7Ozs7Ozs7S0FYeEJaO0FBZU4sK0RBQWVBLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdC50c3g/YTE5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgQ29udGVudExvYWRlciBmcm9tICdyZWFjdC1jb250ZW50LWxvYWRlcic7XHJcblxyXG5jb25zdCBTaW5nbGVEb3RMb2FkZXIgPSAocHJvcHM6IGFueSkgPT4gKFxyXG4gIDxDb250ZW50TG9hZGVyXHJcbiAgICBzcGVlZD17Mn1cclxuICAgIHdpZHRoPXszMn1cclxuICAgIGhlaWdodD17MzJ9XHJcbiAgICB2aWV3Qm94PVwiMCAwIDMyIDMyXCJcclxuICAgIHVuaXF1ZUtleT1cInNpbmdsZS1kb3QtbG9hZGVyXCJcclxuICAgIGJhY2tncm91bmRDb2xvcj1cIiNjZGNkY2RcIlxyXG4gICAgZm9yZWdyb3VuZENvbG9yPVwiI2RkZFwiXHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgPlxyXG4gICAgPGNpcmNsZSBjeD1cIjE2XCIgY3k9XCIxNlwiIHI9XCIxNlwiIC8+XHJcbiAgPC9Db250ZW50TG9hZGVyPlxyXG4pO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU2luZ2xlRG90TG9hZGVyO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb250ZW50TG9hZGVyIiwiU2luZ2xlRG90TG9hZGVyIiwicHJvcHMiLCJzcGVlZCIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsInVuaXF1ZUtleSIsImJhY2tncm91bmRDb2xvciIsImZvcmVncm91bmRDb2xvciIsImNpcmNsZSIsImN4IiwiY3kiLCJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Loaders/SingleDot.tsx\n"));

/***/ }),

/***/ "./components/Paragraph.tsx":
/*!**********************************!*\
  !*** ./components/Paragraph.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Paragraph = (param)=>{\n    let { icon, title, description } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1 mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 w-4 text-ch\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-bold text-ch\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm break-words text-cs\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Paragraph;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Paragraph);\nvar _c;\n$RefreshReg$(_c, \"Paragraph\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BhcmFncmFwaC50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE1BQU1BLFlBQVk7UUFBQyxFQUNqQkMsSUFBSSxFQUNKQyxLQUFLLEVBQ0xDLFdBQVcsRUFLWjtJQUNDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FBbUJKOzs7Ozs7a0NBQ2xDLDhEQUFDSzt3QkFBR0QsV0FBVTtrQ0FBNkJIOzs7Ozs7Ozs7Ozs7MEJBRTdDLDhEQUFDSztnQkFBRUYsV0FBVTswQkFBK0JGOzs7Ozs7Ozs7Ozs7QUFHbEQ7S0FsQk1IO0FBb0JOLCtEQUFlQSxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvUGFyYWdyYXBoLnRzeD80YjNkIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFBhcmFncmFwaCA9ICh7XHJcbiAgaWNvbixcclxuICB0aXRsZSxcclxuICBkZXNjcmlwdGlvbixcclxufToge1xyXG4gIGljb246IFJlYWN0LlJlYWN0Tm9kZTtcclxuICB0aXRsZTogc3RyaW5nIHwgUmVhY3QuUmVhY3ROb2RlO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgbWItNFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWNoXCI+e2ljb259PC9kaXY+XHJcbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtY2hcIj57dGl0bGV9PC9oMz5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gYnJlYWstd29yZHMgdGV4dC1jc1wiPntkZXNjcmlwdGlvbn08L3A+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgUGFyYWdyYXBoO1xyXG4iXSwibmFtZXMiOlsiUGFyYWdyYXBoIiwiaWNvbiIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Paragraph.tsx\n"));

/***/ }),

/***/ "./components/RadialProgress/index.tsx":
/*!*********************************************!*\
  !*** ./components/RadialProgress/index.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RadialProgress: function() { return /* binding */ RadialProgress; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst RadialProgress = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c = ()=>__webpack_require__.e(/*! import() */ \"components_RadialProgress_RadialProgress_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @Components/RadialProgress/RadialProgress */ \"./components/RadialProgress/RadialProgress.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\RadialProgress\\\\index.tsx -> \" + \"@Components/RadialProgress/RadialProgress\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            height: 66,\n            width: 66\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RadialProgress\\\\index.tsx\",\n            lineNumber: 8,\n            columnNumber: 20\n        }, undefined)\n});\n_c1 = RadialProgress;\nvar _c, _c1;\n$RefreshReg$(_c, \"RadialProgress$dynamic\");\n$RefreshReg$(_c1, \"RadialProgress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTREO0FBQ3pCO0FBRTVCLE1BQU1FLGlCQUFpQkQsbURBQU9BLE1BQ25DLElBQU0sNE9BQU87Ozs7OztJQUVYRSxLQUFLO0lBQ0xDLFNBQVMsa0JBQU0sOERBQUNKLHFFQUFlQTtZQUFDSyxRQUFRO1lBQUlDLE9BQU87Ozs7OztHQUVyRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL2luZGV4LnRzeD9mZjA5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTaW5nbGVEb3RMb2FkZXIgZnJvbSAnQENvbXBvbmVudHMvTG9hZGVycy9TaW5nbGVEb3QnO1xyXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnO1xyXG5cclxuZXhwb3J0IGNvbnN0IFJhZGlhbFByb2dyZXNzID0gZHluYW1pYyhcclxuICAoKSA9PiBpbXBvcnQoJ0BDb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL1JhZGlhbFByb2dyZXNzJyksXHJcbiAge1xyXG4gICAgc3NyOiBmYWxzZSxcclxuICAgIGxvYWRpbmc6ICgpID0+IDxTaW5nbGVEb3RMb2FkZXIgaGVpZ2h0PXs2Nn0gd2lkdGg9ezY2fSAvPixcclxuICB9LFxyXG4pO1xyXG4iXSwibmFtZXMiOlsiU2luZ2xlRG90TG9hZGVyIiwiZHluYW1pYyIsIlJhZGlhbFByb2dyZXNzIiwic3NyIiwibG9hZGluZyIsImhlaWdodCIsIndpZHRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/RadialProgress/index.tsx\n"));

/***/ })

}]);
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Giveaways_GiveawaySummaryList_tsx";
exports.ids = ["components_Giveaways_GiveawaySummaryList_tsx"];
exports.modules = {

/***/ "./components/Giveaways/GiveawayRendererWrapper.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/GiveawayRendererWrapper.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawayRendererWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Tasks/components/TaskTileLoader */ \"./components/Tasks/components/TaskTileLoader.tsx\");\n/* harmony import */ var _Root_context_Actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/context/Actions */ \"./context/Actions.ts\");\n/* harmony import */ var _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Root/context/AppContext */ \"./context/AppContext.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_GiveawayRenderer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/GiveawayRenderer */ \"./components/Giveaways/components/GiveawayRenderer.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__]);\n_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction GiveawayRendererWrapper({ giveaway, projectEvent, size }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { state: { isAuthenticated }, dispatch } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_3__.useAppContext)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GiveawayRenderer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        giveawayType: giveaway.giveawayType,\n        distributionType: giveaway.distributionType,\n        renderer: \"summary\",\n        placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                style: {\n                    background: \"transparent\",\n                    border: 0,\n                    padding: 0\n                },\n                loaderProps: {\n                    viewBox: \"0 0 300 32\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n                lineNumber: 30,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n            lineNumber: 29,\n            columnNumber: 9\n        }, void 0),\n        props: {\n            giveaway: giveaway,\n            projectEvent,\n            size,\n            onClick: ()=>{\n                if (!isAuthenticated) {\n                    dispatch({\n                        type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_2__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                        payload: null\n                    });\n                    return;\n                }\n                const newQuery = {\n                    ...router.query,\n                    taskid: \"claim\",\n                    giveaway: giveaway.id\n                };\n                router.push({\n                    pathname: router.pathname,\n                    query: newQuery\n                }, undefined, {\n                    shallow: true\n                });\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayRendererWrapper.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/GiveawayRendererWrapper.tsx\n");

/***/ }),

/***/ "./components/Giveaways/GiveawaySummaryList.tsx":
/*!******************************************************!*\
  !*** ./components/Giveaways/GiveawaySummaryList.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawaySummaryList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AirSlider_AirSlider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AirSlider/AirSlider */ \"./components/AirSlider/AirSlider.tsx\");\n/* harmony import */ var _Components_Loaders_ListSkeleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Loaders/ListSkeleton */ \"./components/Loaders/ListSkeleton.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _hooks_useGetGiveaways__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hooks/useGetGiveaways */ \"./components/Giveaways/hooks/useGetGiveaways.ts\");\n/* harmony import */ var _GiveawayRendererWrapper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./GiveawayRendererWrapper */ \"./components/Giveaways/GiveawayRendererWrapper.tsx\");\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AirSlider_AirSlider__WEBPACK_IMPORTED_MODULE_1__, _Components_Tag__WEBPACK_IMPORTED_MODULE_3__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__, _hooks_useGetGiveaways__WEBPACK_IMPORTED_MODULE_5__, _GiveawayRendererWrapper__WEBPACK_IMPORTED_MODULE_6__, _Root_utils_utils__WEBPACK_IMPORTED_MODULE_7__]);\n([_Components_AirSlider_AirSlider__WEBPACK_IMPORTED_MODULE_1__, _Components_Tag__WEBPACK_IMPORTED_MODULE_3__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__, _hooks_useGetGiveaways__WEBPACK_IMPORTED_MODULE_5__, _GiveawayRendererWrapper__WEBPACK_IMPORTED_MODULE_6__, _Root_utils_utils__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nfunction GiveawaySummaryList({ projectEvent, scrollable, className }) {\n    const { data: giveawayData, loading } = (0,_hooks_useGetGiveaways__WEBPACK_IMPORTED_MODULE_5__.useGetGiveaways)(projectEvent.id);\n    const giveaways = giveawayData?.pGiveaways || [];\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"ssr\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"ssr\", {\n        keyPrefix: \"global\"\n    });\n    if (scrollable) {\n        if (giveaways.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AirSlider_AirSlider__WEBPACK_IMPORTED_MODULE_1__.AirSlider, {\n                data: giveaways || [],\n                spaceBetween: 20,\n                className: `${giveaways?.length > 1 ? \"flex-shrink-0 flex-grow max-w-full\" : \"w-full\"}`,\n                render: (item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-2xl overflow-hidden component relative p-2.5 pr-4 sm:p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayRendererWrapper__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            giveaway: item,\n                            size: \"small\",\n                            projectEvent: projectEvent\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 15\n                        }, void 0)\n                    }, key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 13\n                    }, void 0),\n                header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__.Gift, {\n                            size: 22,\n                            weight: \"duotone\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"font-bold\",\n                            children: t(\"giveaway.summaryList.heading\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true),\n                showActions: giveaways?.length > 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"rounded-2xl overflow-hidden component relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 text-ch relative z-1 items-center flex gap-2 border-b border-c\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__.Gift, {\n                            className: \"text-[#d0457b]\",\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        t(\"giveaway.summaryList.heading\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 divide-y divide-foreground/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_ListSkeleton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        total: 3\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    if (giveaways.length === 0) {\n        const projectSummary = projectEvent?.summary || {};\n        const totalXP = projectSummary?.totalXP || 0;\n        const totalPoints = projectSummary?.totalPoints || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"rounded-2xl overflow-hidden component relative p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-ch relative z-1 flex gap-2 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__.Gift, {\n                            className: \"text-[#d0457b]\",\n                            size: 32,\n                            weight: \"duotone\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-base font-semibold text-ch\",\n                            children: t(\"giveaway.summaryList.subheading\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex my-5 gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_9__.FeatureGuard, {\n                            feature: \"XP\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_3__.Tag, {\n                                title: `${totalXP} ${globalT(\"projectXp\")}`,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__.Sparkle, {\n                                    size: 26,\n                                    weight: \"duotone\",\n                                    className: \"text-orange-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, void 0),\n                                className: \"!font-semibold !py-2.5 !text-base flex-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_9__.FeatureGuard, {\n                            feature: \"POINTS\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_3__.Tag, {\n                                title: `${totalPoints} ${globalT(\"projectPoints\")}`,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__.Fire, {\n                                    size: 26,\n                                    weight: \"duotone\",\n                                    className: \"text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, void 0),\n                                className: \"!font-semibold !py-3 !text-base flex-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-cl leading-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-ch font-bold\",\n                            children: t(\"giveaway.summaryList.noRewardNote.pre\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        t(\"giveaway.summaryList.noRewardNote.post\"),\n                        ((0,_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_9__.isFeatureEnabled)(\"XP\") || (0,_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_9__.isFeatureEnabled)(\"POINTS\")) && t(\"giveaway.summaryList.noRewardNote.postXP\", {\n                            // reward: `${isFeatureEnabled('XP') ? globalT('projectXp') : ''}/${\n                            //   isFeatureEnabled('POINTS') ? globalT('projectPoints') : ''\n                            // }`,\n                            reward: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_7__.pointsXpT)(globalT(\"projectPoints\"), globalT(\"projectXp\")),\n                            projectPoints: globalT(\"projectPoints\")\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_9__.FeatureGuard, {\n                            feature: \"POINTS\",\n                            children: t(\"giveaway.summaryList.noRewardNote.postPoints\", {\n                                projectPoints: globalT(\"projectPoints\")\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"rounded-2xl overflow-hidden component relative divide-y divide-foreground/10\", className),\n        children: [\n            giveaways?.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 text-ch relative z-1 items-center flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__.Gift, {\n                        className: \"text-[#d0457b]\",\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    t(\"giveaway.summaryList.heading\")\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this),\n            giveaways.map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(`p-4`, giveaways?.length === 1 ? \"overflow-hidden rounded-2xl\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayRendererWrapper__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        giveaway: item,\n                        size: giveaways?.length > 1 ? \"small\" : \"large\",\n                        projectEvent: projectEvent\n                    }, key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, key, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawaySummaryList.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/GiveawaySummaryList.tsx\n");

/***/ }),

/***/ "./components/Loaders/ListItemSkeleton.tsx":
/*!*************************************************!*\
  !*** ./components/Loaders/ListItemSkeleton.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-content-loader */ \"react-content-loader\");\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_content_loader__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ListItemSkeleton = (props)=>{\n    if (props.size === \"small\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_content_loader__WEBPACK_IMPORTED_MODULE_1___default()), {\n                height: 20,\n                width: \"100%\",\n                className: \"\",\n                backgroundColor: \"var(--skeleton-background)\",\n                foregroundColor: \"var(--skeleton-foreground)\",\n                uniqueKey: \"list-item-skeleton\",\n                ...props,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        cx: \"10\",\n                        cy: \"10\",\n                        r: \"10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        height: \"12\",\n                        rx: \"6\",\n                        ry: \"6\",\n                        width: \"95%\",\n                        x: \"36\",\n                        y: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_content_loader__WEBPACK_IMPORTED_MODULE_1___default()), {\n            height: 86,\n            width: \"100%\",\n            backgroundColor: \"var(--skeleton-background)\",\n            foregroundColor: \"var(--skeleton-foreground)\",\n            uniqueKey: \"list-item-skeleton\",\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"103\",\n                    y: \"16\",\n                    rx: \"3\",\n                    ry: \"3\",\n                    width: \"123\",\n                    height: \"8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"105\",\n                    y: \"36\",\n                    rx: \"3\",\n                    ry: \"3\",\n                    width: \"171\",\n                    height: \"8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"105\",\n                    y: \"56\",\n                    rx: \"3\",\n                    ry: \"3\",\n                    width: \"90\",\n                    height: \"8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"44\",\n                    cy: \"42\",\n                    r: \"32\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListItemSkeleton.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ListItemSkeleton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Loaders/ListItemSkeleton.tsx\n");

/***/ }),

/***/ "./components/Loaders/ListSkeleton.tsx":
/*!*********************************************!*\
  !*** ./components/Loaders/ListSkeleton.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ListItemSkeleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ListItemSkeleton */ \"./components/Loaders/ListItemSkeleton.tsx\");\n\n\n\nconst ListSkeleton = ({ total = 4, size })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            ...Array(total)\n        ].map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ListItemSkeleton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                size: size\n            }, key, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\ListSkeleton.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ListSkeleton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvTGlzdFNrZWxldG9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBCO0FBQ3dCO0FBRWxELE1BQU1FLGVBQWUsQ0FBQyxFQUNwQkMsUUFBUSxDQUFDLEVBQ1RDLElBQUksRUFJTDtJQUNDLHFCQUNFO2tCQUNHO2VBQUlDLE1BQU1GO1NBQU8sQ0FBQ0csR0FBRyxDQUFDLENBQUNDLE1BQU1DLG9CQUM1Qiw4REFBQ1AseURBQWdCQTtnQkFBV0csTUFBTUE7ZUFBWEk7Ozs7OztBQUkvQjtBQUVBLGlFQUFlTixZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9Mb2FkZXJzL0xpc3RTa2VsZXRvbi50c3g/YWFiOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgTGlzdEl0ZW1Ta2VsZXRvbiBmcm9tICcuL0xpc3RJdGVtU2tlbGV0b24nO1xyXG5cclxuY29uc3QgTGlzdFNrZWxldG9uID0gKHtcclxuICB0b3RhbCA9IDQsXHJcbiAgc2l6ZSxcclxufToge1xyXG4gIHRvdGFsPzogbnVtYmVyO1xyXG4gIHNpemU/OiAnc21hbGwnIHwgJ2RlZmF1bHQnO1xyXG59KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIHtbLi4uQXJyYXkodG90YWwpXS5tYXAoKGl0ZW0sIGtleSkgPT4gKFxyXG4gICAgICAgIDxMaXN0SXRlbVNrZWxldG9uIGtleT17a2V5fSBzaXplPXtzaXplfSAvPlxyXG4gICAgICApKX1cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBMaXN0U2tlbGV0b247XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxpc3RJdGVtU2tlbGV0b24iLCJMaXN0U2tlbGV0b24iLCJ0b3RhbCIsInNpemUiLCJBcnJheSIsIm1hcCIsIml0ZW0iLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Loaders/ListSkeleton.tsx\n");

/***/ })

};
;
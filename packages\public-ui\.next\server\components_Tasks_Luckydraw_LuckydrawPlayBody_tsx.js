"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Tasks_Luckydraw_LuckydrawPlayBody_tsx";
exports.ids = ["components_Tasks_Luckydraw_LuckydrawPlayBody_tsx"];
exports.modules = {

/***/ "./components/Spinwheel.tsx":
/*!**********************************!*\
  !*** ./components/Spinwheel.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Spinwheel = ({ rewards, onSpinStart, onSpinEnd, resultIndex, centerImage = \"/android-chrome-512x512.png\", initialPosition = null })=>{\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isSpinning, setIsSpinning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRotation, setCurrentRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const centerImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const initialPositionApplied = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const colorPairs = [\n        {\n            start: \"#dc2626\",\n            end: \"#7f1d1d\"\n        },\n        {\n            start: \"#2563eb\",\n            end: \"#1e3a8a\"\n        },\n        {\n            start: \"#16a34a\",\n            end: \"#14532d\"\n        },\n        {\n            start: \"#7c3aed\",\n            end: \"#4c1d95\"\n        },\n        {\n            start: \"#65a30d\",\n            end: \"#365314\"\n        },\n        {\n            start: \"#0d9488\",\n            end: \"#134e4a\"\n        },\n        {\n            start: \"#4f46e5\",\n            end: \"#312e81\"\n        },\n        {\n            start: \"#e11d48\",\n            end: \"#881337\"\n        },\n        {\n            start: \"#c026d3\",\n            end: \"#701a75\"\n        },\n        {\n            start: \"#ea580c\",\n            end: \"#7c2d12\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (centerImage) {\n            const img = new Image();\n            img.src = centerImage;\n            img.onload = ()=>{\n                centerImageRef.current = img;\n                setImageLoaded(true);\n            };\n        } else {\n            centerImageRef.current = null;\n        }\n    }, [\n        centerImage\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialPosition !== null && !initialPositionApplied.current && rewards.length > 0 && imageLoaded) {\n            const targetRotation = calculateTargetRotation(initialPosition);\n            setCurrentRotation(targetRotation);\n            initialPositionApplied.current = true;\n        }\n    }, [\n        initialPosition,\n        rewards,\n        imageLoaded\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (imageLoaded) {\n            drawWheel();\n        }\n    }, [\n        rewards,\n        currentRotation,\n        imageLoaded\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (resultIndex !== null && !isSpinning) {\n            spin(resultIndex);\n        }\n    }, [\n        resultIndex\n    ]);\n    const createGradient = (ctx, startAngle, endAngle, colorPair)=>{\n        const centerX = ctx.canvas.width / 2;\n        const centerY = ctx.canvas.height / 2;\n        const radius = Math.min(centerX, centerY) - 20;\n        const midAngle = startAngle + (endAngle - startAngle) / 2;\n        const gradientStartX = centerX + Math.cos(midAngle) * radius * 0.3;\n        const gradientStartY = centerY + Math.sin(midAngle) * radius * 0.3;\n        const gradientEndX = centerX + Math.cos(midAngle) * radius;\n        const gradientEndY = centerY + Math.sin(midAngle) * radius;\n        const gradient = ctx.createLinearGradient(gradientStartX, gradientStartY, gradientEndX, gradientEndY);\n        gradient.addColorStop(0, colorPair.start);\n        gradient.addColorStop(1, colorPair.end);\n        return gradient;\n    };\n    const drawWheel = ()=>{\n        const canvas = canvasRef.current;\n        if (!canvas || rewards.length === 0 || !centerImageRef.current) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        canvas.width = canvas.height = 800;\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const radius = Math.min(centerX, centerY) - 40;\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        // Draw outer ring shadow\n        const outerGradient = ctx.createConicGradient(Math.PI / 2, centerX, centerY);\n        outerGradient.addColorStop(0, \"#c084fc\");\n        outerGradient.addColorStop(0.5, \"#d8b4fe\");\n        outerGradient.addColorStop(1, \"#c084fc\");\n        ctx.save();\n        ctx.beginPath();\n        ctx.arc(centerX, centerY, radius + 5, 0, 2 * Math.PI);\n        ctx.fillStyle = outerGradient;\n        ctx.fill();\n        ctx.restore();\n        // Move to center and rotate\n        ctx.save();\n        ctx.translate(centerX, centerY);\n        ctx.rotate(currentRotation * Math.PI / 180);\n        ctx.translate(-centerX, -centerY);\n        let startAngle = -Math.PI / 2;\n        const sliceAngle = 2 * Math.PI / rewards.length;\n        // Draw segments clockwise\n        rewards.forEach((reward, index)=>{\n            const endAngle = startAngle + sliceAngle;\n            ctx.beginPath();\n            ctx.moveTo(centerX, centerY);\n            ctx.arc(centerX, centerY, radius, startAngle, endAngle);\n            ctx.closePath();\n            ctx.fillStyle = createGradient(ctx, startAngle, endAngle, colorPairs[index % colorPairs.length]);\n            ctx.fill();\n            ctx.beginPath();\n            ctx.moveTo(centerX, centerY);\n            ctx.arc(centerX, centerY, radius, startAngle, endAngle);\n            ctx.strokeStyle = \"rgba(255, 255, 255, 0.2)\";\n            ctx.lineWidth = 2;\n            ctx.stroke();\n            // Draw text\n            ctx.save();\n            ctx.translate(centerX, centerY);\n            ctx.rotate(startAngle + sliceAngle / 2);\n            ctx.textAlign = \"right\";\n            ctx.textBaseline = \"middle\";\n            ctx.fillStyle = \"#FFFFFF\";\n            ctx.font = \"bold 40px sans-serif\";\n            ctx.shadowColor = \"rgba(0, 0, 0, 0.5)\";\n            ctx.shadowBlur = 4;\n            ctx.shadowOffsetX = 2;\n            ctx.shadowOffsetY = 2;\n            ctx.fillText(`${reward.amount}`, radius - 60, 0);\n            ctx.restore();\n            startAngle = endAngle;\n        });\n        ctx.restore();\n        // Draw center image with circular mask\n        const centerRadius = radius * 0.15;\n        ctx.save();\n        // Create circular clipping path\n        ctx.beginPath();\n        ctx.arc(centerX, centerY, centerRadius, 0, 2 * Math.PI);\n        ctx.clip();\n        // Draw image\n        const imageSize = centerRadius * 2;\n        ctx.drawImage(centerImageRef.current, centerX - centerRadius, centerY - centerRadius, imageSize, imageSize);\n        // Draw border\n        // ctx.strokeStyle = '#E5E7EB';\n        // ctx.lineWidth = 2;\n        // ctx.stroke();\n        ctx.restore();\n        // Draw pointer\n        ctx.beginPath();\n        ctx.moveTo(centerX - 30, centerY - radius);\n        ctx.quadraticCurveTo(centerX, centerY - radius - 15, centerX + 30, centerY - radius);\n        ctx.quadraticCurveTo(centerX + 15, centerY - radius + 30, centerX, centerY - radius + 60);\n        ctx.quadraticCurveTo(centerX - 15, centerY - radius + 30, centerX - 30, centerY - radius);\n        ctx.closePath();\n        // Create a radial gradient for more depth\n        const pointerGradient = ctx.createRadialGradient(centerX, centerY - radius + 20, 10, centerX, centerY - radius + 20, 50);\n        pointerGradient.addColorStop(0, \"#94a3b8\");\n        pointerGradient.addColorStop(0.5, \"#cbd5e1\");\n        pointerGradient.addColorStop(1, \"#f8fafc\");\n        ctx.shadowColor = \"rgba(0, 0, 0, 0.5)\";\n        ctx.shadowBlur = 10;\n        ctx.shadowOffsetX = 0;\n        ctx.shadowOffsetY = 5;\n        ctx.fillStyle = pointerGradient;\n        ctx.fill();\n        ctx.strokeStyle = \"rgba(0, 0, 0, 1)\";\n        ctx.lineWidth = 2.5;\n        ctx.stroke();\n    };\n    const spin = (resultIndex)=>{\n        if (isSpinning) return;\n        setIsSpinning(true);\n        onSpinStart();\n        let startRotation = currentRotation;\n        let targetRotation = calculateTargetRotation(resultIndex);\n        let animationStartTime;\n        const animate = (currentTime)=>{\n            if (!animationStartTime) animationStartTime = currentTime;\n            const elapsed = currentTime - animationStartTime;\n            const duration = 6000;\n            if (elapsed < duration) {\n                const progress = elapsed / duration;\n                const easeProgress = easeOutQuartic(progress);\n                const currentRot = startRotation + (targetRotation - startRotation) * easeProgress;\n                setCurrentRotation(currentRot);\n                requestAnimationFrame(animate);\n            } else {\n                setCurrentRotation(targetRotation);\n                setIsSpinning(false);\n                onSpinEnd();\n            }\n        };\n        requestAnimationFrame(animate);\n    };\n    const calculateTargetRotation = (resultIndex)=>{\n        const sliceAngle = 360 / rewards.length;\n        const maxOffset = sliceAngle * 0.5;\n        const randomOffset = Math.random() * maxOffset - maxOffset / 2;\n        const targetAngle = resultIndex * sliceAngle + sliceAngle / 2 + randomOffset;\n        const extraSpins = 13 * 360;\n        return extraSpins + (360 - targetAngle);\n    };\n    const easeOutQuartic = (x)=>{\n        const correctionFactor = 0.02;\n        return 1 - Math.pow(1 - x + correctionFactor, 4);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative flex flex-col items-center gap-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"w-64 h-64 md:w-96 md:h-96\",\n                width: \"800\",\n                height: \"800\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Spinwheel.tsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Spinwheel.tsx\",\n            lineNumber: 316,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Spinwheel.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Spinwheel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1NwaW53aGVlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTJEO0FBWTNELE1BQU1JLFlBQXNDLENBQUMsRUFDM0NDLE9BQU8sRUFDUEMsV0FBVyxFQUNYQyxTQUFTLEVBQ1RDLFdBQVcsRUFDWEMsY0FBYyw2QkFBNkIsRUFDM0NDLGtCQUFrQixJQUFJLEVBQ3ZCO0lBQ0MsTUFBTUMsWUFBWVQsNkNBQU1BLENBQW9CO0lBQzVDLE1BQU0sQ0FBQ1UsWUFBWUMsY0FBYyxHQUFHViwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNXLGlCQUFpQkMsbUJBQW1CLEdBQUdaLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ2EsYUFBYUMsZUFBZSxHQUFHZCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNZSxpQkFBaUJoQiw2Q0FBTUEsQ0FBMEI7SUFDdkQsTUFBTWlCLHlCQUF5QmpCLDZDQUFNQSxDQUFDO0lBRXRDLE1BQU1rQixhQUFhO1FBQ2pCO1lBQUVDLE9BQU87WUFBV0MsS0FBSztRQUFVO1FBQ25DO1lBQUVELE9BQU87WUFBV0MsS0FBSztRQUFVO1FBQ25DO1lBQUVELE9BQU87WUFBV0MsS0FBSztRQUFVO1FBQ25DO1lBQUVELE9BQU87WUFBV0MsS0FBSztRQUFVO1FBQ25DO1lBQUVELE9BQU87WUFBV0MsS0FBSztRQUFVO1FBQ25DO1lBQUVELE9BQU87WUFBV0MsS0FBSztRQUFVO1FBQ25DO1lBQUVELE9BQU87WUFBV0MsS0FBSztRQUFVO1FBQ25DO1lBQUVELE9BQU87WUFBV0MsS0FBSztRQUFVO1FBQ25DO1lBQUVELE9BQU87WUFBV0MsS0FBSztRQUFVO1FBQ25DO1lBQUVELE9BQU87WUFBV0MsS0FBSztRQUFVO0tBQ3BDO0lBRURyQixnREFBU0EsQ0FBQztRQUNSLElBQUlRLGFBQWE7WUFDZixNQUFNYyxNQUFNLElBQUlDO1lBQ2hCRCxJQUFJRSxHQUFHLEdBQUdoQjtZQUNWYyxJQUFJRyxNQUFNLEdBQUc7Z0JBQ1hSLGVBQWVTLE9BQU8sR0FBR0o7Z0JBQ3pCTixlQUFlO1lBQ2pCO1FBQ0YsT0FBTztZQUNMQyxlQUFlUyxPQUFPLEdBQUc7UUFDM0I7SUFDRixHQUFHO1FBQUNsQjtLQUFZO0lBRWhCUixnREFBU0EsQ0FBQztRQUNSLElBQUlTLG9CQUFvQixRQUFRLENBQUNTLHVCQUF1QlEsT0FBTyxJQUFJdEIsUUFBUXVCLE1BQU0sR0FBRyxLQUFLWixhQUFhO1lBQ3BHLE1BQU1hLGlCQUFpQkMsd0JBQXdCcEI7WUFDL0NLLG1CQUFtQmM7WUFDbkJWLHVCQUF1QlEsT0FBTyxHQUFHO1FBQ25DO0lBQ0YsR0FBRztRQUFDakI7UUFBaUJMO1FBQVNXO0tBQVk7SUFFMUNmLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSWUsYUFBYTtZQUNmZTtRQUNGO0lBQ0YsR0FBRztRQUFDMUI7UUFBU1M7UUFBaUJFO0tBQVk7SUFFMUNmLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSU8sZ0JBQWdCLFFBQVEsQ0FBQ0ksWUFBWTtZQUN2Q29CLEtBQUt4QjtRQUNQO0lBQ0YsR0FBRztRQUFDQTtLQUFZO0lBRWhCLE1BQU15QixpQkFBaUIsQ0FDckJDLEtBQ0FDLFlBQ0FDLFVBQ0FDO1FBRUEsTUFBTUMsVUFBVUosSUFBSUssTUFBTSxDQUFDQyxLQUFLLEdBQUc7UUFDbkMsTUFBTUMsVUFBVVAsSUFBSUssTUFBTSxDQUFDRyxNQUFNLEdBQUc7UUFDcEMsTUFBTUMsU0FBU0MsS0FBS0MsR0FBRyxDQUFDUCxTQUFTRyxXQUFXO1FBRTVDLE1BQU1LLFdBQVdYLGFBQWEsQ0FBQ0MsV0FBV0QsVUFBUyxJQUFLO1FBQ3hELE1BQU1ZLGlCQUFpQlQsVUFBVU0sS0FBS0ksR0FBRyxDQUFDRixZQUFZSCxTQUFTO1FBQy9ELE1BQU1NLGlCQUFpQlIsVUFBVUcsS0FBS00sR0FBRyxDQUFDSixZQUFZSCxTQUFTO1FBQy9ELE1BQU1RLGVBQWViLFVBQVVNLEtBQUtJLEdBQUcsQ0FBQ0YsWUFBWUg7UUFDcEQsTUFBTVMsZUFBZVgsVUFBVUcsS0FBS00sR0FBRyxDQUFDSixZQUFZSDtRQUVwRCxNQUFNVSxXQUFXbkIsSUFBSW9CLG9CQUFvQixDQUN2Q1AsZ0JBQ0FFLGdCQUNBRSxjQUNBQztRQUVGQyxTQUFTRSxZQUFZLENBQUMsR0FBR2xCLFVBQVVoQixLQUFLO1FBQ3hDZ0MsU0FBU0UsWUFBWSxDQUFDLEdBQUdsQixVQUFVZixHQUFHO1FBRXRDLE9BQU8rQjtJQUNUO0lBRUEsTUFBTXRCLFlBQVk7UUFDaEIsTUFBTVEsU0FBUzVCLFVBQVVnQixPQUFPO1FBQ2hDLElBQUksQ0FBQ1ksVUFBVWxDLFFBQVF1QixNQUFNLEtBQUssS0FBSyxDQUFDVixlQUFlUyxPQUFPLEVBQUU7UUFFaEUsTUFBTU8sTUFBTUssT0FBT2lCLFVBQVUsQ0FBQztRQUM5QixJQUFJLENBQUN0QixLQUFLO1FBRVZLLE9BQU9DLEtBQUssR0FBR0QsT0FBT0csTUFBTSxHQUFHO1FBQy9CLE1BQU1KLFVBQVVDLE9BQU9DLEtBQUssR0FBRztRQUMvQixNQUFNQyxVQUFVRixPQUFPRyxNQUFNLEdBQUc7UUFDaEMsTUFBTUMsU0FBU0MsS0FBS0MsR0FBRyxDQUFDUCxTQUFTRyxXQUFXO1FBRTVDUCxJQUFJdUIsU0FBUyxDQUFDLEdBQUcsR0FBR2xCLE9BQU9DLEtBQUssRUFBRUQsT0FBT0csTUFBTTtRQUUvQyx5QkFBeUI7UUFDekIsTUFBTWdCLGdCQUFnQnhCLElBQUl5QixtQkFBbUIsQ0FDM0NmLEtBQUtnQixFQUFFLEdBQUcsR0FDVnRCLFNBQ0FHO1FBRUZpQixjQUFjSCxZQUFZLENBQUMsR0FBRztRQUM5QkcsY0FBY0gsWUFBWSxDQUFDLEtBQUs7UUFDaENHLGNBQWNILFlBQVksQ0FBQyxHQUFHO1FBRTlCckIsSUFBSTJCLElBQUk7UUFDUjNCLElBQUk0QixTQUFTO1FBQ2I1QixJQUFJNkIsR0FBRyxDQUFDekIsU0FBU0csU0FBU0UsU0FBUyxHQUFHLEdBQUcsSUFBSUMsS0FBS2dCLEVBQUU7UUFDcEQxQixJQUFJOEIsU0FBUyxHQUFHTjtRQUNoQnhCLElBQUkrQixJQUFJO1FBQ1IvQixJQUFJZ0MsT0FBTztRQUVYLDRCQUE0QjtRQUM1QmhDLElBQUkyQixJQUFJO1FBQ1IzQixJQUFJaUMsU0FBUyxDQUFDN0IsU0FBU0c7UUFDdkJQLElBQUlrQyxNQUFNLENBQUMsa0JBQW1CeEIsS0FBS2dCLEVBQUUsR0FBSTtRQUN6QzFCLElBQUlpQyxTQUFTLENBQUMsQ0FBQzdCLFNBQVMsQ0FBQ0c7UUFFekIsSUFBSU4sYUFBYSxDQUFDUyxLQUFLZ0IsRUFBRSxHQUFHO1FBQzVCLE1BQU1TLGFBQWEsSUFBS3pCLEtBQUtnQixFQUFFLEdBQUl2RCxRQUFRdUIsTUFBTTtRQUVqRCwwQkFBMEI7UUFDMUJ2QixRQUFRaUUsT0FBTyxDQUFDLENBQUNDLFFBQVFDO1lBQ3ZCLE1BQU1wQyxXQUFXRCxhQUFha0M7WUFFOUJuQyxJQUFJNEIsU0FBUztZQUNiNUIsSUFBSXVDLE1BQU0sQ0FBQ25DLFNBQVNHO1lBQ3BCUCxJQUFJNkIsR0FBRyxDQUFDekIsU0FBU0csU0FBU0UsUUFBUVIsWUFBWUM7WUFDOUNGLElBQUl3QyxTQUFTO1lBQ2J4QyxJQUFJOEIsU0FBUyxHQUFHL0IsZUFDZEMsS0FDQUMsWUFDQUMsVUFDQWhCLFVBQVUsQ0FBQ29ELFFBQVFwRCxXQUFXUSxNQUFNLENBQUM7WUFFdkNNLElBQUkrQixJQUFJO1lBRVIvQixJQUFJNEIsU0FBUztZQUNiNUIsSUFBSXVDLE1BQU0sQ0FBQ25DLFNBQVNHO1lBQ3BCUCxJQUFJNkIsR0FBRyxDQUFDekIsU0FBU0csU0FBU0UsUUFBUVIsWUFBWUM7WUFDOUNGLElBQUl5QyxXQUFXLEdBQUc7WUFDbEJ6QyxJQUFJMEMsU0FBUyxHQUFHO1lBQ2hCMUMsSUFBSTJDLE1BQU07WUFFVixZQUFZO1lBQ1ozQyxJQUFJMkIsSUFBSTtZQUNSM0IsSUFBSWlDLFNBQVMsQ0FBQzdCLFNBQVNHO1lBQ3ZCUCxJQUFJa0MsTUFBTSxDQUFDakMsYUFBYWtDLGFBQWE7WUFDckNuQyxJQUFJNEMsU0FBUyxHQUFHO1lBQ2hCNUMsSUFBSTZDLFlBQVksR0FBRztZQUNuQjdDLElBQUk4QixTQUFTLEdBQUc7WUFDaEI5QixJQUFJOEMsSUFBSSxHQUFHO1lBRVg5QyxJQUFJK0MsV0FBVyxHQUFHO1lBQ2xCL0MsSUFBSWdELFVBQVUsR0FBRztZQUNqQmhELElBQUlpRCxhQUFhLEdBQUc7WUFDcEJqRCxJQUFJa0QsYUFBYSxHQUFHO1lBQ3BCbEQsSUFBSW1ELFFBQVEsQ0FBQyxDQUFDLEVBQUVkLE9BQU9lLE1BQU0sQ0FBQyxDQUFDLEVBQUUzQyxTQUFTLElBQUk7WUFDOUNULElBQUlnQyxPQUFPO1lBRVgvQixhQUFhQztRQUNmO1FBRUFGLElBQUlnQyxPQUFPO1FBRVgsdUNBQXVDO1FBQ3ZDLE1BQU1xQixlQUFlNUMsU0FBUztRQUM5QlQsSUFBSTJCLElBQUk7UUFFUixnQ0FBZ0M7UUFDaEMzQixJQUFJNEIsU0FBUztRQUNiNUIsSUFBSTZCLEdBQUcsQ0FBQ3pCLFNBQVNHLFNBQVM4QyxjQUFjLEdBQUcsSUFBSTNDLEtBQUtnQixFQUFFO1FBQ3REMUIsSUFBSXNELElBQUk7UUFFUixhQUFhO1FBQ2IsTUFBTUMsWUFBWUYsZUFBZTtRQUNqQ3JELElBQUl3RCxTQUFTLENBQ1h4RSxlQUFlUyxPQUFPLEVBQ3RCVyxVQUFVaUQsY0FDVjlDLFVBQVU4QyxjQUNWRSxXQUNBQTtRQUdGLGNBQWM7UUFDZCwrQkFBK0I7UUFDL0IscUJBQXFCO1FBQ3JCLGdCQUFnQjtRQUVoQnZELElBQUlnQyxPQUFPO1FBRVgsZUFBZTtRQUNmaEMsSUFBSTRCLFNBQVM7UUFFYjVCLElBQUl1QyxNQUFNLENBQUNuQyxVQUFVLElBQUlHLFVBQVVFO1FBQ25DVCxJQUFJeUQsZ0JBQWdCLENBQ2xCckQsU0FDQUcsVUFBVUUsU0FBUyxJQUNuQkwsVUFBVSxJQUNWRyxVQUFVRTtRQUVaVCxJQUFJeUQsZ0JBQWdCLENBQ2xCckQsVUFBVSxJQUNWRyxVQUFVRSxTQUFTLElBQ25CTCxTQUNBRyxVQUFVRSxTQUFTO1FBRXJCVCxJQUFJeUQsZ0JBQWdCLENBQ2xCckQsVUFBVSxJQUNWRyxVQUFVRSxTQUFTLElBQ25CTCxVQUFVLElBQ1ZHLFVBQVVFO1FBRVpULElBQUl3QyxTQUFTO1FBRWIsMENBQTBDO1FBQzFDLE1BQU1rQixrQkFBa0IxRCxJQUFJMkQsb0JBQW9CLENBQzlDdkQsU0FDQUcsVUFBVUUsU0FBUyxJQUNuQixJQUNBTCxTQUNBRyxVQUFVRSxTQUFTLElBQ25CO1FBR0ZpRCxnQkFBZ0JyQyxZQUFZLENBQUMsR0FBRztRQUNoQ3FDLGdCQUFnQnJDLFlBQVksQ0FBQyxLQUFLO1FBQ2xDcUMsZ0JBQWdCckMsWUFBWSxDQUFDLEdBQUc7UUFFaENyQixJQUFJK0MsV0FBVyxHQUFHO1FBQ2xCL0MsSUFBSWdELFVBQVUsR0FBRztRQUNqQmhELElBQUlpRCxhQUFhLEdBQUc7UUFDcEJqRCxJQUFJa0QsYUFBYSxHQUFHO1FBRXBCbEQsSUFBSThCLFNBQVMsR0FBRzRCO1FBQ2hCMUQsSUFBSStCLElBQUk7UUFFUi9CLElBQUl5QyxXQUFXLEdBQUc7UUFDbEJ6QyxJQUFJMEMsU0FBUyxHQUFHO1FBQ2hCMUMsSUFBSTJDLE1BQU07SUFDWjtJQUVBLE1BQU03QyxPQUFPLENBQUN4QjtRQUNaLElBQUlJLFlBQVk7UUFFaEJDLGNBQWM7UUFDZFA7UUFFQSxJQUFJd0YsZ0JBQWdCaEY7UUFDcEIsSUFBSWUsaUJBQWlCQyx3QkFBd0J0QjtRQUM3QyxJQUFJdUY7UUFFSixNQUFNQyxVQUFVLENBQUNDO1lBQ2YsSUFBSSxDQUFDRixvQkFBb0JBLHFCQUFxQkU7WUFDOUMsTUFBTUMsVUFBVUQsY0FBY0Y7WUFDOUIsTUFBTUksV0FBVztZQUVqQixJQUFJRCxVQUFVQyxVQUFVO2dCQUN0QixNQUFNQyxXQUFXRixVQUFVQztnQkFDM0IsTUFBTUUsZUFBZUMsZUFBZUY7Z0JBQ3BDLE1BQU1HLGFBQ0pULGdCQUFnQixDQUFDakUsaUJBQWlCaUUsYUFBWSxJQUFLTztnQkFFckR0RixtQkFBbUJ3RjtnQkFDbkJDLHNCQUFzQlI7WUFDeEIsT0FBTztnQkFDTGpGLG1CQUFtQmM7Z0JBQ25CaEIsY0FBYztnQkFDZE47WUFDRjtRQUNGO1FBRUFpRyxzQkFBc0JSO0lBQ3hCO0lBRUEsTUFBTWxFLDBCQUEwQixDQUFDdEI7UUFDL0IsTUFBTTZELGFBQWEsTUFBTWhFLFFBQVF1QixNQUFNO1FBQ3ZDLE1BQU02RSxZQUFZcEMsYUFBYTtRQUMvQixNQUFNcUMsZUFBZTlELEtBQUsrRCxNQUFNLEtBQUtGLFlBQVlBLFlBQVk7UUFFN0QsTUFBTUcsY0FDSnBHLGNBQWM2RCxhQUFhQSxhQUFhLElBQUlxQztRQUU5QyxNQUFNRyxhQUFhLEtBQUs7UUFFeEIsT0FBT0EsYUFBYyxPQUFNRCxXQUFVO0lBQ3ZDO0lBRUEsTUFBTU4saUJBQWlCLENBQUNRO1FBQ3RCLE1BQU1DLG1CQUFtQjtRQUN6QixPQUFPLElBQUluRSxLQUFLb0UsR0FBRyxDQUFDLElBQUlGLElBQUlDLGtCQUFrQjtJQUNoRDtJQUVBLHFCQUNFLDhEQUFDRTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDM0U7Z0JBQ0M0RSxLQUFLeEc7Z0JBQ0x1RyxXQUFVO2dCQUNWMUUsT0FBTTtnQkFDTkUsUUFBTzs7Ozs7Ozs7Ozs7Ozs7OztBQUtqQjtBQUVBLGlFQUFldEMsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvU3BpbndoZWVsLnRzeD84MDQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IEx1Y2t5ZHJhd1Jld2FyZCB9IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuXHJcbmludGVyZmFjZSBTcGlud2hlZWxQcm9wcyB7XHJcbiAgcmV3YXJkczogTHVja3lkcmF3UmV3YXJkW107XHJcbiAgb25TcGluU3RhcnQ6ICgpID0+IHZvaWQ7XHJcbiAgb25TcGluRW5kOiAoKSA9PiB2b2lkO1xyXG4gIHJlc3VsdEluZGV4OiBudW1iZXIgfCBudWxsO1xyXG4gIGNlbnRlckltYWdlPzogc3RyaW5nIHwgbnVsbDtcclxuICBpbml0aWFsUG9zaXRpb24/OiBudW1iZXIgfCBudWxsO1xyXG59XHJcblxyXG5jb25zdCBTcGlud2hlZWw6IFJlYWN0LkZDPFNwaW53aGVlbFByb3BzPiA9ICh7XHJcbiAgcmV3YXJkcyxcclxuICBvblNwaW5TdGFydCxcclxuICBvblNwaW5FbmQsXHJcbiAgcmVzdWx0SW5kZXgsXHJcbiAgY2VudGVySW1hZ2UgPSAnL2FuZHJvaWQtY2hyb21lLTUxMng1MTIucG5nJyxcclxuICBpbml0aWFsUG9zaXRpb24gPSBudWxsLFxyXG59KSA9PiB7XHJcbiAgY29uc3QgY2FudmFzUmVmID0gdXNlUmVmPEhUTUxDYW52YXNFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBbaXNTcGlubmluZywgc2V0SXNTcGlubmluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2N1cnJlbnRSb3RhdGlvbiwgc2V0Q3VycmVudFJvdGF0aW9uXSA9IHVzZVN0YXRlKDApO1xyXG4gIGNvbnN0IFtpbWFnZUxvYWRlZCwgc2V0SW1hZ2VMb2FkZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IGNlbnRlckltYWdlUmVmID0gdXNlUmVmPEhUTUxJbWFnZUVsZW1lbnQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBpbml0aWFsUG9zaXRpb25BcHBsaWVkID0gdXNlUmVmKGZhbHNlKTtcclxuXHJcbiAgY29uc3QgY29sb3JQYWlycyA9IFtcclxuICAgIHsgc3RhcnQ6ICcjZGMyNjI2JywgZW5kOiAnIzdmMWQxZCcgfSwgLy8gcmVkXHJcbiAgICB7IHN0YXJ0OiAnIzI1NjNlYicsIGVuZDogJyMxZTNhOGEnIH0sIC8vIGJsdWVcclxuICAgIHsgc3RhcnQ6ICcjMTZhMzRhJywgZW5kOiAnIzE0NTMyZCcgfSwgLy8gZ3JlZW5cclxuICAgIHsgc3RhcnQ6ICcjN2MzYWVkJywgZW5kOiAnIzRjMWQ5NScgfSwgLy8gdmlvbGV0XHJcbiAgICB7IHN0YXJ0OiAnIzY1YTMwZCcsIGVuZDogJyMzNjUzMTQnIH0sIC8vIGxpbWVcclxuICAgIHsgc3RhcnQ6ICcjMGQ5NDg4JywgZW5kOiAnIzEzNGU0YScgfSwgLy8gdGVhbFxyXG4gICAgeyBzdGFydDogJyM0ZjQ2ZTUnLCBlbmQ6ICcjMzEyZTgxJyB9LCAvLyBpbmRpZ29cclxuICAgIHsgc3RhcnQ6ICcjZTExZDQ4JywgZW5kOiAnIzg4MTMzNycgfSwgLy8gcm9zZVxyXG4gICAgeyBzdGFydDogJyNjMDI2ZDMnLCBlbmQ6ICcjNzAxYTc1JyB9LCAvLyBmdWNoc2lhXHJcbiAgICB7IHN0YXJ0OiAnI2VhNTgwYycsIGVuZDogJyM3YzJkMTInIH0sIC8vIG9yYW5nZVxyXG4gIF07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoY2VudGVySW1hZ2UpIHtcclxuICAgICAgY29uc3QgaW1nID0gbmV3IEltYWdlKCk7XHJcbiAgICAgIGltZy5zcmMgPSBjZW50ZXJJbWFnZTtcclxuICAgICAgaW1nLm9ubG9hZCA9ICgpID0+IHtcclxuICAgICAgICBjZW50ZXJJbWFnZVJlZi5jdXJyZW50ID0gaW1nO1xyXG4gICAgICAgIHNldEltYWdlTG9hZGVkKHRydWUpO1xyXG4gICAgICB9O1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY2VudGVySW1hZ2VSZWYuY3VycmVudCA9IG51bGw7XHJcbiAgICB9XHJcbiAgfSwgW2NlbnRlckltYWdlXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoaW5pdGlhbFBvc2l0aW9uICE9PSBudWxsICYmICFpbml0aWFsUG9zaXRpb25BcHBsaWVkLmN1cnJlbnQgJiYgcmV3YXJkcy5sZW5ndGggPiAwICYmIGltYWdlTG9hZGVkKSB7XHJcbiAgICAgIGNvbnN0IHRhcmdldFJvdGF0aW9uID0gY2FsY3VsYXRlVGFyZ2V0Um90YXRpb24oaW5pdGlhbFBvc2l0aW9uKTtcclxuICAgICAgc2V0Q3VycmVudFJvdGF0aW9uKHRhcmdldFJvdGF0aW9uKTtcclxuICAgICAgaW5pdGlhbFBvc2l0aW9uQXBwbGllZC5jdXJyZW50ID0gdHJ1ZTtcclxuICAgIH1cclxuICB9LCBbaW5pdGlhbFBvc2l0aW9uLCByZXdhcmRzLCBpbWFnZUxvYWRlZF0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGltYWdlTG9hZGVkKSB7XHJcbiAgICAgIGRyYXdXaGVlbCgpO1xyXG4gICAgfVxyXG4gIH0sIFtyZXdhcmRzLCBjdXJyZW50Um90YXRpb24sIGltYWdlTG9hZGVkXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAocmVzdWx0SW5kZXggIT09IG51bGwgJiYgIWlzU3Bpbm5pbmcpIHtcclxuICAgICAgc3BpbihyZXN1bHRJbmRleCk7XHJcbiAgICB9XHJcbiAgfSwgW3Jlc3VsdEluZGV4XSk7XHJcblxyXG4gIGNvbnN0IGNyZWF0ZUdyYWRpZW50ID0gKFxyXG4gICAgY3R4OiBDYW52YXNSZW5kZXJpbmdDb250ZXh0MkQsXHJcbiAgICBzdGFydEFuZ2xlOiBudW1iZXIsXHJcbiAgICBlbmRBbmdsZTogbnVtYmVyLFxyXG4gICAgY29sb3JQYWlyOiAodHlwZW9mIGNvbG9yUGFpcnMpWzBdLFxyXG4gICkgPT4ge1xyXG4gICAgY29uc3QgY2VudGVyWCA9IGN0eC5jYW52YXMud2lkdGggLyAyO1xyXG4gICAgY29uc3QgY2VudGVyWSA9IGN0eC5jYW52YXMuaGVpZ2h0IC8gMjtcclxuICAgIGNvbnN0IHJhZGl1cyA9IE1hdGgubWluKGNlbnRlclgsIGNlbnRlclkpIC0gMjA7XHJcblxyXG4gICAgY29uc3QgbWlkQW5nbGUgPSBzdGFydEFuZ2xlICsgKGVuZEFuZ2xlIC0gc3RhcnRBbmdsZSkgLyAyO1xyXG4gICAgY29uc3QgZ3JhZGllbnRTdGFydFggPSBjZW50ZXJYICsgTWF0aC5jb3MobWlkQW5nbGUpICogcmFkaXVzICogMC4zO1xyXG4gICAgY29uc3QgZ3JhZGllbnRTdGFydFkgPSBjZW50ZXJZICsgTWF0aC5zaW4obWlkQW5nbGUpICogcmFkaXVzICogMC4zO1xyXG4gICAgY29uc3QgZ3JhZGllbnRFbmRYID0gY2VudGVyWCArIE1hdGguY29zKG1pZEFuZ2xlKSAqIHJhZGl1cztcclxuICAgIGNvbnN0IGdyYWRpZW50RW5kWSA9IGNlbnRlclkgKyBNYXRoLnNpbihtaWRBbmdsZSkgKiByYWRpdXM7XHJcblxyXG4gICAgY29uc3QgZ3JhZGllbnQgPSBjdHguY3JlYXRlTGluZWFyR3JhZGllbnQoXHJcbiAgICAgIGdyYWRpZW50U3RhcnRYLFxyXG4gICAgICBncmFkaWVudFN0YXJ0WSxcclxuICAgICAgZ3JhZGllbnRFbmRYLFxyXG4gICAgICBncmFkaWVudEVuZFksXHJcbiAgICApO1xyXG4gICAgZ3JhZGllbnQuYWRkQ29sb3JTdG9wKDAsIGNvbG9yUGFpci5zdGFydCk7XHJcbiAgICBncmFkaWVudC5hZGRDb2xvclN0b3AoMSwgY29sb3JQYWlyLmVuZCk7XHJcblxyXG4gICAgcmV0dXJuIGdyYWRpZW50O1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGRyYXdXaGVlbCA9ICgpID0+IHtcclxuICAgIGNvbnN0IGNhbnZhcyA9IGNhbnZhc1JlZi5jdXJyZW50O1xyXG4gICAgaWYgKCFjYW52YXMgfHwgcmV3YXJkcy5sZW5ndGggPT09IDAgfHwgIWNlbnRlckltYWdlUmVmLmN1cnJlbnQpIHJldHVybjtcclxuXHJcbiAgICBjb25zdCBjdHggPSBjYW52YXMuZ2V0Q29udGV4dCgnMmQnKTtcclxuICAgIGlmICghY3R4KSByZXR1cm47XHJcblxyXG4gICAgY2FudmFzLndpZHRoID0gY2FudmFzLmhlaWdodCA9IDgwMDtcclxuICAgIGNvbnN0IGNlbnRlclggPSBjYW52YXMud2lkdGggLyAyO1xyXG4gICAgY29uc3QgY2VudGVyWSA9IGNhbnZhcy5oZWlnaHQgLyAyO1xyXG4gICAgY29uc3QgcmFkaXVzID0gTWF0aC5taW4oY2VudGVyWCwgY2VudGVyWSkgLSA0MDtcclxuXHJcbiAgICBjdHguY2xlYXJSZWN0KDAsIDAsIGNhbnZhcy53aWR0aCwgY2FudmFzLmhlaWdodCk7XHJcblxyXG4gICAgLy8gRHJhdyBvdXRlciByaW5nIHNoYWRvd1xyXG4gICAgY29uc3Qgb3V0ZXJHcmFkaWVudCA9IGN0eC5jcmVhdGVDb25pY0dyYWRpZW50KFxyXG4gICAgICBNYXRoLlBJIC8gMixcclxuICAgICAgY2VudGVyWCxcclxuICAgICAgY2VudGVyWSxcclxuICAgICk7XHJcbiAgICBvdXRlckdyYWRpZW50LmFkZENvbG9yU3RvcCgwLCAnI2MwODRmYycpO1xyXG4gICAgb3V0ZXJHcmFkaWVudC5hZGRDb2xvclN0b3AoMC41LCAnI2Q4YjRmZScpO1xyXG4gICAgb3V0ZXJHcmFkaWVudC5hZGRDb2xvclN0b3AoMSwgJyNjMDg0ZmMnKTtcclxuXHJcbiAgICBjdHguc2F2ZSgpO1xyXG4gICAgY3R4LmJlZ2luUGF0aCgpO1xyXG4gICAgY3R4LmFyYyhjZW50ZXJYLCBjZW50ZXJZLCByYWRpdXMgKyA1LCAwLCAyICogTWF0aC5QSSk7XHJcbiAgICBjdHguZmlsbFN0eWxlID0gb3V0ZXJHcmFkaWVudDtcclxuICAgIGN0eC5maWxsKCk7XHJcbiAgICBjdHgucmVzdG9yZSgpO1xyXG5cclxuICAgIC8vIE1vdmUgdG8gY2VudGVyIGFuZCByb3RhdGVcclxuICAgIGN0eC5zYXZlKCk7XHJcbiAgICBjdHgudHJhbnNsYXRlKGNlbnRlclgsIGNlbnRlclkpO1xyXG4gICAgY3R4LnJvdGF0ZSgoY3VycmVudFJvdGF0aW9uICogTWF0aC5QSSkgLyAxODApO1xyXG4gICAgY3R4LnRyYW5zbGF0ZSgtY2VudGVyWCwgLWNlbnRlclkpO1xyXG5cclxuICAgIGxldCBzdGFydEFuZ2xlID0gLU1hdGguUEkgLyAyO1xyXG4gICAgY29uc3Qgc2xpY2VBbmdsZSA9ICgyICogTWF0aC5QSSkgLyByZXdhcmRzLmxlbmd0aDtcclxuXHJcbiAgICAvLyBEcmF3IHNlZ21lbnRzIGNsb2Nrd2lzZVxyXG4gICAgcmV3YXJkcy5mb3JFYWNoKChyZXdhcmQsIGluZGV4KSA9PiB7XHJcbiAgICAgIGNvbnN0IGVuZEFuZ2xlID0gc3RhcnRBbmdsZSArIHNsaWNlQW5nbGU7XHJcblxyXG4gICAgICBjdHguYmVnaW5QYXRoKCk7XHJcbiAgICAgIGN0eC5tb3ZlVG8oY2VudGVyWCwgY2VudGVyWSk7XHJcbiAgICAgIGN0eC5hcmMoY2VudGVyWCwgY2VudGVyWSwgcmFkaXVzLCBzdGFydEFuZ2xlLCBlbmRBbmdsZSk7XHJcbiAgICAgIGN0eC5jbG9zZVBhdGgoKTtcclxuICAgICAgY3R4LmZpbGxTdHlsZSA9IGNyZWF0ZUdyYWRpZW50KFxyXG4gICAgICAgIGN0eCxcclxuICAgICAgICBzdGFydEFuZ2xlLFxyXG4gICAgICAgIGVuZEFuZ2xlLFxyXG4gICAgICAgIGNvbG9yUGFpcnNbaW5kZXggJSBjb2xvclBhaXJzLmxlbmd0aF0sXHJcbiAgICAgICk7XHJcbiAgICAgIGN0eC5maWxsKCk7XHJcblxyXG4gICAgICBjdHguYmVnaW5QYXRoKCk7XHJcbiAgICAgIGN0eC5tb3ZlVG8oY2VudGVyWCwgY2VudGVyWSk7XHJcbiAgICAgIGN0eC5hcmMoY2VudGVyWCwgY2VudGVyWSwgcmFkaXVzLCBzdGFydEFuZ2xlLCBlbmRBbmdsZSk7XHJcbiAgICAgIGN0eC5zdHJva2VTdHlsZSA9ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMiknO1xyXG4gICAgICBjdHgubGluZVdpZHRoID0gMjtcclxuICAgICAgY3R4LnN0cm9rZSgpO1xyXG5cclxuICAgICAgLy8gRHJhdyB0ZXh0XHJcbiAgICAgIGN0eC5zYXZlKCk7XHJcbiAgICAgIGN0eC50cmFuc2xhdGUoY2VudGVyWCwgY2VudGVyWSk7XHJcbiAgICAgIGN0eC5yb3RhdGUoc3RhcnRBbmdsZSArIHNsaWNlQW5nbGUgLyAyKTtcclxuICAgICAgY3R4LnRleHRBbGlnbiA9ICdyaWdodCc7XHJcbiAgICAgIGN0eC50ZXh0QmFzZWxpbmUgPSAnbWlkZGxlJztcclxuICAgICAgY3R4LmZpbGxTdHlsZSA9ICcjRkZGRkZGJztcclxuICAgICAgY3R4LmZvbnQgPSAnYm9sZCA0MHB4IHNhbnMtc2VyaWYnO1xyXG5cclxuICAgICAgY3R4LnNoYWRvd0NvbG9yID0gJ3JnYmEoMCwgMCwgMCwgMC41KSc7XHJcbiAgICAgIGN0eC5zaGFkb3dCbHVyID0gNDtcclxuICAgICAgY3R4LnNoYWRvd09mZnNldFggPSAyO1xyXG4gICAgICBjdHguc2hhZG93T2Zmc2V0WSA9IDI7XHJcbiAgICAgIGN0eC5maWxsVGV4dChgJHtyZXdhcmQuYW1vdW50fWAsIHJhZGl1cyAtIDYwLCAwKTtcclxuICAgICAgY3R4LnJlc3RvcmUoKTtcclxuXHJcbiAgICAgIHN0YXJ0QW5nbGUgPSBlbmRBbmdsZTtcclxuICAgIH0pO1xyXG5cclxuICAgIGN0eC5yZXN0b3JlKCk7XHJcblxyXG4gICAgLy8gRHJhdyBjZW50ZXIgaW1hZ2Ugd2l0aCBjaXJjdWxhciBtYXNrXHJcbiAgICBjb25zdCBjZW50ZXJSYWRpdXMgPSByYWRpdXMgKiAwLjE1O1xyXG4gICAgY3R4LnNhdmUoKTtcclxuXHJcbiAgICAvLyBDcmVhdGUgY2lyY3VsYXIgY2xpcHBpbmcgcGF0aFxyXG4gICAgY3R4LmJlZ2luUGF0aCgpO1xyXG4gICAgY3R4LmFyYyhjZW50ZXJYLCBjZW50ZXJZLCBjZW50ZXJSYWRpdXMsIDAsIDIgKiBNYXRoLlBJKTtcclxuICAgIGN0eC5jbGlwKCk7XHJcblxyXG4gICAgLy8gRHJhdyBpbWFnZVxyXG4gICAgY29uc3QgaW1hZ2VTaXplID0gY2VudGVyUmFkaXVzICogMjtcclxuICAgIGN0eC5kcmF3SW1hZ2UoXHJcbiAgICAgIGNlbnRlckltYWdlUmVmLmN1cnJlbnQsXHJcbiAgICAgIGNlbnRlclggLSBjZW50ZXJSYWRpdXMsXHJcbiAgICAgIGNlbnRlclkgLSBjZW50ZXJSYWRpdXMsXHJcbiAgICAgIGltYWdlU2l6ZSxcclxuICAgICAgaW1hZ2VTaXplLFxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBEcmF3IGJvcmRlclxyXG4gICAgLy8gY3R4LnN0cm9rZVN0eWxlID0gJyNFNUU3RUInO1xyXG4gICAgLy8gY3R4LmxpbmVXaWR0aCA9IDI7XHJcbiAgICAvLyBjdHguc3Ryb2tlKCk7XHJcblxyXG4gICAgY3R4LnJlc3RvcmUoKTtcclxuXHJcbiAgICAvLyBEcmF3IHBvaW50ZXJcclxuICAgIGN0eC5iZWdpblBhdGgoKTtcclxuXHJcbiAgICBjdHgubW92ZVRvKGNlbnRlclggLSAzMCwgY2VudGVyWSAtIHJhZGl1cyk7XHJcbiAgICBjdHgucXVhZHJhdGljQ3VydmVUbyhcclxuICAgICAgY2VudGVyWCxcclxuICAgICAgY2VudGVyWSAtIHJhZGl1cyAtIDE1LFxyXG4gICAgICBjZW50ZXJYICsgMzAsXHJcbiAgICAgIGNlbnRlclkgLSByYWRpdXMsXHJcbiAgICApO1xyXG4gICAgY3R4LnF1YWRyYXRpY0N1cnZlVG8oXHJcbiAgICAgIGNlbnRlclggKyAxNSxcclxuICAgICAgY2VudGVyWSAtIHJhZGl1cyArIDMwLFxyXG4gICAgICBjZW50ZXJYLFxyXG4gICAgICBjZW50ZXJZIC0gcmFkaXVzICsgNjAsXHJcbiAgICApO1xyXG4gICAgY3R4LnF1YWRyYXRpY0N1cnZlVG8oXHJcbiAgICAgIGNlbnRlclggLSAxNSxcclxuICAgICAgY2VudGVyWSAtIHJhZGl1cyArIDMwLFxyXG4gICAgICBjZW50ZXJYIC0gMzAsXHJcbiAgICAgIGNlbnRlclkgLSByYWRpdXMsXHJcbiAgICApO1xyXG4gICAgY3R4LmNsb3NlUGF0aCgpO1xyXG5cclxuICAgIC8vIENyZWF0ZSBhIHJhZGlhbCBncmFkaWVudCBmb3IgbW9yZSBkZXB0aFxyXG4gICAgY29uc3QgcG9pbnRlckdyYWRpZW50ID0gY3R4LmNyZWF0ZVJhZGlhbEdyYWRpZW50KFxyXG4gICAgICBjZW50ZXJYLFxyXG4gICAgICBjZW50ZXJZIC0gcmFkaXVzICsgMjAsXHJcbiAgICAgIDEwLFxyXG4gICAgICBjZW50ZXJYLFxyXG4gICAgICBjZW50ZXJZIC0gcmFkaXVzICsgMjAsXHJcbiAgICAgIDUwLFxyXG4gICAgKTtcclxuXHJcbiAgICBwb2ludGVyR3JhZGllbnQuYWRkQ29sb3JTdG9wKDAsICcjOTRhM2I4Jyk7XHJcbiAgICBwb2ludGVyR3JhZGllbnQuYWRkQ29sb3JTdG9wKDAuNSwgJyNjYmQ1ZTEnKTtcclxuICAgIHBvaW50ZXJHcmFkaWVudC5hZGRDb2xvclN0b3AoMSwgJyNmOGZhZmMnKTtcclxuXHJcbiAgICBjdHguc2hhZG93Q29sb3IgPSAncmdiYSgwLCAwLCAwLCAwLjUpJztcclxuICAgIGN0eC5zaGFkb3dCbHVyID0gMTA7XHJcbiAgICBjdHguc2hhZG93T2Zmc2V0WCA9IDA7XHJcbiAgICBjdHguc2hhZG93T2Zmc2V0WSA9IDU7XHJcblxyXG4gICAgY3R4LmZpbGxTdHlsZSA9IHBvaW50ZXJHcmFkaWVudDtcclxuICAgIGN0eC5maWxsKCk7XHJcblxyXG4gICAgY3R4LnN0cm9rZVN0eWxlID0gJ3JnYmEoMCwgMCwgMCwgMSknO1xyXG4gICAgY3R4LmxpbmVXaWR0aCA9IDIuNTtcclxuICAgIGN0eC5zdHJva2UoKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBzcGluID0gKHJlc3VsdEluZGV4OiBudW1iZXIpID0+IHtcclxuICAgIGlmIChpc1NwaW5uaW5nKSByZXR1cm47XHJcblxyXG4gICAgc2V0SXNTcGlubmluZyh0cnVlKTtcclxuICAgIG9uU3BpblN0YXJ0KCk7XHJcblxyXG4gICAgbGV0IHN0YXJ0Um90YXRpb24gPSBjdXJyZW50Um90YXRpb247XHJcbiAgICBsZXQgdGFyZ2V0Um90YXRpb24gPSBjYWxjdWxhdGVUYXJnZXRSb3RhdGlvbihyZXN1bHRJbmRleCk7XHJcbiAgICBsZXQgYW5pbWF0aW9uU3RhcnRUaW1lOiBudW1iZXI7XHJcblxyXG4gICAgY29uc3QgYW5pbWF0ZSA9IChjdXJyZW50VGltZTogbnVtYmVyKSA9PiB7XHJcbiAgICAgIGlmICghYW5pbWF0aW9uU3RhcnRUaW1lKSBhbmltYXRpb25TdGFydFRpbWUgPSBjdXJyZW50VGltZTtcclxuICAgICAgY29uc3QgZWxhcHNlZCA9IGN1cnJlbnRUaW1lIC0gYW5pbWF0aW9uU3RhcnRUaW1lO1xyXG4gICAgICBjb25zdCBkdXJhdGlvbiA9IDYwMDA7XHJcblxyXG4gICAgICBpZiAoZWxhcHNlZCA8IGR1cmF0aW9uKSB7XHJcbiAgICAgICAgY29uc3QgcHJvZ3Jlc3MgPSBlbGFwc2VkIC8gZHVyYXRpb247XHJcbiAgICAgICAgY29uc3QgZWFzZVByb2dyZXNzID0gZWFzZU91dFF1YXJ0aWMocHJvZ3Jlc3MpO1xyXG4gICAgICAgIGNvbnN0IGN1cnJlbnRSb3QgPVxyXG4gICAgICAgICAgc3RhcnRSb3RhdGlvbiArICh0YXJnZXRSb3RhdGlvbiAtIHN0YXJ0Um90YXRpb24pICogZWFzZVByb2dyZXNzO1xyXG5cclxuICAgICAgICBzZXRDdXJyZW50Um90YXRpb24oY3VycmVudFJvdCk7XHJcbiAgICAgICAgcmVxdWVzdEFuaW1hdGlvbkZyYW1lKGFuaW1hdGUpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldEN1cnJlbnRSb3RhdGlvbih0YXJnZXRSb3RhdGlvbik7XHJcbiAgICAgICAgc2V0SXNTcGlubmluZyhmYWxzZSk7XHJcbiAgICAgICAgb25TcGluRW5kKCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgcmVxdWVzdEFuaW1hdGlvbkZyYW1lKGFuaW1hdGUpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGNhbGN1bGF0ZVRhcmdldFJvdGF0aW9uID0gKHJlc3VsdEluZGV4OiBudW1iZXIpID0+IHtcclxuICAgIGNvbnN0IHNsaWNlQW5nbGUgPSAzNjAgLyByZXdhcmRzLmxlbmd0aDtcclxuICAgIGNvbnN0IG1heE9mZnNldCA9IHNsaWNlQW5nbGUgKiAwLjU7XHJcbiAgICBjb25zdCByYW5kb21PZmZzZXQgPSBNYXRoLnJhbmRvbSgpICogbWF4T2Zmc2V0IC0gbWF4T2Zmc2V0IC8gMjtcclxuXHJcbiAgICBjb25zdCB0YXJnZXRBbmdsZSA9XHJcbiAgICAgIHJlc3VsdEluZGV4ICogc2xpY2VBbmdsZSArIHNsaWNlQW5nbGUgLyAyICsgcmFuZG9tT2Zmc2V0O1xyXG5cclxuICAgIGNvbnN0IGV4dHJhU3BpbnMgPSAxMyAqIDM2MDtcclxuXHJcbiAgICByZXR1cm4gZXh0cmFTcGlucyArICgzNjAgLSB0YXJnZXRBbmdsZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZWFzZU91dFF1YXJ0aWMgPSAoeDogbnVtYmVyKTogbnVtYmVyID0+IHtcclxuICAgIGNvbnN0IGNvcnJlY3Rpb25GYWN0b3IgPSAwLjAyO1xyXG4gICAgcmV0dXJuIDEgLSBNYXRoLnBvdygxIC0geCArIGNvcnJlY3Rpb25GYWN0b3IsIDQpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC02XCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cclxuICAgICAgICA8Y2FudmFzXHJcbiAgICAgICAgICByZWY9e2NhbnZhc1JlZn1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cInctNjQgaC02NCBtZDp3LTk2IG1kOmgtOTZcIlxyXG4gICAgICAgICAgd2lkdGg9XCI4MDBcIlxyXG4gICAgICAgICAgaGVpZ2h0PVwiODAwXCJcclxuICAgICAgICAvPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBTcGlud2hlZWw7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwiU3BpbndoZWVsIiwicmV3YXJkcyIsIm9uU3BpblN0YXJ0Iiwib25TcGluRW5kIiwicmVzdWx0SW5kZXgiLCJjZW50ZXJJbWFnZSIsImluaXRpYWxQb3NpdGlvbiIsImNhbnZhc1JlZiIsImlzU3Bpbm5pbmciLCJzZXRJc1NwaW5uaW5nIiwiY3VycmVudFJvdGF0aW9uIiwic2V0Q3VycmVudFJvdGF0aW9uIiwiaW1hZ2VMb2FkZWQiLCJzZXRJbWFnZUxvYWRlZCIsImNlbnRlckltYWdlUmVmIiwiaW5pdGlhbFBvc2l0aW9uQXBwbGllZCIsImNvbG9yUGFpcnMiLCJzdGFydCIsImVuZCIsImltZyIsIkltYWdlIiwic3JjIiwib25sb2FkIiwiY3VycmVudCIsImxlbmd0aCIsInRhcmdldFJvdGF0aW9uIiwiY2FsY3VsYXRlVGFyZ2V0Um90YXRpb24iLCJkcmF3V2hlZWwiLCJzcGluIiwiY3JlYXRlR3JhZGllbnQiLCJjdHgiLCJzdGFydEFuZ2xlIiwiZW5kQW5nbGUiLCJjb2xvclBhaXIiLCJjZW50ZXJYIiwiY2FudmFzIiwid2lkdGgiLCJjZW50ZXJZIiwiaGVpZ2h0IiwicmFkaXVzIiwiTWF0aCIsIm1pbiIsIm1pZEFuZ2xlIiwiZ3JhZGllbnRTdGFydFgiLCJjb3MiLCJncmFkaWVudFN0YXJ0WSIsInNpbiIsImdyYWRpZW50RW5kWCIsImdyYWRpZW50RW5kWSIsImdyYWRpZW50IiwiY3JlYXRlTGluZWFyR3JhZGllbnQiLCJhZGRDb2xvclN0b3AiLCJnZXRDb250ZXh0IiwiY2xlYXJSZWN0Iiwib3V0ZXJHcmFkaWVudCIsImNyZWF0ZUNvbmljR3JhZGllbnQiLCJQSSIsInNhdmUiLCJiZWdpblBhdGgiLCJhcmMiLCJmaWxsU3R5bGUiLCJmaWxsIiwicmVzdG9yZSIsInRyYW5zbGF0ZSIsInJvdGF0ZSIsInNsaWNlQW5nbGUiLCJmb3JFYWNoIiwicmV3YXJkIiwiaW5kZXgiLCJtb3ZlVG8iLCJjbG9zZVBhdGgiLCJzdHJva2VTdHlsZSIsImxpbmVXaWR0aCIsInN0cm9rZSIsInRleHRBbGlnbiIsInRleHRCYXNlbGluZSIsImZvbnQiLCJzaGFkb3dDb2xvciIsInNoYWRvd0JsdXIiLCJzaGFkb3dPZmZzZXRYIiwic2hhZG93T2Zmc2V0WSIsImZpbGxUZXh0IiwiYW1vdW50IiwiY2VudGVyUmFkaXVzIiwiY2xpcCIsImltYWdlU2l6ZSIsImRyYXdJbWFnZSIsInF1YWRyYXRpY0N1cnZlVG8iLCJwb2ludGVyR3JhZGllbnQiLCJjcmVhdGVSYWRpYWxHcmFkaWVudCIsInN0YXJ0Um90YXRpb24iLCJhbmltYXRpb25TdGFydFRpbWUiLCJhbmltYXRlIiwiY3VycmVudFRpbWUiLCJlbGFwc2VkIiwiZHVyYXRpb24iLCJwcm9ncmVzcyIsImVhc2VQcm9ncmVzcyIsImVhc2VPdXRRdWFydGljIiwiY3VycmVudFJvdCIsInJlcXVlc3RBbmltYXRpb25GcmFtZSIsIm1heE9mZnNldCIsInJhbmRvbU9mZnNldCIsInJhbmRvbSIsInRhcmdldEFuZ2xlIiwiZXh0cmFTcGlucyIsIngiLCJjb3JyZWN0aW9uRmFjdG9yIiwicG93IiwiZGl2IiwiY2xhc3NOYW1lIiwicmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Spinwheel.tsx\n");

/***/ }),

/***/ "./components/Tasks/Luckydraw/LuckydrawPlayBody.tsx":
/*!**********************************************************!*\
  !*** ./components/Tasks/Luckydraw/LuckydrawPlayBody.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Apps/components/TaskCompletedCard */ \"./components/Tasks/components/TaskCompletedCard.tsx\");\n/* harmony import */ var _luckydraw_gql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./luckydraw.gql */ \"./components/Tasks/Luckydraw/luckydraw.gql.ts\");\n/* harmony import */ var _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/TextEditor */ \"./components/TextEditor.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_TaskToaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TaskToaster */ \"./components/Tasks/components/TaskToaster.tsx\");\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _Components_Spinwheel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @Components/Spinwheel */ \"./components/Spinwheel.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__, _luckydraw_gql__WEBPACK_IMPORTED_MODULE_2__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__, _components_TaskToaster__WEBPACK_IMPORTED_MODULE_5__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_6__]);\n([_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__, _luckydraw_gql__WEBPACK_IMPORTED_MODULE_2__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__, _components_TaskToaster__WEBPACK_IMPORTED_MODULE_5__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst LuckydrawPlayBody = ({ projectEventId, task, verified, onError, scoredPoints, taskParticipation, project })=>{\n    const participationResultIndex = taskParticipation?.info?.resultIndex ?? null;\n    const [isParticipating, setIsParticipating] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [wheelState, setWheelState] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        isSpinning: false,\n        resultIndex: participationResultIndex\n    });\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(verified && !wheelState.isSpinning);\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const { id, description } = task;\n    const taskInfo = task.info;\n    const { rewardType, rewards } = taskInfo;\n    const rewardTypes = rewardType === _airlyft_types__WEBPACK_IMPORTED_MODULE_7__.RewardType.POINTS ? `${globalT(\"projectPoints\")}` : `${globalT(\"projectXp\")}`;\n    const [participateLuckydraw] = (0,_luckydraw_gql__WEBPACK_IMPORTED_MODULE_2__.useParticipateLuckydraw)();\n    const handleSpinComplete = ()=>{\n        setWheelState((prev)=>({\n                ...prev,\n                isSpinning: false\n            }));\n        setShowResult(true);\n        (0,_components_TaskToaster__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n            title: \"Quest Submitted\",\n            defaultText: \"We have verified that you performed the quest\",\n            type: \"success\"\n        });\n        setIsParticipating(false);\n    };\n    const handleClick = async ()=>{\n        if (wheelState.isSpinning) return;\n        try {\n            setWheelState((prev)=>({\n                    ...prev,\n                    isSpinning: true\n                }));\n            setIsParticipating(true);\n            setShowResult(false);\n            const response = await participateLuckydraw({\n                variables: {\n                    eventId: projectEventId,\n                    taskId: id\n                },\n                context: {\n                    rewardType\n                }\n            });\n            setWheelState((prev)=>({\n                    ...prev,\n                    resultIndex: response?.data?.participateLuckydrawTask?.resultIndex ?? 0\n                }));\n        } catch (error) {\n            setWheelState((prev)=>({\n                    ...prev,\n                    isSpinning: false\n                }));\n            setIsParticipating(false);\n            (0,_components_TaskToaster__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n                title: \"Error!\",\n                defaultText: \"Error spinning the wheel!\",\n                type: \"error\",\n                error\n            });\n            onError?.();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                defaultValue: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawPlayBody.tsx\",\n                lineNumber: 101,\n                columnNumber: 23\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Spinwheel__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        rewards: rewards,\n                        onSpinStart: ()=>{\n                            if (isParticipating) {\n                                setWheelState((prev)=>({\n                                        ...prev,\n                                        isSpinning: true\n                                    }));\n                                setShowResult(false);\n                            }\n                        },\n                        onSpinEnd: handleSpinComplete,\n                        resultIndex: isParticipating ? wheelState.resultIndex : null,\n                        initialPosition: verified && !isParticipating ? participationResultIndex : null\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawPlayBody.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    verified && showResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        points: scoredPoints,\n                        title: `Congratulations! You won ${wheelState.resultIndex !== null ? rewards[wheelState.resultIndex].amount : \"failed to fetch\"} ${rewardTypes}.`,\n                        frequency: task.frequency,\n                        status: taskParticipation?.status\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawPlayBody.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        onClick: handleClick,\n                        disabled: wheelState.isSpinning || verified,\n                        loading: wheelState.isSpinning,\n                        block: true,\n                        rounded: \"full\",\n                        children: wheelState.isSpinning ? \"Spinning...\" : \"Spin the Wheel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawPlayBody.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawPlayBody.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LuckydrawPlayBody);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Luckydraw/LuckydrawPlayBody.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/CountDownTimer.tsx":
/*!********************************************************!*\
  !*** ./components/Tasks/components/CountDownTimer.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountdownTimer: () => (/* binding */ CountdownTimer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"moment\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction CountdownTimer({ target }) {\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let intervalId = setInterval(()=>{\n            const now = moment__WEBPACK_IMPORTED_MODULE_2___default()();\n            const duration = moment__WEBPACK_IMPORTED_MODULE_2___default().duration(target.diff(now));\n            const days = Math.floor(duration.asDays());\n            const hours = Math.floor(duration.hours());\n            const minutes = Math.floor(duration.minutes());\n            const seconds = Math.floor(duration.seconds());\n            setTimeLeft(`${days ? days.toString() + \" days \" : \"\"}${hours.toString().padStart(2, \"0\")}:${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`);\n        }, 1000);\n        return ()=>clearInterval(intervalId);\n    }, [\n        target\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: timeLeft\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CountDownTimer.tsx\",\n        lineNumber: 29,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/CountDownTimer.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/TaskCompletedCard.tsx":
/*!***********************************************************!*\
  !*** ./components/Tasks/components/TaskCompletedCard.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/helpers/frequency */ \"./helpers/frequency.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _CountDownTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CountDownTimer */ \"./components/Tasks/components/CountDownTimer.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nconst TaskCompletedCard = ({ points, title, subTitle, frequency, status })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"tasks.completedCard\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const getBackground = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return \"linear-gradient(90deg, #c2410c 0%, #c2640c 50%, #c2800c 100%)\";\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return \"linear-gradient(90deg, #f16363 0%, #f65c5c 50%, #ef4646 100%)\";\n            default:\n                return \"\";\n        }\n    };\n    const getIcon = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Warning, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    function getTitle(title, points, globalT) {\n        if (title) {\n            return title;\n        }\n        if (points) {\n            if ((0,_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__.isFeatureEnabled)(\"POINTS\")) {\n                return `${t(\"title.points\", {\n                    points: points,\n                    projectPoints: globalT(\"projectPoints\")\n                })}`;\n            }\n            return `${t(\"title.noPoints\")}`;\n        }\n        return `${t(\"title.noPoints\")}`;\n    }\n    const getSubTitle = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n                return `${t(\"subtitle.inReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return `${t(\"subtitle.inAIReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return `${t(\"subtitle.valid\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return `${t(\"subtitle.invalid\")}`;\n            default:\n                return `${t(\"subtitle.valid\")}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col p-4 py-6 bg-primary rounded-xl relative overflow-hidden gradient-primary text-primary-foreground shadow\",\n        style: {\n            background: getBackground()\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg  mb-0\",\n                                children: getTitle(title, points, globalT)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: frequency && frequency !== _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.Frequency.NONE ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        \"Resets in \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountDownTimer__WEBPACK_IMPORTED_MODULE_4__.CountdownTimer, {\n                                                target: _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__.frequencyConfig[frequency].cutOff()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : subTitle ? subTitle : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: getSubTitle()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskCompletedCard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/TaskCompletedCard.tsx\n");

/***/ }),

/***/ "./components/TextEditor.tsx":
/*!***********************************!*\
  !*** ./components/TextEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! draft-js */ \"draft-js\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(draft_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! draft-js/dist/Draft.css */ \"./node_modules/draft-js/dist/Draft.css\");\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction TextEditor({ readonly = true, defaultValue, expandable = false, maxHeight = 100, className }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n    const [isOverflow, setIsOverflow] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        const container = ref.current;\n        if (!container || !setIsOverflow) return;\n        if (expandable && container.offsetHeight >= maxHeight) {\n            setIsOverflow(true);\n        }\n    }, [\n        ref,\n        setIsOverflow\n    ]);\n    function Link(props) {\n        const { url } = props.contentState.getEntity(props.entityKey).getData();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: url,\n            target: \"_blank\",\n            rel: \"noreferrer nofollow\",\n            referrerPolicy: \"no-referrer\",\n            style: {\n                color: currentTheme === \"dark\" ? \"#93cefe\" : \"#3b5998\",\n                textDecoration: \"underline\"\n            },\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    function findLinkEntities(contentBlock, callback, contentState) {\n        contentBlock.findEntityRanges((character)=>{\n            const entityKey = character.getEntity();\n            return entityKey !== null && contentState.getEntity(entityKey).getType() === \"LINK\";\n        }, callback);\n    }\n    const decorator = new draft_js__WEBPACK_IMPORTED_MODULE_3__.CompositeDecorator([\n        {\n            strategy: findLinkEntities,\n            component: Link\n        }\n    ]);\n    let editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createEmpty(decorator);\n    if (!defaultValue) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    if (defaultValue) {\n        try {\n            const parsedJson = JSON.parse(defaultValue);\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createWithContent((0,draft_js__WEBPACK_IMPORTED_MODULE_3__.convertFromRaw)(parsedJson), decorator);\n        } catch (err) {\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createWithContent(draft_js__WEBPACK_IMPORTED_MODULE_3__.ContentState.createFromText(defaultValue), decorator);\n        }\n    }\n    if (!editorState.getCurrentContent().hasText()) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            isOverflow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 z-10 h-[40px] w-full bg-gradient-to-t from-background/60 to-background/0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute z-20 flex items-center w-full justify-center -bottom-3`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-2 z-10 rounded-full border bg-background/90 text-cs text-sm font-extrabold\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.CaretUp, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.CaretDown, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`text-base text-ch leading-normal overflow-hidden`, className, isExpanded ? \"mb-10\" : \"\"),\n                ref: ref,\n                style: expandable && !isExpanded ? {\n                    maxHeight,\n                    marginBottom: 0\n                } : {},\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(draft_js__WEBPACK_IMPORTED_MODULE_3__.Editor, {\n                    editorState: editorState,\n                    readOnly: readonly,\n                    onChange: ()=>{}\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TextEditor.tsx\n");

/***/ })

};
;
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Giveaways_AirPoolGiveaway_AirPoolRandomDetails_tsx";
exports.ids = ["components_Giveaways_AirPoolGiveaway_AirPoolRandomDetails_tsx"];
exports.modules = {

/***/ "./components/Web3Wallet lazy recursive ^\\.\\/.*$":
/*!***************************************************************!*\
  !*** ./components/Web3Wallet/ lazy ^\.\/.*$ namespace object ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./Dotsama/DotsamaAccountList": [
		"./components/Web3Wallet/Dotsama/DotsamaAccountList.tsx",
		"components_Web3Wallet_Dotsama_DotsamaAccountList_tsx"
	],
	"./Dotsama/DotsamaAccountList.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaAccountList.tsx",
		"components_Web3Wallet_Dotsama_DotsamaAccountList_tsx"
	],
	"./Dotsama/DotsamaManual": [
		"./components/Web3Wallet/Dotsama/DotsamaManual.tsx",
		"components_Web3Wallet_Dotsama_DotsamaManual_tsx"
	],
	"./Dotsama/DotsamaManual.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaManual.tsx",
		"components_Web3Wallet_Dotsama_DotsamaManual_tsx"
	],
	"./Dotsama/DotsamaNova": [
		"./components/Web3Wallet/Dotsama/DotsamaNova.tsx",
		"components_Web3Wallet_Dotsama_DotsamaNova_tsx"
	],
	"./Dotsama/DotsamaNova.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaNova.tsx",
		"components_Web3Wallet_Dotsama_DotsamaNova_tsx"
	],
	"./Dotsama/DotsamaPolkadotJs": [
		"./components/Web3Wallet/Dotsama/DotsamaPolkadotJs.tsx",
		"components_Web3Wallet_Dotsama_DotsamaPolkadotJs_tsx"
	],
	"./Dotsama/DotsamaPolkadotJs.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaPolkadotJs.tsx",
		"components_Web3Wallet_Dotsama_DotsamaPolkadotJs_tsx"
	],
	"./Dotsama/DotsamaRaw": [
		"./components/Web3Wallet/Dotsama/DotsamaRaw.tsx",
		"components_Web3Wallet_Dotsama_DotsamaRaw_tsx"
	],
	"./Dotsama/DotsamaRaw.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaRaw.tsx",
		"components_Web3Wallet_Dotsama_DotsamaRaw_tsx"
	],
	"./Dotsama/DotsamaSubwallet": [
		"./components/Web3Wallet/Dotsama/DotsamaSubwallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaSubwallet_tsx"
	],
	"./Dotsama/DotsamaSubwallet.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaSubwallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaSubwallet_tsx"
	],
	"./Dotsama/DotsamaTalisman": [
		"./components/Web3Wallet/Dotsama/DotsamaTalisman.tsx",
		"components_Web3Wallet_Dotsama_DotsamaTalisman_tsx"
	],
	"./Dotsama/DotsamaTalisman.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaTalisman.tsx",
		"components_Web3Wallet_Dotsama_DotsamaTalisman_tsx"
	],
	"./Dotsama/DotsamaWallet": [
		"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaWallet_tsx"
	],
	"./Dotsama/DotsamaWallet.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaWallet_tsx"
	],
	"./Dotsama/WalletNotFound": [
		"./components/Web3Wallet/Dotsama/WalletNotFound.tsx",
		"components_Web3Wallet_Dotsama_WalletNotFound_tsx"
	],
	"./Dotsama/WalletNotFound.tsx": [
		"./components/Web3Wallet/Dotsama/WalletNotFound.tsx",
		"components_Web3Wallet_Dotsama_WalletNotFound_tsx"
	],
	"./Evm/EvmManual": [
		"./components/Web3Wallet/Evm/EvmManual.tsx",
		"components_Web3Wallet_Evm_EvmManual_tsx"
	],
	"./Evm/EvmManual.tsx": [
		"./components/Web3Wallet/Evm/EvmManual.tsx",
		"components_Web3Wallet_Evm_EvmManual_tsx"
	],
	"./Evm/EvmMetamask": [
		"./components/Web3Wallet/Evm/EvmMetamask.tsx",
		"components_Web3Wallet_Evm_EvmMetamask_tsx"
	],
	"./Evm/EvmMetamask.tsx": [
		"./components/Web3Wallet/Evm/EvmMetamask.tsx",
		"components_Web3Wallet_Evm_EvmMetamask_tsx"
	],
	"./Evm/EvmSubwallet": [
		"./components/Web3Wallet/Evm/EvmSubwallet.tsx",
		"components_Web3Wallet_Evm_EvmSubwallet_tsx"
	],
	"./Evm/EvmSubwallet.tsx": [
		"./components/Web3Wallet/Evm/EvmSubwallet.tsx",
		"components_Web3Wallet_Evm_EvmSubwallet_tsx"
	],
	"./Evm/EvmTalisman": [
		"./components/Web3Wallet/Evm/EvmTalisman.tsx",
		"components_Web3Wallet_Evm_EvmTalisman_tsx"
	],
	"./Evm/EvmTalisman.tsx": [
		"./components/Web3Wallet/Evm/EvmTalisman.tsx",
		"components_Web3Wallet_Evm_EvmTalisman_tsx"
	],
	"./Evm/EvmWallet": [
		"./components/Web3Wallet/Evm/EvmWallet.tsx",
		"components_Web3Wallet_Evm_EvmWallet_tsx"
	],
	"./Evm/EvmWallet.tsx": [
		"./components/Web3Wallet/Evm/EvmWallet.tsx",
		"components_Web3Wallet_Evm_EvmWallet_tsx"
	],
	"./Evm/EvmWalletConnect": [
		"./components/Web3Wallet/Evm/EvmWalletConnect.tsx",
		"components_Web3Wallet_Evm_EvmWalletConnect_tsx"
	],
	"./Evm/EvmWalletConnect.tsx": [
		"./components/Web3Wallet/Evm/EvmWalletConnect.tsx",
		"components_Web3Wallet_Evm_EvmWalletConnect_tsx"
	],
	"./Evm/GenericInjectedEvm": [
		"./components/Web3Wallet/Evm/GenericInjectedEvm.tsx",
		"components_Web3Wallet_Evm_GenericInjectedEvm_tsx"
	],
	"./Evm/GenericInjectedEvm.tsx": [
		"./components/Web3Wallet/Evm/GenericInjectedEvm.tsx",
		"components_Web3Wallet_Evm_GenericInjectedEvm_tsx"
	],
	"./Web3WalletRenderer": [
		"./components/Web3Wallet/Web3WalletRenderer.tsx"
	],
	"./Web3WalletRenderer.tsx": [
		"./components/Web3Wallet/Web3WalletRenderer.tsx"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "./components/Web3Wallet lazy recursive ^\\.\\/.*$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "./components/AlertBox.tsx":
/*!*********************************!*\
  !*** ./components/AlertBox.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleWarningAlertBox: () => (/* binding */ SimpleWarningAlertBox),\n/* harmony export */   SuccessAlertBox: () => (/* binding */ SuccessAlertBox),\n/* harmony export */   WarningAlertBox: () => (/* binding */ WarningAlertBox),\n/* harmony export */   \"default\": () => (/* binding */ AlertBox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nfunction AlertBox({ title, subtitle, icon, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`flex flex-col p-4 py-8 rounded-2xl relative overflow-hidden text-primary-foreground`, className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg mb-0\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\nconst SimpleWarningAlertBox = ({ title, description })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-100 border border-red-300 text-red-600 px-4 py-3 rounded relative mt-4 space-x-2\",\n        role: \"alert\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                className: \"font-bold\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 51,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block sm:inline\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 52,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined);\nfunction SuccessAlertBox({ title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        className: \"gradient-primary\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n            className: \"h-10 text-primary-foreground\",\n            size: 32\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 66,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\nfunction WarningAlertBox({ title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Info, {\n            fontSize: 28\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 82,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle,\n        className: \"gradient-warning text-white\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AlertBox.tsx\n");

/***/ }),

/***/ "./components/BlockExplorerLink.tsx":
/*!******************************************!*\
  !*** ./components/BlockExplorerLink.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlockExplorerLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction BlockExplorerLink({ hash, blockExplorerUrls }) {\n    return hash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        className: \"underline inline-flex text-sm\",\n        target: \"_blank\",\n        href: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getExplorerLink)(blockExplorerUrls, hash, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ExplorerDataType.TRANSACTION),\n        rel: \"noreferrer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-1 inline-block\",\n                children: \"View on Explorer \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-4 w-4 inline-block\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/BlockExplorerLink.tsx\n");

/***/ }),

/***/ "./components/Giveaways/AirPoolGiveaway/AirPoolDotsamaGiveawayClaim.tsx":
/*!******************************************************************************!*\
  !*** ./components/Giveaways/AirPoolGiveaway/AirPoolDotsamaGiveawayClaim.tsx ***!
  \******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Web3Wallet/Dotsama/DotsamaWallet */ \"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../GiveawayTransactionHash */ \"./components/Giveaways/GiveawayTransactionHash.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./useAirPoolGiveawayClaim */ \"./components/Giveaways/AirPoolGiveaway/useAirPoolGiveawayClaim.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"react-google-recaptcha-v3\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\n/* harmony import */ var _Root_services_tracking__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @Root/services/tracking */ \"./services/tracking.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__, _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__, _useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_10__, _Root_services_tracking__WEBPACK_IMPORTED_MODULE_14__]);\n([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__, _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__, _useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_10__, _Root_services_tracking__WEBPACK_IMPORTED_MODULE_14__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AirPoolDotsamaGiveawayClaim = ({ giveaway, projectEvent, blockchain, asset, amount, airPool })=>{\n    const { claimRewardTrack } = (0,_Root_services_tracking__WEBPACK_IMPORTED_MODULE_14__.useGtmTrack)();\n    const { claim } = (0,_useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_10__.useDotsamaAirPoolGiveawayClaim)(projectEvent.project.id, projectEvent.id, giveaway.id, asset, airPool);\n    const [isClaiming, setIsClaiming] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__.useGoogleReCaptcha)();\n    const { data: userEventRewardsData, loading: isUserEventRewardsLoading } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__.useGetUserEventRewards)(projectEvent.id);\n    const processing = userEventRewardsData?.userEventRewards.find((item)=>item.status === _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.RewardStatus.PROCESSING && item.giveawayId === giveaway.id);\n    const txHash = [\n        ...userEventRewardsData?.userEventRewards || []\n    ].sort((a, b)=>{\n        const dateA = +new Date(a.updatedAt);\n        const dateB = +new Date(b.updatedAt);\n        return dateB - dateA;\n    }).find((reward)=>reward.txHash)?.txHash;\n    const handleSubmit = async (connectorData)=>{\n        setIsClaiming(true);\n        let captcha;\n        if (projectEvent.ipProtect) {\n            if (!executeRecaptcha) {\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                    title: \"Failed\",\n                    text: \"Recaptcha not initialized\",\n                    type: \"error\"\n                });\n                setIsClaiming(false);\n                return;\n            }\n            captcha = await executeRecaptcha(\"airpool_dotsama_giveaway_claim\");\n        }\n        const { account } = connectorData;\n        if (account) {\n            const formattedAddress = (0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__.convertToSs58Address)(account, blockchain.chainId);\n            const formattedConnectorData = {\n                ...connectorData,\n                account: formattedAddress\n            };\n            claim({\n                connectorData: formattedConnectorData,\n                onError: (err)=>{\n                    (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                        title: \"Failed\",\n                        text: err.message,\n                        type: \"error\"\n                    });\n                    setIsClaiming(false);\n                },\n                onSuccess: ()=>{\n                    claimRewardTrack({\n                        projectId: projectEvent.project.id,\n                        eventId: projectEvent.id,\n                        projectTitle: projectEvent.project.name,\n                        eventTitle: projectEvent.title,\n                        giveawayId: giveaway.id,\n                        giveawayTitle: giveaway.title || \"\"\n                    });\n                    (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                        title: \"Submitted\",\n                        text: \"Your claim request has been submitted, check your notifications for an update.\",\n                        type: \"success\"\n                    });\n                    setIsClaiming(false);\n                },\n                captcha\n            });\n        } else {\n            setIsClaiming(false);\n        }\n    };\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation)(\"translation\");\n    if (processing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.SuccessAlertBox, {\n            title: t(\"giveaway.airTokenPool.successTitle\"),\n            subtitle: t(\"giveaway.airTokenPool.successSubtitle\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolDotsamaGiveawayClaim.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!amount.isZero()) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    blockchain: blockchain,\n                    button: {\n                        confirm: {\n                            enable: true,\n                            loading: isClaiming || isUserEventRewardsLoading,\n                            text: `Claim ${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__.formatAmount)(amount?.toString(), asset.decimals)} ${asset.ticker} using `\n                        }\n                    },\n                    onSuccess: handleSubmit,\n                    excludedWallets: [\n                        _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.Web3WalletType.DOTSAMA_MANUAL\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, undefined),\n                projectEvent.ipProtect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__.RecaptchaDeclaration, {\n                    className: \"text-xs text-cs text-center\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolDotsamaGiveawayClaim.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        txHash: txHash,\n        blockchain: blockchain\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolDotsamaGiveawayClaim.tsx\",\n        lineNumber: 165,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AirPoolDotsamaGiveawayClaim);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirPoolGiveaway/AirPoolDotsamaGiveawayClaim.tsx\n");

/***/ }),

/***/ "./components/Giveaways/AirPoolGiveaway/AirPoolEVMGiveawayClaim.tsx":
/*!**************************************************************************!*\
  !*** ./components/Giveaways/AirPoolGiveaway/AirPoolEVMGiveawayClaim.tsx ***!
  \**************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Loaders/ParagraphLoader */ \"./components/Loaders/ParagraphLoader.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_TransactionResult__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/TransactionResult */ \"./components/TransactionResult.tsx\");\n/* harmony import */ var _Components_Web3Wallet_Evm_EvmWallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/Web3Wallet/Evm/EvmWallet */ \"./components/Web3Wallet/Evm/EvmWallet.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _hooks_useGetPoolWindowInfo__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/useGetPoolWindowInfo */ \"./components/Giveaways/hooks/useGetPoolWindowInfo.ts\");\n/* harmony import */ var _useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./useAirPoolGiveawayClaim */ \"./components/Giveaways/AirPoolGiveaway/useAirPoolGiveawayClaim.tsx\");\n/* harmony import */ var _Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @Hooks/useGiveawayTxHash */ \"./hooks/useGiveawayTxHash.ts\");\n/* harmony import */ var _GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../GiveawayTransactionHash */ \"./components/Giveaways/GiveawayTransactionHash.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"react-google-recaptcha-v3\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\n/* harmony import */ var _Root_services_tracking__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @Root/services/tracking */ \"./services/tracking.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__, _Components_Web3Wallet_Evm_EvmWallet__WEBPACK_IMPORTED_MODULE_5__, _useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_11__, _Root_services_tracking__WEBPACK_IMPORTED_MODULE_17__]);\n([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__, _Components_Web3Wallet_Evm_EvmWallet__WEBPACK_IMPORTED_MODULE_5__, _useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_11__, _Root_services_tracking__WEBPACK_IMPORTED_MODULE_17__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AirPoolEVMGiveawayClaim = ({ giveaway, projectEvent, blockchain, asset, amount, airPool })=>{\n    const { claimRewardTrack } = (0,_Root_services_tracking__WEBPACK_IMPORTED_MODULE_17__.useGtmTrack)();\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_15__.useGoogleReCaptcha)();\n    const { txHash: giveawayTxHash } = (0,_Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_12__.useGiveawayTxHash)(giveaway.id);\n    const giveawayInfo = giveaway.info;\n    const [tx, setTx] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)();\n    const [txHash, setTxHash] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)();\n    const { claim, loading: claiming } = (0,_useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_11__.useEvmAirPoolGiveawayClaim)(projectEvent.project.id, projectEvent.id, giveaway.id, blockchain, asset, airPool);\n    const { limitReached, loading: poolLoading } = (0,_hooks_useGetPoolWindowInfo__WEBPACK_IMPORTED_MODULE_10__.useGetPoolWindowInfo)(airPool.contractAddress || \"\", giveaway.id, airPool.poolId || \"\", asset.assetType, ethers__WEBPACK_IMPORTED_MODULE_8__.BigNumber.from(giveawayInfo.amount), amount, blockchain);\n    const handleSubmit = async (connectorData)=>{\n        const gasLess = !!giveaway.gasless;\n        let captcha;\n        if (projectEvent.ipProtect) {\n            if (!executeRecaptcha) {\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n                    title: \"Failed\",\n                    text: \"Recaptcha not initialized\",\n                    type: \"error\"\n                });\n                return;\n            }\n            captcha = await executeRecaptcha(\"airpool_evm_giveaway_claim\");\n        }\n        claim({\n            connectorData,\n            onError: (err)=>{\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n                    title: \"Failed\",\n                    text: err.message,\n                    type: \"error\"\n                });\n            },\n            onSuccess: (data)=>{\n                claimRewardTrack({\n                    projectId: projectEvent.project.id,\n                    eventId: projectEvent.id,\n                    projectTitle: projectEvent.project.name,\n                    eventTitle: projectEvent.title,\n                    giveawayId: giveaway.id,\n                    giveawayTitle: giveaway.title || \"\"\n                });\n                if (gasLess) {\n                    setTxHash(data.txHash);\n                } else {\n                    setTx(data.tx);\n                }\n            },\n            gasLess,\n            captcha\n        });\n    };\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_14__.useTranslation)(\"translation\");\n    if (poolLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"poolgiveawayclaim\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolEVMGiveawayClaim.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolEVMGiveawayClaim.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n    if (!amount.isZero() && limitReached) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.WarningAlertBox, {\n            title: t(\"giveaway.airTokenPool.warningTitle\"),\n            subtitle: t(\"giveaway.airTokenPool.warningPoolSubtitle\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolEVMGiveawayClaim.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!amount.isZero()) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: tx || txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TransactionResult__WEBPACK_IMPORTED_MODULE_4__.TransactionResult, {\n            tx: tx,\n            txHash: txHash,\n            blockchain: blockchain\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolEVMGiveawayClaim.tsx\",\n            lineNumber: 136,\n            columnNumber: 11\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Web3Wallet_Evm_EvmWallet__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    blockchain: blockchain,\n                    size: \"default\",\n                    button: {\n                        confirm: {\n                            enable: true,\n                            loading: claiming,\n                            text: `Claim ${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7__.formatAmount)(amount?.toString(), asset.decimals)} ${asset.ticker} using `\n                        }\n                    },\n                    excludedWallets: giveaway.gasless ? undefined : [\n                        _airlyft_types__WEBPACK_IMPORTED_MODULE_6__.Web3WalletType.EVM_MANUAL\n                    ],\n                    onSuccess: (item)=>handleSubmit(item)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolEVMGiveawayClaim.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 13\n                }, undefined),\n                projectEvent.ipProtect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_16__.RecaptchaDeclaration, {\n                    className: \"text-xs text-cs text-center\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolEVMGiveawayClaim.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 15\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolEVMGiveawayClaim.tsx\",\n            lineNumber: 138,\n            columnNumber: 11\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolEVMGiveawayClaim.tsx\",\n        lineNumber: 134,\n        columnNumber: 7\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        txHash: giveawayTxHash,\n        blockchain: blockchain\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolEVMGiveawayClaim.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AirPoolEVMGiveawayClaim);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirPoolGiveaway/AirPoolEVMGiveawayClaim.tsx\n");

/***/ }),

/***/ "./components/Giveaways/AirPoolGiveaway/AirPoolGiveawayClaim.tsx":
/*!***********************************************************************!*\
  !*** ./components/Giveaways/AirPoolGiveaway/AirPoolGiveawayClaim.tsx ***!
  \***********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AirPoolGiveawayClaim)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _AirPoolDotsamaGiveawayClaim__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AirPoolDotsamaGiveawayClaim */ \"./components/Giveaways/AirPoolGiveaway/AirPoolDotsamaGiveawayClaim.tsx\");\n/* harmony import */ var _AirPoolEVMGiveawayClaim__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AirPoolEVMGiveawayClaim */ \"./components/Giveaways/AirPoolGiveaway/AirPoolEVMGiveawayClaim.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_AirPoolDotsamaGiveawayClaim__WEBPACK_IMPORTED_MODULE_2__, _AirPoolEVMGiveawayClaim__WEBPACK_IMPORTED_MODULE_3__]);\n([_AirPoolDotsamaGiveawayClaim__WEBPACK_IMPORTED_MODULE_2__, _AirPoolEVMGiveawayClaim__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction AirPoolGiveawayClaim({ giveaway, projectEvent, blockchain, asset, amount, airPool }) {\n    if (!blockchain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    switch(blockchain?.type){\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.BlockchainType.EVM:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AirPoolEVMGiveawayClaim__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent,\n                amount: amount,\n                blockchain: blockchain,\n                airPool: airPool,\n                asset: asset\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolGiveawayClaim.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this);\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.BlockchainType.DOTSAMA:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AirPoolDotsamaGiveawayClaim__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent,\n                amount: amount,\n                blockchain: blockchain,\n                airPool: airPool,\n                asset: asset\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolGiveawayClaim.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirPoolGiveaway/AirPoolGiveawayClaim.tsx\n");

/***/ }),

/***/ "./components/Giveaways/AirPoolGiveaway/AirPoolRandomDetails.tsx":
/*!***********************************************************************!*\
  !*** ./components/Giveaways/AirPoolGiveaway/AirPoolRandomDetails.tsx ***!
  \***********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AirTokenRandomEndDetails)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Hooks/useGetBlockchain */ \"./hooks/useGetBlockchain.ts\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_TokenGiveawayDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TokenGiveawayDetails */ \"./components/Giveaways/components/TokenGiveawayDetails.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _AirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AirPoolGiveawayClaim */ \"./components/Giveaways/AirPoolGiveaway/AirPoolGiveawayClaim.tsx\");\n/* harmony import */ var _Distribution_TokenRandomDistribution__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../Distribution/TokenRandomDistribution */ \"./components/Giveaways/Distribution/TokenRandomDistribution.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _components_TokenGiveawayDetails__WEBPACK_IMPORTED_MODULE_5__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_6__, _AirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_7__, _Distribution_TokenRandomDistribution__WEBPACK_IMPORTED_MODULE_8__]);\n([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _components_TokenGiveawayDetails__WEBPACK_IMPORTED_MODULE_5__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_6__, _AirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_7__, _Distribution_TokenRandomDistribution__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nfunction AirTokenRandomEndDetails({ giveaway, projectEvent, expandable, expanded, onClick }) {\n    const giveawayInfo = giveaway.info;\n    const airPool = giveawayInfo?.airPool;\n    const asset = airPool?.asset;\n    const totalAmount = ethers__WEBPACK_IMPORTED_MODULE_3__.BigNumber.from(giveawayInfo.winnerAmount || 0);\n    const { totalClaimable, loading, totalClaimed } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_6__.useGetUserEventRewardsStats)(projectEvent.id, giveaway.id, totalAmount);\n    const { data: blockchainData, loading: blockchainLoading } = (0,_Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_2__.useGetBlockchain)(asset?.blockchainId);\n    const blockchain = blockchainData?.blockchain;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenGiveawayDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        token: asset,\n        blockchain: blockchain,\n        amount: ethers__WEBPACK_IMPORTED_MODULE_3__.BigNumber.from(totalAmount),\n        claimableAmount: totalClaimable,\n        claimedAmount: totalClaimed,\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        title: t(\"giveaway.selectionTypes.random\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Distribution_TokenRandomDistribution__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolRandomDetails.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            blockchain ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent,\n                amount: totalClaimable,\n                blockchain: blockchain,\n                airPool: airPool,\n                asset: asset\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolRandomDetails.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolRandomDetails.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, this),\n            giveaway.lastSettledTime != null && totalClaimable.eq(ethers__WEBPACK_IMPORTED_MODULE_3__.BigNumber.from(0)) && totalClaimed.eq(ethers__WEBPACK_IMPORTED_MODULE_3__.BigNumber.from(0)) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.WarningAlertBox, {\n                title: t(\"giveaway.whitelist.giveawayDetails.notWinner.title\"),\n                subtitle: t(\"giveaway.whitelist.giveawayDetails.notWinner.message\", {\n                    event: globalT(\"event_one\")\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolRandomDetails.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolRandomDetails.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirPoolGiveaway/AirPoolRandomDetails.tsx\n");

/***/ }),

/***/ "./components/Giveaways/AirPoolGiveaway/useAirPoolGiveawayClaim.tsx":
/*!**************************************************************************!*\
  !*** ./components/Giveaways/AirPoolGiveaway/useAirPoolGiveawayClaim.tsx ***!
  \**************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDotsamaAirPoolGiveawayClaim: () => (/* binding */ useDotsamaAirPoolGiveawayClaim),\n/* harmony export */   useEvmAirPoolGiveawayClaim: () => (/* binding */ useEvmAirPoolGiveawayClaim)\n/* harmony export */ });\n/* harmony import */ var _Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @Hooks/useGiveawayTxHash */ \"./hooks/useGiveawayTxHash.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hooks_useGetContract__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useGetContract */ \"./components/Giveaways/hooks/useGetContract.ts\");\n/* harmony import */ var _airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./airpool-giveaway.gql */ \"./components/Giveaways/AirPoolGiveaway/airpool-giveaway.gql.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__]);\n_airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst claimERC721AirToken = (contractAddress, connectorData, rewardCertificate, airPool)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ERC721AirPoolController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    //For EVM case - poolId should always be present\n    if (!airPool.poolId) {\n        throw new Error(\"Invalid Reward Pool ID\");\n    }\n    if (claimIds.length === 1) {\n        return contract.claim(account, airPool.poolId, claimIds[0], amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, airPool.poolId, claimIds, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n};\nconst claimERC1155AirToken = (contractAddress, connectorData, rewardCertificate, airPool)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ERC1155AirPoolController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    if (!airPool.poolId) {\n        throw new Error(\"Invalid Reward Pool ID\");\n    }\n    if (claimIds.length === 1) {\n        return contract.claim(account, airPool.poolId, claimIds[0], amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, airPool.poolId, claimIds, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n};\nconst claimERC20AirToken = (contractAddress, connectorData, rewardCertificate, airPool)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ERC20AirPoolController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    if (!airPool.poolId) {\n        throw new Error(\"Invalid Reward Pool ID\");\n    }\n    if (claimIds.length === 1) {\n        return contract.claim(account, airPool.poolId, claimIds[0], amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, airPool.poolId, claimIds, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n};\nfunction useEvmAirPoolGiveawayClaim(projectId, projectEventId, giveawayId, blockchain, asset, airPool) {\n    const { update } = (0,_Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_0__.useGiveawayTxHash)(giveawayId);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [getAirTokenRewardCertificate] = (0,_airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useGetAirPoolRewardCertificate)();\n    const [sync] = (0,_airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useSyncClaimAirPoolGiveaway)();\n    const assetType = asset?.assetType;\n    const contractType = assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC1155 ? _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.ContractType.ERC1155_AIRPOOL : assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC20 || assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.NATIVE ? _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.ContractType.ERC20_AIRPOOL : assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC721 ? _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.ContractType.ERC721_AIRPOOL : undefined;\n    const { data: contractData, loading: contractLoading } = (0,_hooks_useGetContract__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(blockchain?.id, contractType);\n    const contractAddress = contractData?.contract?.address;\n    const mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        mountedRef.current = true;\n        return ()=>{\n            mountedRef.current = false;\n        };\n    }, []);\n    const claim = async ({ connectorData, onError, onSuccess, gasLess = false, captcha })=>{\n        const { provider, account } = connectorData;\n        if (!gasLess && !provider || !account || !contractAddress || !airPool || !asset) return;\n        mountedRef.current && setLoading(true);\n        try {\n            const { data } = await getAirTokenRewardCertificate({\n                variables: {\n                    projectId,\n                    eventId: projectEventId,\n                    giveawayId,\n                    userAddress: account,\n                    captcha\n                },\n                context: {\n                    gasLess\n                }\n            });\n            if (gasLess) {\n                const txHash = data?.claimAirPoolGiveaway?.txHash;\n                if (!txHash) throw new Error(\"Claim Failed\");\n                update(txHash);\n                onSuccess?.({\n                    contractAddress,\n                    txHash\n                });\n            } else {\n                const result = data?.claimAirPoolGiveaway;\n                if (!result || !result.certificate) throw new Error(\"Invalid certificate\");\n                let tx;\n                if (asset.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC20 || asset.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.NATIVE) {\n                    tx = await claimERC20AirToken(contractAddress, connectorData, result, airPool);\n                } else if (asset.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC1155) {\n                    tx = await claimERC1155AirToken(contractAddress, connectorData, result, airPool);\n                } else if (asset.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC721) {\n                    tx = await claimERC721AirToken(contractAddress, connectorData, result, airPool);\n                } else {\n                    throw new Error(\"Invalid asset\");\n                }\n                update(tx.hash);\n                await tx.wait();\n                await sync({\n                    variables: {\n                        ids: result.raw.map((item)=>item.id),\n                        giveawayId: giveawayId\n                    },\n                    context: {\n                        eventId: projectEventId\n                    }\n                });\n                onSuccess?.({\n                    contractAddress,\n                    tx\n                });\n            }\n        } catch (err) {\n            let error;\n            if (err?.code === \"ACTION_REJECTED\") {\n                error = new Error(\"Tx Signature: User denied transaction signature.\");\n            }\n            onError?.(error || err);\n        } finally{\n            mountedRef.current && setLoading(false);\n        }\n    };\n    return {\n        claim,\n        loading: loading || contractLoading\n    };\n}\nfunction useDotsamaAirPoolGiveawayClaim(projectId, projectEventId, giveawayId, asset, airPool) {\n    const [claimDotsama] = (0,_airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useClaimDotsamaAirPoolGiveaway)();\n    const claim = async ({ connectorData, onError, onSuccess, captcha })=>{\n        const { account } = connectorData;\n        if (!account || !airPool || !asset) return;\n        try {\n            await claimDotsama({\n                variables: {\n                    projectId,\n                    eventId: projectEventId,\n                    giveawayId,\n                    userAddress: account,\n                    captcha\n                }\n            });\n            onSuccess?.();\n        } catch (err) {\n            onError?.(err);\n        }\n    };\n    return {\n        claim\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirPoolGiveaway/useAirPoolGiveawayClaim.tsx\n");

/***/ }),

/***/ "./components/Giveaways/Distribution/TokenRandomDistribution.tsx":
/*!***********************************************************************!*\
  !*** ./components/Giveaways/Distribution/TokenRandomDistribution.tsx ***!
  \***********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _Components_Paragraph__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Paragraph */ \"./components/Paragraph.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _Root_utils_date_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/utils/date-utils */ \"./utils/date-utils.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst RandomDistribution = ({ projectEvent, giveaway })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\");\n    let isResultsAnnounced = false;\n    let distributionDate = projectEvent.endTime;\n    if (giveaway.distributionType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.DistributionType.RANDOM_SPECIFIC_END) {\n        distributionDate = giveaway.startTime;\n        isResultsAnnounced = new Date() > new Date(distributionDate);\n    }\n    const getDescription = ()=>{\n        if (isResultsAnnounced) {\n            return `${t(\"giveaway.distribution.descriptionRandomResults\")} ${(0,_Root_utils_date_utils__WEBPACK_IMPORTED_MODULE_4__.formatHumanize)(distributionDate)}.`;\n        }\n        if (!distributionDate) {\n            return `${t(\"giveaway.distribution.descriptionRandom\")} ${t(\"giveaway.distribution.descriptionRandomLater\")}.`;\n        }\n        return `${t(\"giveaway.distribution.descriptionRandom\")} ${(0,_Root_utils_date_utils__WEBPACK_IMPORTED_MODULE_4__.formatHumanize)(distributionDate)}.`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Paragraph__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Flask, {\n            weight: \"fill\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenRandomDistribution.tsx\",\n            lineNumber: 44,\n            columnNumber: 13\n        }, void 0),\n        title: t(\"giveaway.distribution.title\"),\n        description: getDescription()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenRandomDistribution.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RandomDistribution);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/Distribution/TokenRandomDistribution.tsx\n");

/***/ }),

/***/ "./components/Giveaways/GiveawayTransactionHash.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/GiveawayTransactionHash.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_TransactionResult__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/TransactionResult */ \"./components/TransactionResult.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst GiveawayTransactionHash = ({ txHash, blockchain })=>{\n    if (!txHash) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n        lineNumber: 12,\n        columnNumber: 23\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"!my-0 flex justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TransactionResult__WEBPACK_IMPORTED_MODULE_1__.TransactionResult, {\n            txHash: txHash,\n            blockchain: blockchain\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GiveawayTransactionHash);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9HaXZlYXdheVRyYW5zYWN0aW9uSGFzaC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNrRTtBQUNqQztBQUVqQyxNQUFNRSwwQkFBMEIsQ0FBQyxFQUMvQkMsTUFBTSxFQUNOQyxVQUFVLEVBSVg7SUFDQyxJQUFJLENBQUNELFFBQVEscUJBQU8sOERBQUNGLDJDQUFRQTs7Ozs7SUFDN0IscUJBQ0UsOERBQUNJO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNOLDRFQUFpQkE7WUFBQ0csUUFBUUE7WUFBUUMsWUFBWUE7Ozs7Ozs7Ozs7O0FBR3JEO0FBRUEsaUVBQWVGLHVCQUF1QkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvR2l2ZWF3YXlzL0dpdmVhd2F5VHJhbnNhY3Rpb25IYXNoLnRzeD84NmNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJsb2NrY2hhaW4gfSBmcm9tICdAYWlybHlmdC90eXBlcyc7XHJcbmltcG9ydCB7IFRyYW5zYWN0aW9uUmVzdWx0IH0gZnJvbSAnQENvbXBvbmVudHMvVHJhbnNhY3Rpb25SZXN1bHQnO1xyXG5pbXBvcnQgeyBGcmFnbWVudCB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmNvbnN0IEdpdmVhd2F5VHJhbnNhY3Rpb25IYXNoID0gKHtcclxuICB0eEhhc2gsXHJcbiAgYmxvY2tjaGFpbixcclxufToge1xyXG4gIHR4SGFzaD86IHN0cmluZyB8IG51bGw7XHJcbiAgYmxvY2tjaGFpbjogQmxvY2tjaGFpbjtcclxufSkgPT4ge1xyXG4gIGlmICghdHhIYXNoKSByZXR1cm4gPEZyYWdtZW50IC8+O1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIiFteS0wIGZsZXgganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgPFRyYW5zYWN0aW9uUmVzdWx0IHR4SGFzaD17dHhIYXNofSBibG9ja2NoYWluPXtibG9ja2NoYWlufSAvPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEdpdmVhd2F5VHJhbnNhY3Rpb25IYXNoO1xyXG4iXSwibmFtZXMiOlsiVHJhbnNhY3Rpb25SZXN1bHQiLCJGcmFnbWVudCIsIkdpdmVhd2F5VHJhbnNhY3Rpb25IYXNoIiwidHhIYXNoIiwiYmxvY2tjaGFpbiIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/Giveaways/GiveawayTransactionHash.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/GiveawaySummaryItem.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryItem.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawaySummaryItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction GiveawaySummaryItem({ banner, children, bannerTag, onClick, size = \"default\", actionText, className, actionSuccess }) {\n    const isVideo = banner?.endsWith(\".mp4\") || banner?.endsWith(\".webm\") || banner?.endsWith(\".mov\");\n    const isGif = banner?.endsWith(\".gif\");\n    if (size === \"small\" || size == \"default\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative gap-4 grid grid-cols-[120px_1fr] items-center ${size === \"default\" ? \"sm:grid-cols-[170px_1fr]\" : \"\"} ${className || \"\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            autoPlay: true,\n                            playsInline: true,\n                            loop: true,\n                            muted: true,\n                            className: \"object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: banner,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 200,\n                            width: 200,\n                            src: banner,\n                            className: `object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]`,\n                            alt: \"giveaway-image\",\n                            unoptimized: isGif\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 max-w-sm m-auto\",\n                            children: [\n                                isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    autoPlay: true,\n                                    playsInline: true,\n                                    loop: true,\n                                    muted: true,\n                                    className: \"rounded-xl w-full object-cover\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: banner,\n                                        type: \"video/mp4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    height: 200,\n                                    width: 200,\n                                    src: banner,\n                                    className: \"object-cover w-full aspect-square rounded-xl\",\n                                    alt: \"giveaway-image\",\n                                    loading: \"lazy\",\n                                    unoptimized: isGif\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-1 left-0 w-full flex justify-center\",\n                                    children: bannerTag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-20 gap-2 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: children\n                        }, void 0, false),\n                        actionText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm flex items-center gap-1 text-blue-500 font-medium cursor-pointer\",\n                            onClick: onClick,\n                            children: [\n                                actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                    weight: \"bold\",\n                                    size: 15\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"line-clamp-1 break-all\",\n                                    children: actionText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    \"aria-hidden\": \"true\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute transform-gpu -z-10 rounded-3xl w-full blur-3xl`,\n                        children: isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            autoPlay: true,\n                            playsInline: true,\n                            loop: true,\n                            muted: true,\n                            className: \"rounded-xl w-full object-cover\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: banner,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 400,\n                            width: 400,\n                            src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                            className: `object-cover w-full aspect-square rounded-xl`,\n                            alt: \"giveaway-image\",\n                            loading: \"lazy\",\n                            unoptimized: isGif\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-2 relative z-10\",\n                        children: [\n                            isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                autoPlay: true,\n                                playsInline: true,\n                                loop: true,\n                                muted: true,\n                                className: \"rounded-xl w-full object-cover\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                    src: banner,\n                                    type: \"video/mp4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                height: 400,\n                                width: 400,\n                                src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                                className: `object-cover aspect-square w-full rounded-xl`,\n                                alt: \"giveaway-image\",\n                                loading: \"lazy\",\n                                unoptimized: isGif\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2\",\n                                children: bannerTag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20 space-y-2\",\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"sm\",\n                            rounded: \"full\",\n                            className: \"!font-semibold mt-2\",\n                            block: true,\n                            onClick: onClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                        weight: \"bold\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"line-clamp-1 break-all\",\n                                        children: actionText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryItem.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenAddress.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/components/TokenAddress.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenAddress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction TokenAddress({ token, blockchain, className, showBlockchain = false, addressChars = 3 }) {\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    if (token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AssetType.NATIVE) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex gap-1.5 text-sm text-cl items-center ${className || \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.shortenAddress)(token.address, addressChars)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Copy, {\n                className: \"cursor-pointer text-ch\",\n                size: 16,\n                onClick: (event)=>{\n                    event.stopPropagation();\n                    navigator.clipboard.writeText(token.address);\n                    setCopied(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"inline-flex text-ch\",\n                target: \"_blank\",\n                href: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.address, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.TOKEN),\n                rel: \"noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.ArrowSquareOut, {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this),\n            showBlockchain && blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                src: blockchain?.icon || \"\",\n                height: 20,\n                width: 20,\n                className: \"h-5 w-5\",\n                alt: \"blockchain-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenAddress.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenGiveawayClaimStats.tsx":
/*!*********************************************************************!*\
  !*** ./components/Giveaways/components/TokenGiveawayClaimStats.tsx ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenGiveawayClaimStats: () => (/* binding */ TokenGiveawayClaimStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction TokenGiveawayClaimStats({ totalClaimable, totalClaimed, token, blockchain }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2 text-sm text-ch font-medium \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Coins, {\n                        size: 20,\n                        className: \"flex-shrink-0\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"break-all\",\n                        children: [\n                            (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(totalClaimed?.toString(), token.decimals),\n                            \" \",\n                            token.ticker,\n                            \" Claimed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Coins, {\n                        size: 20,\n                        className: \"flex-shrink-0\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"break-all\",\n                        children: [\n                            (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(totalClaimable?.toString(), token.decimals),\n                            \" \",\n                            token.ticker,\n                            \" Claimable\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9jb21wb25lbnRzL1Rva2VuR2l2ZWF3YXlDbGFpbVN0YXRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQ2lEO0FBQ0g7QUFHdkMsU0FBU0Usd0JBQXdCLEVBQ3RDQyxjQUFjLEVBQ2RDLFlBQVksRUFDWkMsS0FBSyxFQUNMQyxVQUFVLEVBTVg7SUFDQyxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ1Asd0RBQUtBO3dCQUNKUSxNQUFNO3dCQUNORCxXQUFVO3dCQUNWRSxRQUFPOzs7Ozs7a0NBRVQsOERBQUNDO3dCQUFLSCxXQUFVOzs0QkFDYlIsK0RBQVlBLENBQUNJLGNBQWNRLFlBQVlQLE1BQU1RLFFBQVE7NEJBQUc7NEJBQ3hEUixNQUFNUyxNQUFNOzRCQUFDOzs7Ozs7Ozs7Ozs7OzBCQUdsQiw4REFBQ1A7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDUCx3REFBS0E7d0JBQ0pRLE1BQU07d0JBQ05ELFdBQVU7d0JBQ1ZFLFFBQU87Ozs7OztrQ0FFVCw4REFBQ0M7d0JBQUtILFdBQVU7OzRCQUNiUiwrREFBWUEsQ0FBQ0csZ0JBQWdCUyxZQUFZUCxNQUFNUSxRQUFROzRCQUFHOzRCQUMxRFIsTUFBTVMsTUFBTTs0QkFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUt4QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvR2l2ZWF3YXlzL2NvbXBvbmVudHMvVG9rZW5HaXZlYXdheUNsYWltU3RhdHMudHN4P2IxNjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmxvY2tjaGFpbiwgQmxvY2tjaGFpbkFzc2V0IH0gZnJvbSAnQGFpcmx5ZnQvdHlwZXMnO1xyXG5pbXBvcnQgeyBmb3JtYXRBbW91bnQgfSBmcm9tICdAYWlybHlmdC93ZWIzLWV2bSc7XHJcbmltcG9ydCB7IENvaW5zIH0gZnJvbSAnQHBob3NwaG9yLWljb25zL3JlYWN0JztcclxuaW1wb3J0IHsgQmlnTnVtYmVyIH0gZnJvbSAnZXRoZXJzJztcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBUb2tlbkdpdmVhd2F5Q2xhaW1TdGF0cyh7XHJcbiAgdG90YWxDbGFpbWFibGUsXHJcbiAgdG90YWxDbGFpbWVkLFxyXG4gIHRva2VuLFxyXG4gIGJsb2NrY2hhaW4sXHJcbn06IHtcclxuICB0b3RhbENsYWltZWQ6IEJpZ051bWJlcjtcclxuICB0b3RhbENsYWltYWJsZTogQmlnTnVtYmVyO1xyXG4gIHRva2VuOiBCbG9ja2NoYWluQXNzZXQ7XHJcbiAgYmxvY2tjaGFpbjogQmxvY2tjaGFpbiB8IHVuZGVmaW5lZDtcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LXNtIHRleHQtY2ggZm9udC1tZWRpdW0gXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMiBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICA8Q29pbnNcclxuICAgICAgICAgIHNpemU9ezIwfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiXHJcbiAgICAgICAgICB3ZWlnaHQ9XCJkdW90b25lXCJcclxuICAgICAgICAvPlxyXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJyZWFrLWFsbFwiPlxyXG4gICAgICAgICAge2Zvcm1hdEFtb3VudCh0b3RhbENsYWltZWQ/LnRvU3RyaW5nKCksIHRva2VuLmRlY2ltYWxzKX17JyAnfVxyXG4gICAgICAgICAge3Rva2VuLnRpY2tlcn0gQ2xhaW1lZFxyXG4gICAgICAgIDwvc3Bhbj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMiBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICA8Q29pbnNcclxuICAgICAgICAgIHNpemU9ezIwfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiXHJcbiAgICAgICAgICB3ZWlnaHQ9XCJkdW90b25lXCJcclxuICAgICAgICAvPlxyXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJyZWFrLWFsbFwiPlxyXG4gICAgICAgICAge2Zvcm1hdEFtb3VudCh0b3RhbENsYWltYWJsZT8udG9TdHJpbmcoKSwgdG9rZW4uZGVjaW1hbHMpfXsnICd9XHJcbiAgICAgICAgICB7dG9rZW4udGlja2VyfSBDbGFpbWFibGVcclxuICAgICAgICA8L3NwYW4+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiZm9ybWF0QW1vdW50IiwiQ29pbnMiLCJUb2tlbkdpdmVhd2F5Q2xhaW1TdGF0cyIsInRvdGFsQ2xhaW1hYmxlIiwidG90YWxDbGFpbWVkIiwidG9rZW4iLCJibG9ja2NoYWluIiwiZGl2IiwiY2xhc3NOYW1lIiwic2l6ZSIsIndlaWdodCIsInNwYW4iLCJ0b1N0cmluZyIsImRlY2ltYWxzIiwidGlja2VyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenGiveawayClaimStats.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenGiveawayDetails.tsx":
/*!******************************************************************!*\
  !*** ./components/Giveaways/components/TokenGiveawayDetails.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenGiveawayDetails)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Panel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Panel */ \"./components/Panel.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/TokenGiveawayClaimStats */ \"./components/Giveaways/components/TokenGiveawayClaimStats.tsx\");\n/* harmony import */ var _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./GiveawaySummaryItem */ \"./components/Giveaways/components/GiveawaySummaryItem.tsx\");\n/* harmony import */ var _TokenAddress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TokenAddress */ \"./components/Giveaways/components/TokenAddress.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__, _Components_Panel__WEBPACK_IMPORTED_MODULE_3__, _Components_Tag__WEBPACK_IMPORTED_MODULE_4__, _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__, _TokenAddress__WEBPACK_IMPORTED_MODULE_8__]);\n([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__, _Components_Panel__WEBPACK_IMPORTED_MODULE_3__, _Components_Tag__WEBPACK_IMPORTED_MODULE_4__, _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__, _TokenAddress__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction TokenGiveawayDetails({ token, blockchain, amount, claimableAmount, claimedAmount, expandable, expanded, children, onClick, title }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Panel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            banner: token.icon || \"\",\n            className: expanded ? \"!grid-cols-1 sm:!grid-cols-[170px_1fr]\" : \"\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_4__.Tag, {\n                        title: title,\n                        className: \"!inline-flex !text-xs !font-semibold\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: `text-lg text-ch font-semibold break-all`,\n                    children: [\n                        \"Win up to \",\n                        (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(amount?.toString() || \"\", token.decimals),\n                        \" \",\n                        token.ticker,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, void 0),\n                token.address && token.address != ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.constants.AddressZero ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAddress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    token: token,\n                    blockchain: blockchain,\n                    showBlockchain: true,\n                    className: \"mb-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 13\n                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__.TokenGiveawayClaimStats, {\n                    token: token,\n                    totalClaimable: claimableAmount,\n                    totalClaimed: claimedAmount,\n                    blockchain: blockchain\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n            lineNumber: 41,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 relative z-50\",\n            children: [\n                children,\n                amount && claimedAmount.eq(amount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__.SuccessAlertBox, {\n                    title: \"Congrats!\",\n                    subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Your transaction was submitted successfully, sometimes it takes 30-60 seconds for the explorer to index it.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9jb21wb25lbnRzL1Rva2VuR2l2ZWF3YXlEZXRhaWxzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNpRDtBQUNNO0FBQ2pCO0FBQ0E7QUFFSztBQUNxQztBQUN4QjtBQUNkO0FBRTNCLFNBQVNRLHFCQUFxQixFQUMzQ0MsS0FBSyxFQUNMQyxVQUFVLEVBQ1ZDLE1BQU0sRUFDTkMsZUFBZSxFQUNmQyxhQUFhLEVBQ2JDLFVBQVUsRUFDVkMsUUFBUSxFQUNSQyxRQUFRLEVBQ1JDLE9BQU8sRUFDUEMsS0FBSyxFQVlOO0lBQ0MscUJBQ0UsOERBQUNoQix5REFBS0E7UUFDSlksWUFBWUE7UUFDWkMsVUFBVUE7UUFDVkUsU0FBU0E7UUFDVEUsc0JBQ0UsOERBQUNiLDREQUFtQkE7WUFDbEJjLFFBQVFYLE1BQU1ZLElBQUksSUFBSTtZQUN0QkMsV0FBV1AsV0FBVywyQ0FBMkM7OzhCQUVqRSw4REFBQ1E7OEJBQ0MsNEVBQUNwQixnREFBR0E7d0JBQ0ZlLE9BQU9BO3dCQUNQSSxXQUFVOzs7Ozs7Ozs7Ozs4QkFHZCw4REFBQ0U7b0JBQUdGLFdBQVcsQ0FBQyx1Q0FBdUMsQ0FBQzs7d0JBQUU7d0JBQzdDdEIsK0RBQVlBLENBQUNXLFFBQVFjLGNBQWMsSUFBSWhCLE1BQU1pQixRQUFRO3dCQUFHO3dCQUNsRWpCLE1BQU1rQixNQUFNO3dCQUFFOzs7Ozs7O2dCQUVoQmxCLE1BQU1tQixPQUFPLElBQUluQixNQUFNbUIsT0FBTyxJQUFJeEIsMENBQU1BLENBQUN5QixTQUFTLENBQUNDLFdBQVcsaUJBQzdELDhEQUFDdkIscURBQVlBO29CQUNYRSxPQUFPQTtvQkFDUEMsWUFBWUE7b0JBQ1pxQixnQkFBZ0I7b0JBQ2hCVCxXQUFVOzs7OzsyQ0FHWjs4QkFFRiw4REFBQ2pCLHdGQUF1QkE7b0JBQ3RCSSxPQUFPQTtvQkFDUHVCLGdCQUFnQnBCO29CQUNoQnFCLGNBQWNwQjtvQkFDZEgsWUFBWUE7Ozs7Ozs7Ozs7OztrQkFLbEIsNEVBQUNhO1lBQUlELFdBQVU7O2dCQUNaTjtnQkFDQUwsVUFBVUUsY0FBY3FCLEVBQUUsQ0FBQ3ZCLHlCQUMxQiw4REFBQ1YsaUVBQWVBO29CQUNkaUIsT0FBTTtvQkFDTmlCLHdCQUNFLDhEQUFDQztrQ0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVwQiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvR2l2ZWF3YXlzL2NvbXBvbmVudHMvVG9rZW5HaXZlYXdheURldGFpbHMudHN4PzljNWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmxvY2tjaGFpbiwgQmxvY2tjaGFpbkFzc2V0IH0gZnJvbSAnQGFpcmx5ZnQvdHlwZXMnO1xyXG5pbXBvcnQgeyBmb3JtYXRBbW91bnQgfSBmcm9tICdAYWlybHlmdC93ZWIzLWV2bSc7XHJcbmltcG9ydCB7IFN1Y2Nlc3NBbGVydEJveCB9IGZyb20gJ0BDb21wb25lbnRzL0FsZXJ0Qm94JztcclxuaW1wb3J0IFBhbmVsIGZyb20gJ0BDb21wb25lbnRzL1BhbmVsJztcclxuaW1wb3J0IHsgVGFnIH0gZnJvbSAnQENvbXBvbmVudHMvVGFnJztcclxuXHJcbmltcG9ydCB7IEJpZ051bWJlciwgZXRoZXJzIH0gZnJvbSAnZXRoZXJzJztcclxuaW1wb3J0IHsgVG9rZW5HaXZlYXdheUNsYWltU3RhdHMgfSBmcm9tICcuLi9jb21wb25lbnRzL1Rva2VuR2l2ZWF3YXlDbGFpbVN0YXRzJztcclxuaW1wb3J0IEdpdmVhd2F5U3VtbWFyeUl0ZW0gZnJvbSAnLi9HaXZlYXdheVN1bW1hcnlJdGVtJztcclxuaW1wb3J0IFRva2VuQWRkcmVzcyBmcm9tICcuL1Rva2VuQWRkcmVzcyc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUb2tlbkdpdmVhd2F5RGV0YWlscyh7XHJcbiAgdG9rZW4sXHJcbiAgYmxvY2tjaGFpbixcclxuICBhbW91bnQsXHJcbiAgY2xhaW1hYmxlQW1vdW50LFxyXG4gIGNsYWltZWRBbW91bnQsXHJcbiAgZXhwYW5kYWJsZSxcclxuICBleHBhbmRlZCxcclxuICBjaGlsZHJlbixcclxuICBvbkNsaWNrLFxyXG4gIHRpdGxlLFxyXG59OiB7XHJcbiAgdG9rZW46IEJsb2NrY2hhaW5Bc3NldDtcclxuICBibG9ja2NoYWluOiBCbG9ja2NoYWluIHwgdW5kZWZpbmVkO1xyXG4gIGFtb3VudDogQmlnTnVtYmVyIHwgdW5kZWZpbmVkO1xyXG4gIGNsYWltYWJsZUFtb3VudDogQmlnTnVtYmVyO1xyXG4gIGNsYWltZWRBbW91bnQ6IEJpZ051bWJlcjtcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIGV4cGFuZGFibGU/OiBib29sZWFuO1xyXG4gIGV4cGFuZGVkPzogYm9vbGVhbjtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIHwgdW5kZWZpbmVkO1xyXG4gIG9uQ2xpY2s/OiAoKSA9PiB2b2lkO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxQYW5lbFxyXG4gICAgICBleHBhbmRhYmxlPXtleHBhbmRhYmxlfVxyXG4gICAgICBleHBhbmRlZD17ZXhwYW5kZWR9XHJcbiAgICAgIG9uQ2xpY2s9e29uQ2xpY2t9XHJcbiAgICAgIGhlYWRlcj17XHJcbiAgICAgICAgPEdpdmVhd2F5U3VtbWFyeUl0ZW1cclxuICAgICAgICAgIGJhbm5lcj17dG9rZW4uaWNvbiB8fCAnJ31cclxuICAgICAgICAgIGNsYXNzTmFtZT17ZXhwYW5kZWQgPyAnIWdyaWQtY29scy0xIHNtOiFncmlkLWNvbHMtWzE3MHB4XzFmcl0nIDogJyd9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPFRhZ1xyXG4gICAgICAgICAgICAgIHRpdGxlPXt0aXRsZX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCIhaW5saW5lLWZsZXggIXRleHQteHMgIWZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPXtgdGV4dC1sZyB0ZXh0LWNoIGZvbnQtc2VtaWJvbGQgYnJlYWstYWxsYH0+XHJcbiAgICAgICAgICAgIFdpbiB1cCB0byB7Zm9ybWF0QW1vdW50KGFtb3VudD8udG9TdHJpbmcoKSB8fCAnJywgdG9rZW4uZGVjaW1hbHMpfXsnICd9XHJcbiAgICAgICAgICAgIHt0b2tlbi50aWNrZXJ9eycgJ31cclxuICAgICAgICAgIDwvaDI+XHJcbiAgICAgICAgICB7dG9rZW4uYWRkcmVzcyAmJiB0b2tlbi5hZGRyZXNzICE9IGV0aGVycy5jb25zdGFudHMuQWRkcmVzc1plcm8gPyAoXHJcbiAgICAgICAgICAgIDxUb2tlbkFkZHJlc3NcclxuICAgICAgICAgICAgICB0b2tlbj17dG9rZW59XHJcbiAgICAgICAgICAgICAgYmxvY2tjaGFpbj17YmxvY2tjaGFpbn1cclxuICAgICAgICAgICAgICBzaG93QmxvY2tjaGFpbj17dHJ1ZX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYi0xXCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgIDw+PC8+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgICAgPFRva2VuR2l2ZWF3YXlDbGFpbVN0YXRzXHJcbiAgICAgICAgICAgIHRva2VuPXt0b2tlbn1cclxuICAgICAgICAgICAgdG90YWxDbGFpbWFibGU9e2NsYWltYWJsZUFtb3VudH1cclxuICAgICAgICAgICAgdG90YWxDbGFpbWVkPXtjbGFpbWVkQW1vdW50fVxyXG4gICAgICAgICAgICBibG9ja2NoYWluPXtibG9ja2NoYWlufVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L0dpdmVhd2F5U3VtbWFyeUl0ZW0+XHJcbiAgICAgIH1cclxuICAgID5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTYgcmVsYXRpdmUgei01MFwiPlxyXG4gICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICB7YW1vdW50ICYmIGNsYWltZWRBbW91bnQuZXEoYW1vdW50KSAmJiAoXHJcbiAgICAgICAgICA8U3VjY2Vzc0FsZXJ0Qm94XHJcbiAgICAgICAgICAgIHRpdGxlPVwiQ29uZ3JhdHMhXCJcclxuICAgICAgICAgICAgc3VidGl0bGU9e1xyXG4gICAgICAgICAgICAgIDxzcGFuPlxyXG4gICAgICAgICAgICAgICAgWW91ciB0cmFuc2FjdGlvbiB3YXMgc3VibWl0dGVkIHN1Y2Nlc3NmdWxseSwgc29tZXRpbWVzIGl0IHRha2VzXHJcbiAgICAgICAgICAgICAgICAzMC02MCBzZWNvbmRzIGZvciB0aGUgZXhwbG9yZXIgdG8gaW5kZXggaXQuXHJcbiAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9QYW5lbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJmb3JtYXRBbW91bnQiLCJTdWNjZXNzQWxlcnRCb3giLCJQYW5lbCIsIlRhZyIsImV0aGVycyIsIlRva2VuR2l2ZWF3YXlDbGFpbVN0YXRzIiwiR2l2ZWF3YXlTdW1tYXJ5SXRlbSIsIlRva2VuQWRkcmVzcyIsIlRva2VuR2l2ZWF3YXlEZXRhaWxzIiwidG9rZW4iLCJibG9ja2NoYWluIiwiYW1vdW50IiwiY2xhaW1hYmxlQW1vdW50IiwiY2xhaW1lZEFtb3VudCIsImV4cGFuZGFibGUiLCJleHBhbmRlZCIsImNoaWxkcmVuIiwib25DbGljayIsInRpdGxlIiwiaGVhZGVyIiwiYmFubmVyIiwiaWNvbiIsImNsYXNzTmFtZSIsImRpdiIsImgyIiwidG9TdHJpbmciLCJkZWNpbWFscyIsInRpY2tlciIsImFkZHJlc3MiLCJjb25zdGFudHMiLCJBZGRyZXNzWmVybyIsInNob3dCbG9ja2NoYWluIiwidG90YWxDbGFpbWFibGUiLCJ0b3RhbENsYWltZWQiLCJlcSIsInN1YnRpdGxlIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenGiveawayDetails.tsx\n");

/***/ }),

/***/ "./components/Giveaways/hooks/useGetContract.ts":
/*!******************************************************!*\
  !*** ./components/Giveaways/hooks/useGetContract.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useGetContract)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst GET_CONTRACT = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql)`\r\n  query contract($blockchainId: ID!, $contractType: ContractType!) {\r\n    contract(blockchainId: $blockchainId, contractType: $contractType) {\r\n      address\r\n    }\r\n  }\r\n`;\nfunction useGetContract(blockchainId, contractType) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.useQuery)(GET_CONTRACT, {\n        variables: {\n            blockchainId: blockchainId,\n            contractType: contractType\n        },\n        skip: !blockchainId || !contractType\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9ob29rcy91c2VHZXRDb250cmFjdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDK0M7QUFFL0MsTUFBTUUsZUFBZUYsbURBQUcsQ0FBQzs7Ozs7O0FBTXpCLENBQUM7QUFFYyxTQUFTRyxlQUN0QkMsWUFBZ0MsRUFDaENDLFlBQXNDO0lBRXRDLE9BQU9KLHdEQUFRQSxDQUE2Q0MsY0FBYztRQUN4RUksV0FBVztZQUNURixjQUFjQTtZQUNkQyxjQUFjQTtRQUNoQjtRQUNBRSxNQUFNLENBQUNILGdCQUFnQixDQUFDQztJQUMxQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9HaXZlYXdheXMvaG9va3MvdXNlR2V0Q29udHJhY3QudHM/NDJmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDb250cmFjdCwgQ29udHJhY3RUeXBlLCBRdWVyeV9jb250cmFjdEFyZ3MgfSBmcm9tICdAYWlybHlmdC90eXBlcyc7XHJcbmltcG9ydCB7IGdxbCwgdXNlUXVlcnkgfSBmcm9tICdAYXBvbGxvL2NsaWVudCc7XHJcblxyXG5jb25zdCBHRVRfQ09OVFJBQ1QgPSBncWxgXHJcbiAgcXVlcnkgY29udHJhY3QoJGJsb2NrY2hhaW5JZDogSUQhLCAkY29udHJhY3RUeXBlOiBDb250cmFjdFR5cGUhKSB7XHJcbiAgICBjb250cmFjdChibG9ja2NoYWluSWQ6ICRibG9ja2NoYWluSWQsIGNvbnRyYWN0VHlwZTogJGNvbnRyYWN0VHlwZSkge1xyXG4gICAgICBhZGRyZXNzXHJcbiAgICB9XHJcbiAgfVxyXG5gO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlR2V0Q29udHJhY3QoXHJcbiAgYmxvY2tjaGFpbklkOiBzdHJpbmcgfCB1bmRlZmluZWQsXHJcbiAgY29udHJhY3RUeXBlOiBDb250cmFjdFR5cGUgfCB1bmRlZmluZWQsXHJcbikge1xyXG4gIHJldHVybiB1c2VRdWVyeTx7IGNvbnRyYWN0OiBDb250cmFjdCB9LCBRdWVyeV9jb250cmFjdEFyZ3M+KEdFVF9DT05UUkFDVCwge1xyXG4gICAgdmFyaWFibGVzOiB7XHJcbiAgICAgIGJsb2NrY2hhaW5JZDogYmxvY2tjaGFpbklkIGFzIHN0cmluZyxcclxuICAgICAgY29udHJhY3RUeXBlOiBjb250cmFjdFR5cGUgYXMgQ29udHJhY3RUeXBlLFxyXG4gICAgfSxcclxuICAgIHNraXA6ICFibG9ja2NoYWluSWQgfHwgIWNvbnRyYWN0VHlwZSxcclxuICB9KTtcclxufVxyXG4iXSwibmFtZXMiOlsiZ3FsIiwidXNlUXVlcnkiLCJHRVRfQ09OVFJBQ1QiLCJ1c2VHZXRDb250cmFjdCIsImJsb2NrY2hhaW5JZCIsImNvbnRyYWN0VHlwZSIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Giveaways/hooks/useGetContract.ts\n");

/***/ }),

/***/ "./components/Giveaways/hooks/useGetPoolWindowInfo.ts":
/*!************************************************************!*\
  !*** ./components/Giveaways/hooks/useGetPoolWindowInfo.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGetPoolWindowInfo: () => (/* binding */ useGetPoolWindowInfo)\n/* harmony export */ });\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction useGetPoolWindowInfo(contractAddress, giveawayId, poolId, assetType, totalAmount, amount, blockchain) {\n    const { windowsClaimed, loading: windowsLoading } = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0__.useGetBatchWindowClaimed)(blockchain, contractAddress, [\n        giveawayId\n    ]);\n    const { poolInfo, loading: loadingPoolInfo } = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0__.useGetAirPoolInfo)(blockchain, contractAddress, poolId, assetType);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (windowsLoading || loadingPoolInfo) return {\n            limitReached: false,\n            giveawayLiquidityLeft: undefined\n        };\n        const giveawayWindowLeft = totalAmount.sub(windowsClaimed?.[giveawayId] || ethers__WEBPACK_IMPORTED_MODULE_1__.BigNumber.from(0));\n        const giveawayLiquidityLeft = giveawayWindowLeft.lte(poolInfo?.totalLiquidity || giveawayWindowLeft) ? giveawayWindowLeft : poolInfo?.totalLiquidity;\n        if (amount.gt(ethers__WEBPACK_IMPORTED_MODULE_1__.BigNumber.from(0))) {\n            if (amount.gt(giveawayWindowLeft)) return {\n                limitReached: true,\n                giveawayLiquidityLeft\n            };\n            if (poolInfo && amount.gt(poolInfo.totalLiquidity)) return {\n                limitReached: true,\n                giveawayLiquidityLeft\n            };\n            return {\n                limitReached: false,\n                giveawayLiquidityLeft\n            };\n        } else {\n            if (giveawayWindowLeft.lte(ethers__WEBPACK_IMPORTED_MODULE_1__.BigNumber.from(0))) return {\n                limitReached: true,\n                giveawayLiquidityLeft\n            };\n            if (poolInfo && poolInfo.totalLiquidity.lte(ethers__WEBPACK_IMPORTED_MODULE_1__.BigNumber.from(0))) return {\n                limitReached: true,\n                giveawayLiquidityLeft\n            };\n            return {\n                limitReached: false,\n                giveawayLiquidityLeft\n            };\n        }\n    }, [\n        windowsLoading,\n        loadingPoolInfo,\n        totalAmount,\n        giveawayId,\n        amount\n    ]);\n    return {\n        ...stats,\n        poolInfo,\n        loading: windowsLoading || loadingPoolInfo\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/hooks/useGetPoolWindowInfo.ts\n");

/***/ }),

/***/ "./components/Panel.tsx":
/*!******************************!*\
  !*** ./components/Panel.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Panel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__]);\n_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction Panel({ expandable, expanded, header, children, onClick }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`rounded-xl p-4 transition`, expandable ? expanded ? \"cursor-pointer\" : \"hover:bg-foreground/10 cursor-pointer\" : \"\"),\n                onClick: ()=>{\n                    expandable && onClick?.();\n                },\n                children: header\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Panel.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`p-4`, expandable && !expanded ? \"hidden\" : \"\"),\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Panel.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF1QztBQUV4QixTQUFTQyxNQUFNLEVBQzVCQyxVQUFVLEVBQ1ZDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyxRQUFRLEVBQ1JDLE9BQU8sRUFPUjtJQUNDLHFCQUNFOzswQkFDRSw4REFBQ0M7Z0JBQ0NDLFdBQVdSLHFEQUFFQSxDQUNYLENBQUMseUJBQXlCLENBQUMsRUFDM0JFLGFBQ0lDLFdBQ0UsbUJBQ0EsMENBQ0Y7Z0JBRU5HLFNBQVM7b0JBQ1BKLGNBQWNJO2dCQUNoQjswQkFFQ0Y7Ozs7OzswQkFFSCw4REFBQ0c7Z0JBQUlDLFdBQVdSLHFEQUFFQSxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUVFLGNBQWMsQ0FBQ0MsV0FBVyxXQUFXOzBCQUM1REU7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL1BhbmVsLnRzeD9lMDM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNuIH0gZnJvbSAnQFJvb3QvdXRpbHMvdXRpbHMnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFuZWwoe1xyXG4gIGV4cGFuZGFibGUsXHJcbiAgZXhwYW5kZWQsXHJcbiAgaGVhZGVyLFxyXG4gIGNoaWxkcmVuLFxyXG4gIG9uQ2xpY2ssXHJcbn06IHtcclxuICBleHBhbmRhYmxlPzogYm9vbGVhbjtcclxuICBleHBhbmRlZD86IGJvb2xlYW47XHJcbiAgaGVhZGVyOiBSZWFjdC5SZWFjdE5vZGU7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxuICBvbkNsaWNrPzogKCkgPT4gdm9pZDtcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8ZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgIGByb3VuZGVkLXhsIHAtNCB0cmFuc2l0aW9uYCxcclxuICAgICAgICAgIGV4cGFuZGFibGVcclxuICAgICAgICAgICAgPyBleHBhbmRlZFxyXG4gICAgICAgICAgICAgID8gJ2N1cnNvci1wb2ludGVyJ1xyXG4gICAgICAgICAgICAgIDogJ2hvdmVyOmJnLWZvcmVncm91bmQvMTAgY3Vyc29yLXBvaW50ZXInXHJcbiAgICAgICAgICAgIDogJycsXHJcbiAgICAgICAgKX1cclxuICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICBleHBhbmRhYmxlICYmIG9uQ2xpY2s/LigpO1xyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICB7aGVhZGVyfVxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKGBwLTRgLCBleHBhbmRhYmxlICYmICFleHBhbmRlZCA/ICdoaWRkZW4nIDogJycpfT5cclxuICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiY24iLCJQYW5lbCIsImV4cGFuZGFibGUiLCJleHBhbmRlZCIsImhlYWRlciIsImNoaWxkcmVuIiwib25DbGljayIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/Panel.tsx\n");

/***/ }),

/***/ "./components/Paragraph.tsx":
/*!**********************************!*\
  !*** ./components/Paragraph.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Paragraph = ({ icon, title, description })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1 mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 w-4 text-ch\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-bold text-ch\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm break-words text-cs\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Paragraph);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BhcmFncmFwaC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLE1BQU1BLFlBQVksQ0FBQyxFQUNqQkMsSUFBSSxFQUNKQyxLQUFLLEVBQ0xDLFdBQVcsRUFLWjtJQUNDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FBbUJKOzs7Ozs7a0NBQ2xDLDhEQUFDSzt3QkFBR0QsV0FBVTtrQ0FBNkJIOzs7Ozs7Ozs7Ozs7MEJBRTdDLDhEQUFDSztnQkFBRUYsV0FBVTswQkFBK0JGOzs7Ozs7Ozs7Ozs7QUFHbEQ7QUFFQSxpRUFBZUgsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvUGFyYWdyYXBoLnRzeD80YjNkIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFBhcmFncmFwaCA9ICh7XHJcbiAgaWNvbixcclxuICB0aXRsZSxcclxuICBkZXNjcmlwdGlvbixcclxufToge1xyXG4gIGljb246IFJlYWN0LlJlYWN0Tm9kZTtcclxuICB0aXRsZTogc3RyaW5nIHwgUmVhY3QuUmVhY3ROb2RlO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgbWItNFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWNoXCI+e2ljb259PC9kaXY+XHJcbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtY2hcIj57dGl0bGV9PC9oMz5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gYnJlYWstd29yZHMgdGV4dC1jc1wiPntkZXNjcmlwdGlvbn08L3A+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgUGFyYWdyYXBoO1xyXG4iXSwibmFtZXMiOlsiUGFyYWdyYXBoIiwiaWNvbiIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Paragraph.tsx\n");

/***/ }),

/***/ "./components/RecaptchaDeclaration.tsx":
/*!*********************************************!*\
  !*** ./components/RecaptchaDeclaration.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecaptchaDeclaration: () => (/* binding */ RecaptchaDeclaration)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst RecaptchaDeclaration = ({ className = \"text-sm\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: className,\n        children: [\n            \"This site is protected by reCAPTCHA and the Google\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"underline text-link cursor-pointer\",\n                href: \"https://policies.google.com/privacy\",\n                children: \"Privacy Policy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"and\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"underline text-link cursor-pointer\",\n                href: \"https://policies.google.com/terms\",\n                children: \"Terms of Service\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"apply.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JlY2FwdGNoYURlY2xhcmF0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFbkIsTUFBTUMsdUJBQXVCLENBQUMsRUFDbkNDLFlBQVksU0FBUyxFQUd0QjtJQUNDLHFCQUNFLDhEQUFDQztRQUFFRCxXQUFXQTs7WUFBVztZQUM0QjswQkFDbkQsOERBQUNFO2dCQUNDRixXQUFVO2dCQUNWRyxNQUFLOzBCQUNOOzs7Ozs7WUFFSTtZQUFJO1lBQ0w7MEJBQ0osOERBQUNEO2dCQUNDRixXQUFVO2dCQUNWRyxNQUFLOzBCQUNOOzs7Ozs7WUFFSTtZQUFJOzs7Ozs7O0FBSWYsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvUmVjYXB0Y2hhRGVjbGFyYXRpb24udHN4PzBmMWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuXHJcbmV4cG9ydCBjb25zdCBSZWNhcHRjaGFEZWNsYXJhdGlvbiA9ICh7XHJcbiAgY2xhc3NOYW1lID0gJ3RleHQtc20nLFxyXG59OiB7XHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG59KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxwIGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cclxuICAgICAgVGhpcyBzaXRlIGlzIHByb3RlY3RlZCBieSByZUNBUFRDSEEgYW5kIHRoZSBHb29nbGV7JyAnfVxyXG4gICAgICA8YVxyXG4gICAgICAgIGNsYXNzTmFtZT1cInVuZGVybGluZSB0ZXh0LWxpbmsgY3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgIGhyZWY9XCJodHRwczovL3BvbGljaWVzLmdvb2dsZS5jb20vcHJpdmFjeVwiXHJcbiAgICAgID5cclxuICAgICAgICBQcml2YWN5IFBvbGljeVxyXG4gICAgICA8L2E+eycgJ31cclxuICAgICAgYW5keycgJ31cclxuICAgICAgPGFcclxuICAgICAgICBjbGFzc05hbWU9XCJ1bmRlcmxpbmUgdGV4dC1saW5rIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICBocmVmPVwiaHR0cHM6Ly9wb2xpY2llcy5nb29nbGUuY29tL3Rlcm1zXCJcclxuICAgICAgPlxyXG4gICAgICAgIFRlcm1zIG9mIFNlcnZpY2VcclxuICAgICAgPC9hPnsnICd9XHJcbiAgICAgIGFwcGx5LlxyXG4gICAgPC9wPlxyXG4gICk7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlJlY2FwdGNoYURlY2xhcmF0aW9uIiwiY2xhc3NOYW1lIiwicCIsImEiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/RecaptchaDeclaration.tsx\n");

/***/ }),

/***/ "./components/TransactionResult.tsx":
/*!******************************************!*\
  !*** ./components/TransactionResult.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionResult: () => (/* binding */ TransactionResult)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _BlockExplorerLink__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BlockExplorerLink */ \"./components/BlockExplorerLink.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Loaders/SpinLoader */ \"./components/Loaders/SpinLoader.tsx\");\n\n\n\n\nfunction TransactionResult({ blockchain, tx, txHash }) {\n    const [broadcasting, setBroadcasting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let mounted = true;\n        if (!tx || !setBroadcasting || !setError) return;\n        const wait = async ()=>{\n            mounted && setBroadcasting(true);\n            try {\n                await tx.wait();\n            } catch (err) {\n                mounted && setError(true);\n            }\n            mounted && setBroadcasting(false);\n        };\n        wait();\n        return ()=>{\n            mounted = false;\n        };\n    }, [\n        tx,\n        setBroadcasting,\n        setError\n    ]);\n    const hash = tx?.hash ?? txHash;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            broadcasting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-bold\",\n                        children: \"Waiting for transaction to be included in the block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"Transaction failed due to \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 52,\n                columnNumber: 17\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BlockExplorerLink__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                hash: hash,\n                blockExplorerUrls: blockchain.blockExplorerUrls\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1RyYW5zYWN0aW9uUmVzdWx0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFvRDtBQUdSO0FBQ0U7QUFFdkMsU0FBU0ksa0JBQWtCLEVBQ2hDQyxVQUFVLEVBQ1ZDLEVBQUUsRUFDRkMsTUFBTSxFQUtQO0lBQ0MsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR1AsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDUSxPQUFPQyxTQUFTLEdBQUdULCtDQUFRQTtJQUVsQ0QsZ0RBQVNBLENBQUM7UUFDUixJQUFJVyxVQUFVO1FBQ2QsSUFBSSxDQUFDTixNQUFNLENBQUNHLG1CQUFtQixDQUFDRSxVQUFVO1FBRTFDLE1BQU1FLE9BQU87WUFDWEQsV0FBV0gsZ0JBQWdCO1lBQzNCLElBQUk7Z0JBQ0YsTUFBTUgsR0FBR08sSUFBSTtZQUNmLEVBQUUsT0FBT0MsS0FBSztnQkFDWkYsV0FBV0QsU0FBUztZQUN0QjtZQUNBQyxXQUFXSCxnQkFBZ0I7UUFDN0I7UUFFQUk7UUFFQSxPQUFPO1lBQ0xELFVBQVU7UUFDWjtJQUNGLEdBQUc7UUFBQ047UUFBSUc7UUFBaUJFO0tBQVM7SUFFbEMsTUFBTUksT0FBT1QsSUFBSVMsUUFBUVI7SUFFekIscUJBQ0UsOERBQUNTO1FBQUlDLFdBQVU7O1lBQ1pULDhCQUNDLDhEQUFDUTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNkLDJEQUFVQTs7Ozs7a0NBQ1gsOERBQUNlO3dCQUFLRCxXQUFVO2tDQUFvQjs7Ozs7Ozs7Ozs7O1lBS3ZDUCx1QkFBUyw4REFBQ1M7O29CQUFFO29CQUEyQlQ7Ozs7Ozs7WUFDdkNMLDRCQUNDLDhEQUFDTCwwREFBaUJBO2dCQUNoQmUsTUFBTUE7Z0JBQ05LLG1CQUFtQmYsV0FBV2UsaUJBQWlCOzs7Ozs7Ozs7Ozs7QUFLekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL1RyYW5zYWN0aW9uUmVzdWx0LnRzeD9iY2FjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBCbG9ja0V4cGxvcmVyTGluayBmcm9tICcuL0Jsb2NrRXhwbG9yZXJMaW5rJztcclxuaW1wb3J0IHsgQmxvY2tjaGFpbiB9IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuaW1wb3J0IHsgQ29udHJhY3RUcmFuc2FjdGlvbiB9IGZyb20gJ2V0aGVycyc7XHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBTcGluTG9hZGVyIGZyb20gJy4vTG9hZGVycy9TcGluTG9hZGVyJztcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBUcmFuc2FjdGlvblJlc3VsdCh7XHJcbiAgYmxvY2tjaGFpbixcclxuICB0eCxcclxuICB0eEhhc2gsXHJcbn06IHtcclxuICBibG9ja2NoYWluOiBCbG9ja2NoYWluIHwgdW5kZWZpbmVkO1xyXG4gIHR4PzogQ29udHJhY3RUcmFuc2FjdGlvbjtcclxuICB0eEhhc2g/OiBzdHJpbmc7XHJcbn0pIHtcclxuICBjb25zdCBbYnJvYWRjYXN0aW5nLCBzZXRCcm9hZGNhc3RpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8YW55PigpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgbGV0IG1vdW50ZWQgPSB0cnVlO1xyXG4gICAgaWYgKCF0eCB8fCAhc2V0QnJvYWRjYXN0aW5nIHx8ICFzZXRFcnJvcikgcmV0dXJuO1xyXG5cclxuICAgIGNvbnN0IHdhaXQgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIG1vdW50ZWQgJiYgc2V0QnJvYWRjYXN0aW5nKHRydWUpO1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGF3YWl0IHR4LndhaXQoKTtcclxuICAgICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgICAgbW91bnRlZCAmJiBzZXRFcnJvcih0cnVlKTtcclxuICAgICAgfVxyXG4gICAgICBtb3VudGVkICYmIHNldEJyb2FkY2FzdGluZyhmYWxzZSk7XHJcbiAgICB9O1xyXG5cclxuICAgIHdhaXQoKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBtb3VudGVkID0gZmFsc2U7XHJcbiAgICB9O1xyXG4gIH0sIFt0eCwgc2V0QnJvYWRjYXN0aW5nLCBzZXRFcnJvcl0pO1xyXG5cclxuICBjb25zdCBoYXNoID0gdHg/Lmhhc2ggPz8gdHhIYXNoO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAge2Jyb2FkY2FzdGluZyAmJiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgPFNwaW5Mb2FkZXIgLz5cclxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkXCI+XHJcbiAgICAgICAgICAgIFdhaXRpbmcgZm9yIHRyYW5zYWN0aW9uIHRvIGJlIGluY2x1ZGVkIGluIHRoZSBibG9ja1xyXG4gICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG4gICAgICB7ZXJyb3IgJiYgPHA+VHJhbnNhY3Rpb24gZmFpbGVkIGR1ZSB0byB7ZXJyb3J9PC9wPn1cclxuICAgICAge2Jsb2NrY2hhaW4gJiYgKFxyXG4gICAgICAgIDxCbG9ja0V4cGxvcmVyTGlua1xyXG4gICAgICAgICAgaGFzaD17aGFzaH1cclxuICAgICAgICAgIGJsb2NrRXhwbG9yZXJVcmxzPXtibG9ja2NoYWluLmJsb2NrRXhwbG9yZXJVcmxzfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJCbG9ja0V4cGxvcmVyTGluayIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiU3BpbkxvYWRlciIsIlRyYW5zYWN0aW9uUmVzdWx0IiwiYmxvY2tjaGFpbiIsInR4IiwidHhIYXNoIiwiYnJvYWRjYXN0aW5nIiwic2V0QnJvYWRjYXN0aW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsIm1vdW50ZWQiLCJ3YWl0IiwiZXJyIiwiaGFzaCIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJwIiwiYmxvY2tFeHBsb3JlclVybHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/TransactionResult.tsx\n");

/***/ }),

/***/ "./components/Web3Wallet/Dotsama/DotsamaWallet.tsx":
/*!*********************************************************!*\
  !*** ./components/Web3Wallet/Dotsama/DotsamaWallet.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DotsamaWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/DropdownList/DropdownList */ \"./components/DropdownList/DropdownList.tsx\");\n/* harmony import */ var _Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/DropdownList/DropdownListItem */ \"./components/DropdownList/DropdownListItem.tsx\");\n/* harmony import */ var _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tasks/components/AppStoreIconRenderer */ \"./components/Tasks/components/AppStoreIconRenderer.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @polkadot/extension-dapp */ \"@polkadot/extension-dapp\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../Web3WalletRenderer */ \"./components/Web3Wallet/Web3WalletRenderer.tsx\");\n/* harmony import */ var _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @Root/context/AppContext */ \"./context/AppContext.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__, _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__, _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_6__, _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_9__]);\n([_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__, _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__, _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_6__, _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\nfunction DotsamaWallet({ onError, ...props }) {\n    const wallets = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.useWalletStore)((state)=>state.walletCategory.find((category)=>category.categoryType === _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.Web3WalletCategoryType.DOTSAMA))?.wallets || [];\n    const excludedWallets = props.excludedWallets ?? [];\n    const { state: { isWidget } } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_9__.useAppContext)();\n    if (isWidget) {\n        excludedWallets.push(_airlyft_types__WEBPACK_IMPORTED_MODULE_10__.Web3WalletType.DOTSAMA_POLKADOT_JS, _airlyft_types__WEBPACK_IMPORTED_MODULE_10__.Web3WalletType.DOTSAMA_NOVA);\n    }\n    let filteredWallets = wallets;\n    if (excludedWallets?.length) {\n        filteredWallets = wallets.filter((wallet)=>!excludedWallets?.includes(wallet.walletType));\n    }\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(filteredWallets?.[0]);\n    const [injectedExtensions, setInjectedExtensions] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        if (!window || !_polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_6__.isWeb3Injected || !filteredWallets?.length) {\n            return;\n        }\n        const extensions = window.injectedWeb3;\n        setInjectedExtensions(extensions);\n    }, [\n        window,\n        _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_6__.isWeb3Injected,\n        filteredWallets\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-5 mt-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-[90]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    data: filteredWallets,\n                    selected: selected,\n                    onChange: (item)=>setSelected(item),\n                    renderItem: (item, isButton)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: item.title,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                iconKey: item.walletType,\n                                className: \"h-8 w-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 17\n                            }, void 0),\n                            selected: item.walletType === selected?.walletType,\n                            button: isButton\n                        }, item.title, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            selected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                walletType: selected.walletType,\n                categoryType: _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.Web3WalletCategoryType.DOTSAMA,\n                placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                props: {\n                    wallet: selected,\n                    onError: (err)=>{\n                        (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n                            title: \"Error\",\n                            text: err,\n                            type: \"error\"\n                        });\n                        onError?.(err);\n                    },\n                    ...props,\n                    ...selected.config?.name ? {\n                        injectedExtension: injectedExtensions[selected.config.name]\n                    } : {},\n                    excludedWallets\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\n");

/***/ }),

/***/ "./components/Web3Wallet/Evm/EvmWallet.tsx":
/*!*************************************************!*\
  !*** ./components/Web3Wallet/Evm/EvmWallet.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EvmWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/DropdownList/DropdownList */ \"./components/DropdownList/DropdownList.tsx\");\n/* harmony import */ var _Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/DropdownList/DropdownListItem */ \"./components/DropdownList/DropdownListItem.tsx\");\n/* harmony import */ var _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Tasks/components/AppStoreIconRenderer */ \"./components/Tasks/components/AppStoreIconRenderer.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Web3WalletRenderer */ \"./components/Web3Wallet/Web3WalletRenderer.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_1__, _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_3__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_4__]);\n([_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_1__, _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_3__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction EvmWallet({ onError, ...props }) {\n    let wallets = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__.useWalletStore)((state)=>state.walletCategory.find((category)=>category.categoryType === _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__.Web3WalletCategoryType.EVM))?.wallets || [];\n    if (props.excludedWallets?.length) {\n        wallets = wallets.filter((wallet)=>!props.excludedWallets?.includes(wallet.walletType));\n    }\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(wallets?.[0]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-5 mt-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                data: wallets,\n                selected: selected,\n                onChange: (item)=>setSelected(item),\n                renderItem: (item, isButton)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: item.title,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            iconKey: item.walletType,\n                            className: \"h-8 w-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 15\n                        }, void 0),\n                        selected: item.walletType === selected?.walletType,\n                        button: isButton\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            selected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                walletType: selected.walletType,\n                categoryType: _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__.Web3WalletCategoryType.EVM,\n                placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                props: {\n                    wallet: selected,\n                    onError: (err)=>{\n                        (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n                            title: \"Error\",\n                            text: err,\n                            type: \"error\"\n                        });\n                        onError?.(err);\n                    },\n                    ...props\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Evm/EvmWallet.tsx\n");

/***/ }),

/***/ "./components/Web3Wallet/Web3WalletRenderer.tsx":
/*!******************************************************!*\
  !*** ./components/Web3Wallet/Web3WalletRenderer.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WalletRenderer),\n/* harmony export */   rendererPath: () => (/* binding */ rendererPath)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/utils/string-utils */ \"./utils/string-utils.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Loaders/ParagraphLoader */ \"./components/Loaders/ParagraphLoader.tsx\");\n\n\n\n\n\nconst importProvider = (path, placeholder)=>next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__(\"./components/Web3Wallet lazy recursive ^\\\\.\\\\/.*$\")(`./${path}`), {\n        ssr: false,\n        loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n                lineNumber: 20,\n                columnNumber: 20\n            }, undefined)\n    });\nconst rendererPath = (categoryType, walletType)=>{\n    const path = (0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.capitalizeFirstLetter)((0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.camelCased)(categoryType));\n    const fileName = (0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.capitalizeFirstLetter)((0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.camelCased)(walletType));\n    return `${path}/${fileName}`;\n};\nfunction WalletRenderer({ categoryType, walletType, placeholder, props }) {\n    const View = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>importProvider(rendererPath(categoryType, walletType), placeholder), [\n        walletType,\n        categoryType\n    ]);\n    return View ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(View, {\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n        lineNumber: 44,\n        columnNumber: 17\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n        lineNumber: 44,\n        columnNumber: 48\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Web3WalletRenderer.tsx\n");

/***/ }),

/***/ "./hooks/useGetBlockchain.ts":
/*!***********************************!*\
  !*** ./hooks/useGetBlockchain.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGetBlockchain: () => (/* binding */ useGetBlockchain)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst GET_BLOCKCHAIN = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql)`\r\n  query blockchain($id: ID!) {\r\n    blockchain(id: $id) {\r\n      id\r\n      name\r\n      chainId\r\n      icon\r\n      blockExplorerUrls\r\n      rpcUrls\r\n      nativeCurrency\r\n      decimals\r\n      type\r\n    }\r\n  }\r\n`;\nfunction useGetBlockchain(id) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.useQuery)(GET_BLOCKCHAIN, {\n        variables: {\n            id\n        },\n        skip: !id\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VHZXRCbG9ja2NoYWluLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQztBQUcvQyxNQUFNRSxpQkFBaUJGLG1EQUFHLENBQUM7Ozs7Ozs7Ozs7Ozs7O0FBYzNCLENBQUM7QUFFTSxTQUFTRyxpQkFBaUJDLEVBQVU7SUFDekMsT0FBT0gsd0RBQVFBLENBQ2JDLGdCQUNBO1FBQ0VHLFdBQVc7WUFDVEQ7UUFDRjtRQUNBRSxNQUFNLENBQUNGO0lBQ1Q7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2hvb2tzL3VzZUdldEJsb2NrY2hhaW4udHM/ZGMxYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBncWwsIHVzZVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnO1xyXG5pbXBvcnQgeyBCbG9ja2NoYWluLCBRdWVyeV9ibG9ja2NoYWluQXJncyB9IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuXHJcbmNvbnN0IEdFVF9CTE9DS0NIQUlOID0gZ3FsYFxyXG4gIHF1ZXJ5IGJsb2NrY2hhaW4oJGlkOiBJRCEpIHtcclxuICAgIGJsb2NrY2hhaW4oaWQ6ICRpZCkge1xyXG4gICAgICBpZFxyXG4gICAgICBuYW1lXHJcbiAgICAgIGNoYWluSWRcclxuICAgICAgaWNvblxyXG4gICAgICBibG9ja0V4cGxvcmVyVXJsc1xyXG4gICAgICBycGNVcmxzXHJcbiAgICAgIG5hdGl2ZUN1cnJlbmN5XHJcbiAgICAgIGRlY2ltYWxzXHJcbiAgICAgIHR5cGVcclxuICAgIH1cclxuICB9XHJcbmA7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlR2V0QmxvY2tjaGFpbihpZDogc3RyaW5nKSB7XHJcbiAgcmV0dXJuIHVzZVF1ZXJ5PHsgYmxvY2tjaGFpbjogQmxvY2tjaGFpbiB9LCBRdWVyeV9ibG9ja2NoYWluQXJncz4oXHJcbiAgICBHRVRfQkxPQ0tDSEFJTixcclxuICAgIHtcclxuICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgaWQsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNraXA6ICFpZCxcclxuICAgIH0sXHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiZ3FsIiwidXNlUXVlcnkiLCJHRVRfQkxPQ0tDSEFJTiIsInVzZUdldEJsb2NrY2hhaW4iLCJpZCIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./hooks/useGetBlockchain.ts\n");

/***/ }),

/***/ "./hooks/useGiveawayTxHash.ts":
/*!************************************!*\
  !*** ./hooks/useGiveawayTxHash.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGiveawayTxHash: () => (/* binding */ useGiveawayTxHash)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LS_GIVEAWAY_HASH_KEY = \"air_gth\";\nfunction useGiveawayTxHash(id) {\n    const [txHash, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const [initialized, setInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handle = ()=>{\n            if (!initialized) return;\n            setLoading(true);\n            const tx = getAll();\n            setState(tx || \"\");\n            setLoading(false);\n        };\n        addEventListener(\"storage\", handle);\n        return ()=>{\n            removeEventListener(\"storage\", handle);\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setInitialized(false);\n        setLoading(true);\n        const tx = getAll();\n        setState(tx || \"\");\n        setInitialized(true);\n        setLoading(false);\n    }, [\n        id\n    ]);\n    const getAll = ()=>{\n        try {\n            const parsedFs = JSON.parse(localStorage.getItem(LS_GIVEAWAY_HASH_KEY) || \"{}\");\n            return parsedFs[id];\n        } catch (err) {}\n    };\n    const update = (txHash)=>{\n        if (!initialized) return;\n        sync(txHash);\n    };\n    const sync = (txHash)=>{\n        try {\n            const encodedFs = JSON.stringify({\n                ...JSON.parse(localStorage.getItem(LS_GIVEAWAY_HASH_KEY) || \"{}\"),\n                [id]: txHash\n            });\n            localStorage.setItem(LS_GIVEAWAY_HASH_KEY, encodedFs);\n            dispatchEvent(new Event(\"storage\"));\n        } catch (err) {}\n    };\n    return {\n        txHash,\n        initialized,\n        loading,\n        update\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useGiveawayTxHash.ts\n");

/***/ })

};
;
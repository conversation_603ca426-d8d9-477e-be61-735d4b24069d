"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Tasks_Quiz_QuizPlayBody_tsx";
exports.ids = ["components_Tasks_Quiz_QuizPlayBody_tsx"];
exports.modules = {

/***/ "./components/Loaders/SingleDot.tsx":
/*!******************************************!*\
  !*** ./components/Loaders/SingleDot.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-content-loader */ \"react-content-loader\");\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_content_loader__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst SingleDotLoader = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_content_loader__WEBPACK_IMPORTED_MODULE_2___default()), {\n        speed: 2,\n        width: 32,\n        height: 32,\n        viewBox: \"0 0 32 32\",\n        uniqueKey: \"single-dot-loader\",\n        backgroundColor: \"#cdcdcd\",\n        foregroundColor: \"#ddd\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n            cx: \"16\",\n            cy: \"16\",\n            r: \"16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n            lineNumber: 15,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SingleDotLoader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvU2luZ2xlRG90LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUN1QjtBQUVqRCxNQUFNRSxrQkFBa0IsQ0FBQ0Msc0JBQ3ZCLDhEQUFDRiw2REFBYUE7UUFDWkcsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsU0FBUTtRQUNSQyxXQUFVO1FBQ1ZDLGlCQUFnQjtRQUNoQkMsaUJBQWdCO1FBQ2YsR0FBR1AsS0FBSztrQkFFVCw0RUFBQ1E7WUFBT0MsSUFBRztZQUFLQyxJQUFHO1lBQUtDLEdBQUU7Ozs7Ozs7Ozs7O0FBSTlCLGlFQUFlWixlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdC50c3g/YTE5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgQ29udGVudExvYWRlciBmcm9tICdyZWFjdC1jb250ZW50LWxvYWRlcic7XHJcblxyXG5jb25zdCBTaW5nbGVEb3RMb2FkZXIgPSAocHJvcHM6IGFueSkgPT4gKFxyXG4gIDxDb250ZW50TG9hZGVyXHJcbiAgICBzcGVlZD17Mn1cclxuICAgIHdpZHRoPXszMn1cclxuICAgIGhlaWdodD17MzJ9XHJcbiAgICB2aWV3Qm94PVwiMCAwIDMyIDMyXCJcclxuICAgIHVuaXF1ZUtleT1cInNpbmdsZS1kb3QtbG9hZGVyXCJcclxuICAgIGJhY2tncm91bmRDb2xvcj1cIiNjZGNkY2RcIlxyXG4gICAgZm9yZWdyb3VuZENvbG9yPVwiI2RkZFwiXHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgPlxyXG4gICAgPGNpcmNsZSBjeD1cIjE2XCIgY3k9XCIxNlwiIHI9XCIxNlwiIC8+XHJcbiAgPC9Db250ZW50TG9hZGVyPlxyXG4pO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU2luZ2xlRG90TG9hZGVyO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb250ZW50TG9hZGVyIiwiU2luZ2xlRG90TG9hZGVyIiwicHJvcHMiLCJzcGVlZCIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsInVuaXF1ZUtleSIsImJhY2tncm91bmRDb2xvciIsImZvcmVncm91bmRDb2xvciIsImNpcmNsZSIsImN4IiwiY3kiLCJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Loaders/SingleDot.tsx\n");

/***/ }),

/***/ "./components/Patterns/PatternSquareDot.tsx":
/*!**************************************************!*\
  !*** ./components/Patterns/PatternSquareDot.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst PatternSquareDot = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        width: 226,\n        height: 226,\n        viewBox: \"0 0 226 226\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                clipPath: \"url(#a)\",\n                fill: \"#71869d\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M1.9 3.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM26.6 3.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM51.3 3.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM1.9 28.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM26.6 28.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM51.3 28.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM1.9 53.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM26.6 53.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM51.3 53.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM1.9 77.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM26.6 77.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM51.3 77.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM1.9 102.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM26.6 102.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM51.3 102.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM1.9 127.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM26.6 127.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM51.3 127.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM1.9 151.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM26.6 151.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM51.3 151.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM1.9 176.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM26.6 176.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM51.3 176.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM1.9 201.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM26.6 201.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM51.3 201.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM1.9 225.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM26.6 225.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM51.3 225.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM75.9 3.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM100.6 3.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM75.9 28.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM100.6 28.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM75.9 53.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM100.6 53.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM75.9 77.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM100.6 77.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM75.9 102.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM100.6 102.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM75.9 127.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM100.6 127.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM75.9 151.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM100.6 151.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM75.9 176.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM100.6 176.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM75.9 201.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM100.6 201.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM75.9 225.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM100.6 225.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM125.3 3.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM149.9 3.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM174.6 3.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM125.3 28.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM149.9 28.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM174.6 28.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM125.3 53.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM149.9 53.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM174.6 53.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM125.3 77.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM149.9 77.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM174.6 77.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM125.3 102.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM149.9 102.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM174.6 102.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM125.3 127.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM149.9 127.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM174.6 127.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM125.3 151.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM149.9 151.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM174.6 151.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM125.3 176.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM149.9 176.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM174.6 176.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM125.3 201.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM149.9 201.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM174.6 201.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM125.3 225.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM149.9 225.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM174.6 225.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM199.3 3.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM223.9 3.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM199.3 28.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM223.9 28.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM199.3 53.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM223.9 53.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM199.3 77.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM223.9 77.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM199.3 102.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM223.9 102.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM199.3 127.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM223.9 127.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM199.3 151.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM223.9 151.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM199.3 176.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM223.9 176.5a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM199.3 201.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM223.9 201.2a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM199.3 225.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8ZM223.9 225.8a1.9 1.9 0 1 0 0-3.8 1.9 1.9 0 0 0 0 3.8Z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Patterns\\\\PatternSquareDot.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Patterns\\\\PatternSquareDot.tsx\",\n                lineNumber: 12,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"clipPath\", {\n                    id: \"a\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#fff\",\n                        d: \"M0 0h225.9v225.9H0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Patterns\\\\PatternSquareDot.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Patterns\\\\PatternSquareDot.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Patterns\\\\PatternSquareDot.tsx\",\n                lineNumber: 15,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Patterns\\\\PatternSquareDot.tsx\",\n        lineNumber: 4,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PatternSquareDot);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Patterns/PatternSquareDot.tsx\n");

/***/ }),

/***/ "./components/RadialProgress/index.tsx":
/*!*********************************************!*\
  !*** ./components/RadialProgress/index.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RadialProgress: () => (/* binding */ RadialProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst RadialProgress = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/recharts\"), __webpack_require__.e(\"components_RadialProgress_RadialProgress_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! @Components/RadialProgress/RadialProgress */ \"./components/RadialProgress/RadialProgress.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\RadialProgress\\\\index.tsx -> \" + \"@Components/RadialProgress/RadialProgress\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            height: 66,\n            width: 66\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RadialProgress\\\\index.tsx\",\n            lineNumber: 8,\n            columnNumber: 20\n        }, undefined)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTREO0FBQ3pCO0FBRTVCLE1BQU1FLGlCQUFpQkQsbURBQU9BLENBQ25DLElBQU0sMlNBQU87Ozs7OztJQUVYRSxLQUFLO0lBQ0xDLFNBQVMsa0JBQU0sOERBQUNKLHFFQUFlQTtZQUFDSyxRQUFRO1lBQUlDLE9BQU87Ozs7OztHQUVyRCIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvUmFkaWFsUHJvZ3Jlc3MvaW5kZXgudHN4P2ZmMDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFNpbmdsZURvdExvYWRlciBmcm9tICdAQ29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdCc7XHJcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYyc7XHJcblxyXG5leHBvcnQgY29uc3QgUmFkaWFsUHJvZ3Jlc3MgPSBkeW5hbWljKFxyXG4gICgpID0+IGltcG9ydCgnQENvbXBvbmVudHMvUmFkaWFsUHJvZ3Jlc3MvUmFkaWFsUHJvZ3Jlc3MnKSxcclxuICB7XHJcbiAgICBzc3I6IGZhbHNlLFxyXG4gICAgbG9hZGluZzogKCkgPT4gPFNpbmdsZURvdExvYWRlciBoZWlnaHQ9ezY2fSB3aWR0aD17NjZ9IC8+LFxyXG4gIH0sXHJcbik7XHJcbiJdLCJuYW1lcyI6WyJTaW5nbGVEb3RMb2FkZXIiLCJkeW5hbWljIiwiUmFkaWFsUHJvZ3Jlc3MiLCJzc3IiLCJsb2FkaW5nIiwiaGVpZ2h0Iiwid2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/RadialProgress/index.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/QuizPlayBody.tsx":
/*!************************************************!*\
  !*** ./components/Tasks/Quiz/QuizPlayBody.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Patterns_PatternSquareDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Patterns/PatternSquareDot */ \"./components/Patterns/PatternSquareDot.tsx\");\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var _Components_RadialProgress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/RadialProgress */ \"./components/RadialProgress/index.tsx\");\n/* harmony import */ var _Hooks_useRouterQuery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Hooks/useRouterQuery */ \"./hooks/useRouterQuery.ts\");\n/* harmony import */ var _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Hooks/useTaskParticipation */ \"./hooks/useTaskParticipation.ts\");\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-content-loader */ \"react-content-loader\");\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_content_loader__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _QuizPlayBodyRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./QuizPlayBodyRenderer */ \"./components/Tasks/Quiz/QuizPlayBodyRenderer.tsx\");\n/* harmony import */ var _quiz_gql__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./quiz.gql */ \"./components/Tasks/Quiz/quiz.gql.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_5__, _QuizPlayBodyRenderer__WEBPACK_IMPORTED_MODULE_7__, _quiz_gql__WEBPACK_IMPORTED_MODULE_8__]);\n([_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_5__, _QuizPlayBodyRenderer__WEBPACK_IMPORTED_MODULE_7__, _quiz_gql__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\nconst QuizPlayBody = ({ projectEventId, task, onSuccess })=>{\n    const { subTaskStats } = task;\n    const { data: quizChildrenData, loading } = (0,_quiz_gql__WEBPACK_IMPORTED_MODULE_8__.useGetQuizChildren)(task.id);\n    const questions = quizChildrenData?.pTasksByParentId;\n    const { value, clear } = (0,_Hooks_useRouterQuery__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\"question\");\n    const currentQuestionIndex = parseInt(value || \"0\");\n    const currentQuestion = questions ? questions[currentQuestionIndex] : undefined;\n    const { data, loading: participationLoading } = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_5__.useUserTaskParticipationList)(projectEventId);\n    const completedCount = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_5__.useChildCompletedCount)(data?.userTaskParticipation, task?.id);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\", {\n        keyPrefix: \"tasks.quiz\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-1 mt-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Patterns_PatternSquareDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"absolute -top-[8px] left-0 z-0 w-28 h-24\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex relative items-center overflow-hidden bg-primary space-x-4 mb-4 p-4 rounded-2xl z-[99] gradient-primary\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: participationLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    width: 64,\n                                    height: 64\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RadialProgress__WEBPACK_IMPORTED_MODULE_3__.RadialProgress, {\n                                    progress: (completedCount || 0) / (subTaskStats.count || 1) * 100,\n                                    text: `${completedCount || 0}/${subTaskStats.count}`,\n                                    textClass: \"fill-white\",\n                                    radialFill: \"white\",\n                                    bgFill: \"#b1b1b1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined),\n                            currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xl text-primary-foreground mb-1\",\n                                        children: t(\"progressCard.title\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_10__.FeatureGuard, {\n                                        feature: \"POINTS\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-primary-foreground text-opacity-70\",\n                                            children: [\n                                                t(\"progressCard.pre\"),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: currentQuestion.points\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                globalT(\"projectPoints\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_content_loader__WEBPACK_IMPORTED_MODULE_6__.BulletList, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, undefined) : questions && questions.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuizPlayBodyRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onSuccess: onSuccess,\n                projectEventId: projectEventId,\n                task: task,\n                questions: questions,\n                completedCount: completedCount\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n                lineNumber: 83,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBody.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuizPlayBody);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/QuizPlayBody.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/QuizPlayBodyRenderer.tsx":
/*!********************************************************!*\
  !*** ./components/Tasks/Quiz/QuizPlayBodyRenderer.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/TaskToaster */ \"./components/Tasks/components/TaskToaster.tsx\");\n/* harmony import */ var _Hooks_useRouterQuery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Hooks/useRouterQuery */ \"./hooks/useRouterQuery.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_OptionList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/OptionList */ \"./components/Tasks/Quiz/components/OptionList.tsx\");\n/* harmony import */ var _components_Question__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/Question */ \"./components/Tasks/Quiz/components/Question.tsx\");\n/* harmony import */ var _components_useQuizData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/useQuizData */ \"./components/Tasks/Quiz/components/useQuizData.ts\");\n/* harmony import */ var _quiz_gql__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./quiz.gql */ \"./components/Tasks/Quiz/quiz.gql.ts\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_TextEditor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @Components/TextEditor */ \"./components/TextEditor.tsx\");\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__, _components_OptionList__WEBPACK_IMPORTED_MODULE_4__, _components_useQuizData__WEBPACK_IMPORTED_MODULE_6__, _quiz_gql__WEBPACK_IMPORTED_MODULE_7__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_8__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_9__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_10__]);\n([_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__, _components_OptionList__WEBPACK_IMPORTED_MODULE_4__, _components_useQuizData__WEBPACK_IMPORTED_MODULE_6__, _quiz_gql__WEBPACK_IMPORTED_MODULE_7__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_8__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_9__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_10__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\nconst QuizPlayBodyRenderer = ({ projectEventId, task, onSuccess, questions, completedCount })=>{\n    const [participateQuiz, { loading: verifying }] = (0,_quiz_gql__WEBPACK_IMPORTED_MODULE_7__.useParticipateQuiz)();\n    const { value, set, clear } = (0,_Hooks_useRouterQuery__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"question\");\n    const currentQuestionIndex = parseInt(value || \"0\");\n    const currentQuestion = questions ? questions[currentQuestionIndex] : undefined;\n    const { id, description } = task;\n    const [optionsSelected, setOptionsSelected] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const formRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const [loading, questionsState] = (0,_components_useQuizData__WEBPACK_IMPORTED_MODULE_6__.useQuizData)(projectEventId, questions);\n    const handleNavigate = (step)=>{\n        const index = currentQuestionIndex + step;\n        if (index < questions.length || index >= 0) {\n            set(index.toString());\n        }\n        setOptionsSelected([]);\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        const lastCompletedCount = completedCount;\n        if (!optionsSelected?.length) {\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n                title: \"Please select an answer\",\n                type: \"warning\"\n            });\n            return;\n        }\n        participateQuiz({\n            variables: {\n                eventId: projectEventId,\n                taskId: currentQuestion?.id || \"\",\n                data: {\n                    answers: optionsSelected\n                }\n            },\n            context: {\n                taskParentId: id\n            },\n            onCompleted: ()=>{\n                //If this is the last question, then send onSuccess\n                if (lastCompletedCount && lastCompletedCount + 1 == questions.length) {\n                    clear();\n                    return onSuccess?.();\n                }\n            },\n            onError: (error)=>{\n                (0,_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n                    title: \"Error!\",\n                    defaultText: \"Error verifying answer!\",\n                    type: \"error\",\n                    error\n                });\n            }\n        });\n    };\n    const handleChange = ()=>{\n        const form_data = new FormData(formRef.current);\n        const answers = Array.from(form_data.values()).map((el)=>{\n            return el;\n        });\n        setOptionsSelected(answers);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextEditor__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                defaultValue: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                lineNumber: 98,\n                columnNumber: 23\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                ref: formRef,\n                onChange: handleChange,\n                children: [\n                    currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"block w-full space-y-6 mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Question__WEBPACK_IMPORTED_MODULE_5__.Question, {\n                                question: currentQuestion,\n                                questionNumber: currentQuestionIndex + 1\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptionList__WEBPACK_IMPORTED_MODULE_4__.OptionList, {\n                                currentQuestion: currentQuestion,\n                                selectedOptions: questionsState?.get(currentQuestion.id)?.selectedOptions,\n                                submitted: questionsState?.has(currentQuestion.id),\n                                correctOptions: questionsState?.get(currentQuestion.id)?.correctOptions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end gap-4 mt-8\",\n                        children: [\n                            currentQuestionIndex !== questions.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                rounded: \"full\",\n                                variant: !questionsState?.has(currentQuestion?.id || \"\") ? \"outline\" : \"default\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    handleNavigate(1);\n                                },\n                                children: questionsState?.has(currentQuestion?.id || \"\") ? \"Next\" : \"Skip\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                rounded: \"full\",\n                                loading: verifying || loading,\n                                disabled: questionsState?.has(currentQuestion?.id || \"\") || !optionsSelected.length,\n                                type: \"submit\",\n                                children: !questionsState?.has(currentQuestion?.id || \"\") ? \"Submit Answer\" : \"Submitted\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuizPlayBodyRenderer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/QuizPlayBodyRenderer.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/components/Option.tsx":
/*!*****************************************************!*\
  !*** ./components/Tasks/Quiz/components/Option.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Option: () => (/* binding */ Option)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction Option({ option: { id, text }, questionType, name, answered, isSelected, isCorrect, hasCorrectOption }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`p-1 border rounded-md relative flex items-center component-bg`, isSelected ? \"border-primary\" : \"\", answered ? \"cursor-not-allowed\" : \"component-bg-hover cursor-pointer\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(questionType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.QuizQuestionType.SINGLE_CHOICE ? \"rounded-full\" : \"rounded-sm\", `ml-4 mr-2 appearance-none h-4 w-4 border bg-foreground/20 border-input checked:bg-primary checked:border-primary cursor-pointer focus:outline-none transition duration-200`),\n                type: questionType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.QuizQuestionType.SINGLE_CHOICE ? \"radio\" : \"checkbox\",\n                id: id,\n                name: name,\n                value: id,\n                defaultChecked: isSelected,\n                disabled: answered\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: id,\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex-1 inline-flex items-center py-2 pr-6\", answered ? \"cursor-not-allowed\" : \"cursor-pointer\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-4 inline-block\",\n                        children: text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    answered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute text-xl right-2\",\n                        children: isCorrect ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                            className: \"text-primary inline\",\n                            weight: \"fill\",\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 15\n                        }, this) : isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.XCircle, {\n                            className: \"text-red-500 inline\",\n                            weight: \"fill\",\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/components/Option.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/components/OptionList.tsx":
/*!*********************************************************!*\
  !*** ./components/Tasks/Quiz/components/OptionList.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionList: () => (/* binding */ OptionList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Option__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Option */ \"./components/Tasks/Quiz/components/Option.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Option__WEBPACK_IMPORTED_MODULE_1__]);\n_Option__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction OptionList({ currentQuestion, selectedOptions, submitted, correctOptions }) {\n    const quizTaskData = currentQuestion.info;\n    const isOptionSelected = (optionId)=>!!selectedOptions?.find((id)=>id === optionId);\n    const isCorrectOption = (optionId)=>{\n        if (!correctOptions) {\n            return (correctOptions || false) && isOptionSelected(optionId);\n        }\n        return correctOptions.includes(optionId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 mb-4\",\n        children: quizTaskData.options?.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Option__WEBPACK_IMPORTED_MODULE_1__.Option, {\n                option: option,\n                name: currentQuestion.id,\n                questionType: quizTaskData.questionType,\n                isCorrect: isCorrectOption(option.id),\n                isSelected: isOptionSelected(option.id),\n                answered: submitted\n            }, option.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\OptionList.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\OptionList.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/components/OptionList.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/components/Question.tsx":
/*!*******************************************************!*\
  !*** ./components/Tasks/Quiz/components/Question.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Question: () => (/* binding */ Question)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Question({ question: { title }, questionNumber }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-ch flex items-center gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs bg-foreground/10 border border-foreground/10 rounded-full px-2 py-1\",\n                children: [\n                    \"Q\",\n                    questionNumber\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Question.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Question.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Question.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1Rhc2tzL1F1aXovY29tcG9uZW50cy9RdWVzdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBLFNBQVMsRUFDdkJDLFVBQVUsRUFBRUMsS0FBSyxFQUFFLEVBQ25CQyxjQUFjLEVBSWY7SUFDQyxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFLRCxXQUFVOztvQkFBOEU7b0JBQzFGRjs7Ozs7OzswQkFFSiw4REFBQ0c7MEJBQU1KOzs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvVGFza3MvUXVpei9jb21wb25lbnRzL1F1ZXN0aW9uLnRzeD9mZjJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRhc2sgfSBmcm9tICdAYWlybHlmdC90eXBlcyc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUXVlc3Rpb24oe1xyXG4gIHF1ZXN0aW9uOiB7IHRpdGxlIH0sXHJcbiAgcXVlc3Rpb25OdW1iZXIsXHJcbn06IHtcclxuICBxdWVzdGlvbjogVGFzaztcclxuICBxdWVzdGlvbk51bWJlcjogbnVtYmVyO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jaCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGJnLWZvcmVncm91bmQvMTAgYm9yZGVyIGJvcmRlci1mb3JlZ3JvdW5kLzEwIHJvdW5kZWQtZnVsbCBweC0yIHB5LTFcIj5cclxuICAgICAgICBRe3F1ZXN0aW9uTnVtYmVyfVxyXG4gICAgICA8L3NwYW4+XHJcbiAgICAgIDxzcGFuPnt0aXRsZX08L3NwYW4+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJRdWVzdGlvbiIsInF1ZXN0aW9uIiwidGl0bGUiLCJxdWVzdGlvbk51bWJlciIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/components/Question.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/components/useQuizData.ts":
/*!*********************************************************!*\
  !*** ./components/Tasks/Quiz/components/useQuizData.ts ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuizData: () => (/* binding */ useQuizData)\n/* harmony export */ });\n/* harmony import */ var _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @Hooks/useTaskParticipation */ \"./hooks/useTaskParticipation.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_0__]);\n_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction useQuizData(projectEventId, questions) {\n    const { data: userTaskParticipation, loading: isParticipationLoading } = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_0__.useUserCurrentTaskParticipationMap)(projectEventId);\n    return [\n        isParticipationLoading,\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n            let mapTemp = new Map();\n            if (userTaskParticipation) {\n                userTaskParticipation.forEach((item)=>{\n                    if (questions.filter((i)=>i.id === item.taskId).length > 0) {\n                        mapTemp.set(item.taskId, {\n                            selectedOptions: item.info.answers,\n                            correctOptions: item.info.correctAnswers\n                        });\n                    }\n                });\n            }\n            return mapTemp;\n        }, [\n            userTaskParticipation,\n            questions\n        ])\n    ];\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/components/useQuizData.ts\n");

/***/ }),

/***/ "./components/TextEditor.tsx":
/*!***********************************!*\
  !*** ./components/TextEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! draft-js */ \"draft-js\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(draft_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! draft-js/dist/Draft.css */ \"./node_modules/draft-js/dist/Draft.css\");\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction TextEditor({ readonly = true, defaultValue, expandable = false, maxHeight = 100, className }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n    const [isOverflow, setIsOverflow] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        const container = ref.current;\n        if (!container || !setIsOverflow) return;\n        if (expandable && container.offsetHeight >= maxHeight) {\n            setIsOverflow(true);\n        }\n    }, [\n        ref,\n        setIsOverflow\n    ]);\n    function Link(props) {\n        const { url } = props.contentState.getEntity(props.entityKey).getData();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: url,\n            target: \"_blank\",\n            rel: \"noreferrer nofollow\",\n            referrerPolicy: \"no-referrer\",\n            style: {\n                color: currentTheme === \"dark\" ? \"#93cefe\" : \"#3b5998\",\n                textDecoration: \"underline\"\n            },\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    function findLinkEntities(contentBlock, callback, contentState) {\n        contentBlock.findEntityRanges((character)=>{\n            const entityKey = character.getEntity();\n            return entityKey !== null && contentState.getEntity(entityKey).getType() === \"LINK\";\n        }, callback);\n    }\n    const decorator = new draft_js__WEBPACK_IMPORTED_MODULE_3__.CompositeDecorator([\n        {\n            strategy: findLinkEntities,\n            component: Link\n        }\n    ]);\n    let editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createEmpty(decorator);\n    if (!defaultValue) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    if (defaultValue) {\n        try {\n            const parsedJson = JSON.parse(defaultValue);\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createWithContent((0,draft_js__WEBPACK_IMPORTED_MODULE_3__.convertFromRaw)(parsedJson), decorator);\n        } catch (err) {\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createWithContent(draft_js__WEBPACK_IMPORTED_MODULE_3__.ContentState.createFromText(defaultValue), decorator);\n        }\n    }\n    if (!editorState.getCurrentContent().hasText()) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            isOverflow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 z-10 h-[40px] w-full bg-gradient-to-t from-background/60 to-background/0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute z-20 flex items-center w-full justify-center -bottom-3`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-2 z-10 rounded-full border bg-background/90 text-cs text-sm font-extrabold\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.CaretUp, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.CaretDown, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`text-base text-ch leading-normal overflow-hidden`, className, isExpanded ? \"mb-10\" : \"\"),\n                ref: ref,\n                style: expandable && !isExpanded ? {\n                    maxHeight,\n                    marginBottom: 0\n                } : {},\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(draft_js__WEBPACK_IMPORTED_MODULE_3__.Editor, {\n                    editorState: editorState,\n                    readOnly: readonly,\n                    onChange: ()=>{}\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TextEditor.tsx\n");

/***/ })

};
;
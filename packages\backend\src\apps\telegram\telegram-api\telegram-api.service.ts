import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { ChatMember, ChatResponse } from './telegram-api.d';

@Injectable()
export class TelegramApiService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  protected readonly logger = new Logger(this.constructor.name);

  async hasUserJoinedGroup(
    chatId: string,
    telegramId: string,
    apiKey: string,
  ): Promise<boolean> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get<ChatMember>(`/bot${apiKey}/getChatMember`, {
          params: {
            chat_id: chatId,
            user_id: telegramId,
          },
        }),
      );

      if (data.result?.status === 'left') {
        throw new InternalServerErrorException(
          'You have not joined or left the group/channel',
        );
      }
      if (data.result?.status === 'kicked') {
        throw new InternalServerErrorException(
          'You have been banned from the group/channel',
        );
      }
      return data?.ok;
    } catch (error) {
      if (error instanceof InternalServerErrorException) {
        throw new BadRequestException(error.message);
      }
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      if (error?.response?.data?.description == 'Bad Request: user not found') {
        throw new BadRequestException(
          'AirLyft could not locate you in this telegram community. If you have already joined, please leave and rejoin before verifying again',
        );
      }
      if (error?.response?.data?.description == 'Bad Request: chat not found') {
        throw new BadRequestException(
          'AirLyft could not locate this telegram community. Please contact the community admin',
        );
      }

      this.logger.error(
        'Unknown error from telegram',
        JSON.stringify(error?.response?.data ?? error),
      );
      throw new BadRequestException(
        'Telegram API responded with an error. Please contact support',
      );
    }
  }

  async getChatInfo(handle: string) {
    const apiKey = this.configService.get<string>('TELEGRAM_BOT_API_KEY');
    try {
      const {
        data: { result: chat },
      } = await firstValueFrom(
        this.httpService.get<ChatResponse>(`/bot${apiKey}/getChat`, {
          params: {
            chat_id: `@${handle}`,
          },
        }),
      );
      return {
        username: chat.username,
        title: chat.title,
        chatId: chat.id,
        type: chat.type,
      };
    } catch (error) {
      throw new BadRequestException('Failed to fetch telegram chat info');
    }
  }
}

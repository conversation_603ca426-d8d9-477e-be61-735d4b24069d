/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Giveaways_AirTokenGiveaway_AirTokenFcfsTaskIdDetails_tsx";
exports.ids = ["components_Giveaways_AirTokenGiveaway_AirTokenFcfsTaskIdDetails_tsx"];
exports.modules = {

/***/ "./components/Web3Wallet lazy recursive ^\\.\\/.*$":
/*!***************************************************************!*\
  !*** ./components/Web3Wallet/ lazy ^\.\/.*$ namespace object ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./Dotsama/DotsamaAccountList": [
		"./components/Web3Wallet/Dotsama/DotsamaAccountList.tsx",
		"components_Web3Wallet_Dotsama_DotsamaAccountList_tsx"
	],
	"./Dotsama/DotsamaAccountList.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaAccountList.tsx",
		"components_Web3Wallet_Dotsama_DotsamaAccountList_tsx"
	],
	"./Dotsama/DotsamaManual": [
		"./components/Web3Wallet/Dotsama/DotsamaManual.tsx",
		"components_Web3Wallet_Dotsama_DotsamaManual_tsx"
	],
	"./Dotsama/DotsamaManual.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaManual.tsx",
		"components_Web3Wallet_Dotsama_DotsamaManual_tsx"
	],
	"./Dotsama/DotsamaNova": [
		"./components/Web3Wallet/Dotsama/DotsamaNova.tsx",
		"components_Web3Wallet_Dotsama_DotsamaNova_tsx"
	],
	"./Dotsama/DotsamaNova.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaNova.tsx",
		"components_Web3Wallet_Dotsama_DotsamaNova_tsx"
	],
	"./Dotsama/DotsamaPolkadotJs": [
		"./components/Web3Wallet/Dotsama/DotsamaPolkadotJs.tsx",
		"components_Web3Wallet_Dotsama_DotsamaPolkadotJs_tsx"
	],
	"./Dotsama/DotsamaPolkadotJs.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaPolkadotJs.tsx",
		"components_Web3Wallet_Dotsama_DotsamaPolkadotJs_tsx"
	],
	"./Dotsama/DotsamaRaw": [
		"./components/Web3Wallet/Dotsama/DotsamaRaw.tsx",
		"components_Web3Wallet_Dotsama_DotsamaRaw_tsx"
	],
	"./Dotsama/DotsamaRaw.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaRaw.tsx",
		"components_Web3Wallet_Dotsama_DotsamaRaw_tsx"
	],
	"./Dotsama/DotsamaSubwallet": [
		"./components/Web3Wallet/Dotsama/DotsamaSubwallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaSubwallet_tsx"
	],
	"./Dotsama/DotsamaSubwallet.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaSubwallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaSubwallet_tsx"
	],
	"./Dotsama/DotsamaTalisman": [
		"./components/Web3Wallet/Dotsama/DotsamaTalisman.tsx",
		"components_Web3Wallet_Dotsama_DotsamaTalisman_tsx"
	],
	"./Dotsama/DotsamaTalisman.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaTalisman.tsx",
		"components_Web3Wallet_Dotsama_DotsamaTalisman_tsx"
	],
	"./Dotsama/DotsamaWallet": [
		"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaWallet_tsx"
	],
	"./Dotsama/DotsamaWallet.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaWallet_tsx"
	],
	"./Dotsama/WalletNotFound": [
		"./components/Web3Wallet/Dotsama/WalletNotFound.tsx",
		"components_Web3Wallet_Dotsama_WalletNotFound_tsx"
	],
	"./Dotsama/WalletNotFound.tsx": [
		"./components/Web3Wallet/Dotsama/WalletNotFound.tsx",
		"components_Web3Wallet_Dotsama_WalletNotFound_tsx"
	],
	"./Evm/EvmManual": [
		"./components/Web3Wallet/Evm/EvmManual.tsx",
		"components_Web3Wallet_Evm_EvmManual_tsx"
	],
	"./Evm/EvmManual.tsx": [
		"./components/Web3Wallet/Evm/EvmManual.tsx",
		"components_Web3Wallet_Evm_EvmManual_tsx"
	],
	"./Evm/EvmMetamask": [
		"./components/Web3Wallet/Evm/EvmMetamask.tsx",
		"components_Web3Wallet_Evm_EvmMetamask_tsx"
	],
	"./Evm/EvmMetamask.tsx": [
		"./components/Web3Wallet/Evm/EvmMetamask.tsx",
		"components_Web3Wallet_Evm_EvmMetamask_tsx"
	],
	"./Evm/EvmSubwallet": [
		"./components/Web3Wallet/Evm/EvmSubwallet.tsx",
		"components_Web3Wallet_Evm_EvmSubwallet_tsx"
	],
	"./Evm/EvmSubwallet.tsx": [
		"./components/Web3Wallet/Evm/EvmSubwallet.tsx",
		"components_Web3Wallet_Evm_EvmSubwallet_tsx"
	],
	"./Evm/EvmTalisman": [
		"./components/Web3Wallet/Evm/EvmTalisman.tsx",
		"components_Web3Wallet_Evm_EvmTalisman_tsx"
	],
	"./Evm/EvmTalisman.tsx": [
		"./components/Web3Wallet/Evm/EvmTalisman.tsx",
		"components_Web3Wallet_Evm_EvmTalisman_tsx"
	],
	"./Evm/EvmWallet": [
		"./components/Web3Wallet/Evm/EvmWallet.tsx",
		"components_Web3Wallet_Evm_EvmWallet_tsx"
	],
	"./Evm/EvmWallet.tsx": [
		"./components/Web3Wallet/Evm/EvmWallet.tsx",
		"components_Web3Wallet_Evm_EvmWallet_tsx"
	],
	"./Evm/EvmWalletConnect": [
		"./components/Web3Wallet/Evm/EvmWalletConnect.tsx",
		"components_Web3Wallet_Evm_EvmWalletConnect_tsx"
	],
	"./Evm/EvmWalletConnect.tsx": [
		"./components/Web3Wallet/Evm/EvmWalletConnect.tsx",
		"components_Web3Wallet_Evm_EvmWalletConnect_tsx"
	],
	"./Evm/GenericInjectedEvm": [
		"./components/Web3Wallet/Evm/GenericInjectedEvm.tsx",
		"components_Web3Wallet_Evm_GenericInjectedEvm_tsx"
	],
	"./Evm/GenericInjectedEvm.tsx": [
		"./components/Web3Wallet/Evm/GenericInjectedEvm.tsx",
		"components_Web3Wallet_Evm_GenericInjectedEvm_tsx"
	],
	"./Web3WalletRenderer": [
		"./components/Web3Wallet/Web3WalletRenderer.tsx"
	],
	"./Web3WalletRenderer.tsx": [
		"./components/Web3Wallet/Web3WalletRenderer.tsx"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "./components/Web3Wallet lazy recursive ^\\.\\/.*$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "./components/AlertBox.tsx":
/*!*********************************!*\
  !*** ./components/AlertBox.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleWarningAlertBox: () => (/* binding */ SimpleWarningAlertBox),\n/* harmony export */   SuccessAlertBox: () => (/* binding */ SuccessAlertBox),\n/* harmony export */   WarningAlertBox: () => (/* binding */ WarningAlertBox),\n/* harmony export */   \"default\": () => (/* binding */ AlertBox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nfunction AlertBox({ title, subtitle, icon, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`flex flex-col p-4 py-8 rounded-2xl relative overflow-hidden text-primary-foreground`, className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg mb-0\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\nconst SimpleWarningAlertBox = ({ title, description })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-100 border border-red-300 text-red-600 px-4 py-3 rounded relative mt-4 space-x-2\",\n        role: \"alert\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                className: \"font-bold\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 51,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block sm:inline\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 52,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined);\nfunction SuccessAlertBox({ title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        className: \"gradient-primary\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n            className: \"h-10 text-primary-foreground\",\n            size: 32\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 66,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\nfunction WarningAlertBox({ title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Info, {\n            fontSize: 28\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 82,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle,\n        className: \"gradient-warning text-white\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AlertBox.tsx\n");

/***/ }),

/***/ "./components/BlockExplorerLink.tsx":
/*!******************************************!*\
  !*** ./components/BlockExplorerLink.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlockExplorerLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction BlockExplorerLink({ hash, blockExplorerUrls }) {\n    return hash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        className: \"underline inline-flex text-sm\",\n        target: \"_blank\",\n        href: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getExplorerLink)(blockExplorerUrls, hash, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ExplorerDataType.TRANSACTION),\n        rel: \"noreferrer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-1 inline-block\",\n                children: \"View on Explorer \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-4 w-4 inline-block\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/BlockExplorerLink.tsx\n");

/***/ }),

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx":
/*!********************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx ***!
  \********************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Web3Wallet/Dotsama/DotsamaWallet */ \"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../GiveawayTransactionHash */ \"./components/Giveaways/GiveawayTransactionHash.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./airtoken-giveaway.gql */ \"./components/Giveaways/AirTokenGiveaway/airtoken-giveaway.gql.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"react-google-recaptcha-v3\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__, _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__, _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_10__]);\n([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__, _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__, _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_10__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AirTokenDotsamaGiveawayClaim = ({ giveaway, projectEvent, blockchain, airToken, amount })=>{\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__.useGoogleReCaptcha)();\n    const [claimDotsama] = (0,_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_10__.useClaimDotsamaAirTokenGiveaway)();\n    const [isClaiming, setIsClaiming] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation)(\"translation\");\n    const { data: userEventRewardsData, loading: isUserEventRewardsLoading } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__.useGetUserEventRewards)(projectEvent.id);\n    const processing = userEventRewardsData?.userEventRewards.find((item)=>item.status === _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.RewardStatus.PROCESSING && item.giveawayId === giveaway.id);\n    const txHash = [\n        ...userEventRewardsData?.userEventRewards || []\n    ].sort((a, b)=>{\n        const dateA = +new Date(a.updatedAt);\n        const dateB = +new Date(b.updatedAt);\n        return dateB - dateA;\n    }).find((reward)=>reward.txHash && giveaway.id === reward.giveawayId)?.txHash;\n    const handleSubmit = async (connectorData)=>{\n        const { account } = connectorData;\n        if (!account || !airToken) return;\n        setIsClaiming(true);\n        let captcha;\n        if (projectEvent.ipProtect) {\n            if (!executeRecaptcha) {\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                    title: \"Failed\",\n                    text: \"Recaptcha not initialized\",\n                    type: \"error\"\n                });\n                setIsClaiming(false);\n                return;\n            }\n            captcha = await executeRecaptcha(\"airtoken_dotsama_giveaway_claim\");\n        }\n        try {\n            const formattedAddress = (0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__.convertToSs58Address)(account, blockchain.chainId);\n            await claimDotsama({\n                variables: {\n                    projectId: projectEvent.project.id,\n                    eventId: projectEvent.id,\n                    giveawayId: giveaway.id,\n                    userAddress: formattedAddress,\n                    captcha\n                }\n            });\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                title: \"Submitted\",\n                text: \"Your claim request has been submitted, check your notifications for an update.\",\n                type: \"success\"\n            });\n        } catch (err) {\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                title: \"Failed\",\n                text: err.message,\n                type: \"error\"\n            });\n        } finally{\n            setIsClaiming(false);\n        }\n    };\n    if (processing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.SuccessAlertBox, {\n            title: t(\"giveaway.airTokenPool.successTitle\"),\n            subtitle: t(\"giveaway.airTokenPool.successSubtitle\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!amount.isZero()) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    blockchain: blockchain,\n                    button: {\n                        confirm: {\n                            enable: true,\n                            loading: isClaiming || isUserEventRewardsLoading,\n                            text: `Claim ${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__.formatAmount)(amount?.toString(), airToken.decimals)} ${airToken.ticker} using `\n                        }\n                    },\n                    onSuccess: handleSubmit,\n                    excludedWallets: [\n                        _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.Web3WalletType.DOTSAMA_MANUAL\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined),\n                projectEvent.ipProtect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__.RecaptchaDeclaration, {\n                    className: \"text-xs text-cs text-center\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        txHash: txHash,\n        blockchain: blockchain\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n        lineNumber: 148,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AirTokenDotsamaGiveawayClaim);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx\n");

/***/ }),

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenEVMGiveawayClaim.tsx":
/*!****************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenEVMGiveawayClaim.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Loaders/ParagraphLoader */ \"./components/Loaders/ParagraphLoader.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_TransactionResult__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/TransactionResult */ \"./components/TransactionResult.tsx\");\n/* harmony import */ var _Components_Web3Wallet_Evm_EvmWallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/Web3Wallet/Evm/EvmWallet */ \"./components/Web3Wallet/Evm/EvmWallet.tsx\");\n/* harmony import */ var _Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Hooks/useGiveawayTxHash */ \"./hooks/useGiveawayTxHash.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _hooks_useGetTokenWindowInfo__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../hooks/useGetTokenWindowInfo */ \"./components/Giveaways/hooks/useGetTokenWindowInfo.ts\");\n/* harmony import */ var _useAirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./useAirTokenGiveawayClaim */ \"./components/Giveaways/AirTokenGiveaway/useAirTokenGiveawayClaim.tsx\");\n/* harmony import */ var _GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../GiveawayTransactionHash */ \"./components/Giveaways/GiveawayTransactionHash.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"react-google-recaptcha-v3\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\n/* harmony import */ var _Root_services_tracking__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @Root/services/tracking */ \"./services/tracking.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__, _Components_Web3Wallet_Evm_EvmWallet__WEBPACK_IMPORTED_MODULE_5__, _useAirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_12__, _Root_services_tracking__WEBPACK_IMPORTED_MODULE_17__]);\n([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__, _Components_Web3Wallet_Evm_EvmWallet__WEBPACK_IMPORTED_MODULE_5__, _useAirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_12__, _Root_services_tracking__WEBPACK_IMPORTED_MODULE_17__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AirTokenEVMGiveawayClaim = ({ giveaway, projectEvent, blockchain, airToken, amount })=>{\n    const { claimRewardTrack } = (0,_Root_services_tracking__WEBPACK_IMPORTED_MODULE_17__.useGtmTrack)();\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_15__.useGoogleReCaptcha)();\n    const { txHash: giveawayTxHash } = (0,_Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_6__.useGiveawayTxHash)(giveaway.id);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_14__.useTranslation)(\"translation\");\n    const giveawayInfo = giveaway.info;\n    const [tx, setTx] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)();\n    const [txHash, setTxHash] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)();\n    const { claim, loading: claiming } = (0,_useAirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_12__.useAirTokenGiveawayClaim)(projectEvent.project.id, projectEvent.id, giveaway.id, blockchain, airToken);\n    const { limitReached, loading: windowLoading } = (0,_hooks_useGetTokenWindowInfo__WEBPACK_IMPORTED_MODULE_11__.useGetTokenWindowInfo)(airToken.contractAddress || \"\", giveaway.id, ethers__WEBPACK_IMPORTED_MODULE_9__.BigNumber.from(giveawayInfo.amount || 0), amount, giveawayInfo.capped, blockchain);\n    const handleSubmit = async (connectorData)=>{\n        const gasLess = !!giveaway.gasless;\n        let captcha;\n        if (projectEvent.ipProtect) {\n            if (!executeRecaptcha) {\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n                    title: \"Failed\",\n                    text: \"Recaptcha not initialized\",\n                    type: \"error\"\n                });\n                return;\n            }\n            captcha = await executeRecaptcha(\"airtoken_evm_giveaway_claim\");\n        }\n        claim({\n            connectorData,\n            onError: (err)=>{\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n                    title: \"Failed\",\n                    text: err.message,\n                    type: \"error\"\n                });\n            },\n            onSuccess: (data)=>{\n                claimRewardTrack({\n                    projectId: projectEvent.project.id,\n                    eventId: projectEvent.id,\n                    projectTitle: projectEvent.project.name,\n                    eventTitle: projectEvent.title,\n                    giveawayId: giveaway.id,\n                    giveawayTitle: giveaway.title || \"\"\n                });\n                if (gasLess) {\n                    setTxHash(data.txHash);\n                } else {\n                    setTx(data.tx);\n                }\n            },\n            gasLess,\n            captcha\n        });\n    };\n    if (windowLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n        lineNumber: 109,\n        columnNumber: 29\n    }, undefined);\n    if (!amount.isZero() && limitReached) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.WarningAlertBox, {\n            title: t(\"giveaway.airTokenPool.warningTitle\"),\n            subtitle: t(\"giveaway.airTokenPool.warningTokenSubtitle\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!amount.isZero()) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: tx || txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TransactionResult__WEBPACK_IMPORTED_MODULE_4__.TransactionResult, {\n                tx: tx,\n                txHash: txHash,\n                blockchain: blockchain\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n                lineNumber: 124,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Web3Wallet_Evm_EvmWallet__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        blockchain: blockchain,\n                        size: \"default\",\n                        button: {\n                            confirm: {\n                                enable: true,\n                                loading: claiming,\n                                text: `Claim ${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_8__.formatAmount)(amount?.toString(), airToken.decimals)} ${airToken.ticker} using `\n                            }\n                        },\n                        onSuccess: (item)=>handleSubmit(item),\n                        excludedWallets: giveaway.gasless ? undefined : [\n                            _airlyft_types__WEBPACK_IMPORTED_MODULE_7__.Web3WalletType.EVM_MANUAL\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 13\n                    }, undefined),\n                    projectEvent.ipProtect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_16__.RecaptchaDeclaration, {\n                        className: \"text-xs text-cs text-center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n                lineNumber: 126,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        txHash: giveawayTxHash,\n        blockchain: blockchain\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AirTokenEVMGiveawayClaim);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenEVMGiveawayClaim.tsx\n");

/***/ }),

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenFcfsTaskIdDetails.tsx":
/*!*****************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenFcfsTaskIdDetails.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AirTokenGiveawayDetails)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Hooks/useGetBlockchain */ \"./hooks/useGetBlockchain.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_TokenGiveawayDetails__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/TokenGiveawayDetails */ \"./components/Giveaways/components/TokenGiveawayDetails.tsx\");\n/* harmony import */ var _Distribution_TokenFCFSTaskIdDistribution__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Distribution/TokenFCFSTaskIdDistribution */ \"./components/Giveaways/Distribution/TokenFCFSTaskIdDistribution.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _AirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./AirTokenGiveawayClaim */ \"./components/Giveaways/AirTokenGiveaway/AirTokenGiveawayClaim.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_TokenGiveawayDetails__WEBPACK_IMPORTED_MODULE_3__, _Distribution_TokenFCFSTaskIdDistribution__WEBPACK_IMPORTED_MODULE_4__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_5__, _AirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_TokenGiveawayDetails__WEBPACK_IMPORTED_MODULE_3__, _Distribution_TokenFCFSTaskIdDistribution__WEBPACK_IMPORTED_MODULE_4__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_5__, _AirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction AirTokenGiveawayDetails({ giveaway, projectEvent, expandable, expanded, onClick }) {\n    const giveawayInfo = giveaway.info;\n    const airToken = giveawayInfo?.airToken;\n    const rules = giveawayInfo?.rules || [];\n    const { stats, totalClaimable, loading, totalAmount, totalClaimed } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_5__.useGetFCFSStatsGiveaways)(giveaway.id, rules, projectEvent.id);\n    const { data: blockchainData, loading: blockchainLoading } = (0,_Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_1__.useGetBlockchain)(airToken.blockchainId);\n    const blockchain = blockchainData?.blockchain;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation)(\"translation\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenGiveawayDetails__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: t(\"giveaway.selectionTypes.fcfsTask\"),\n        token: airToken,\n        blockchain: blockchain,\n        amount: totalAmount,\n        claimableAmount: totalClaimable,\n        claimedAmount: totalClaimed,\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Distribution_TokenFCFSTaskIdDistribution__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                projectEvent: projectEvent,\n                rules: rules,\n                rewardType: airToken.ticker,\n                stats: stats,\n                token: airToken,\n                showDetails: true,\n                hasWhitelist: giveaway.hasWhitelist\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenFcfsTaskIdDetails.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            blockchain ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent,\n                amount: totalClaimable,\n                blockchain: blockchain,\n                airToken: airToken\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenFcfsTaskIdDetails.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenFcfsTaskIdDetails.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenFcfsTaskIdDetails.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenFcfsTaskIdDetails.tsx\n");

/***/ }),

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenGiveawayClaim.tsx":
/*!*************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenGiveawayClaim.tsx ***!
  \*************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _AirTokenDotsamaGiveawayClaim__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AirTokenDotsamaGiveawayClaim */ \"./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx\");\n/* harmony import */ var _AirTokenEVMGiveawayClaim__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AirTokenEVMGiveawayClaim */ \"./components/Giveaways/AirTokenGiveaway/AirTokenEVMGiveawayClaim.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_AirTokenDotsamaGiveawayClaim__WEBPACK_IMPORTED_MODULE_2__, _AirTokenEVMGiveawayClaim__WEBPACK_IMPORTED_MODULE_3__]);\n([_AirTokenDotsamaGiveawayClaim__WEBPACK_IMPORTED_MODULE_2__, _AirTokenEVMGiveawayClaim__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst AirTokenGiveawayClaim = ({ giveaway, projectEvent, blockchain, airToken, amount })=>{\n    if (!blockchain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    switch(blockchain.type){\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.BlockchainType.EVM:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AirTokenEVMGiveawayClaim__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent,\n                amount: amount,\n                blockchain: blockchain,\n                airToken: airToken\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenGiveawayClaim.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, undefined);\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.BlockchainType.DOTSAMA:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AirTokenDotsamaGiveawayClaim__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent,\n                amount: amount,\n                blockchain: blockchain,\n                airToken: airToken\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenGiveawayClaim.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AirTokenGiveawayClaim);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenGiveawayClaim.tsx\n");

/***/ }),

/***/ "./components/Giveaways/AirTokenGiveaway/useAirTokenGiveawayClaim.tsx":
/*!****************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/useAirTokenGiveawayClaim.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAirTokenGiveawayClaim: () => (/* binding */ useAirTokenGiveawayClaim)\n/* harmony export */ });\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Hooks/useGiveawayTxHash */ \"./hooks/useGiveawayTxHash.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hooks_useGetContract__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useGetContract */ \"./components/Giveaways/hooks/useGetContract.ts\");\n/* harmony import */ var _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./airtoken-giveaway.gql */ \"./components/Giveaways/AirTokenGiveaway/airtoken-giveaway.gql.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__]);\n_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst claimERC721AirToken = (contractAddress, connectorData, rewardCertificate, token)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ERC721AirbaseController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    if (token.external) {\n        if (claimIds.length === 1) {\n            return contract.claimExternal(account, claimIds[0], token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n        }\n        return contract.claimExternalBatch(account, claimIds, token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n    }\n    if (claimIds.length === 1) {\n        return contract.claim(account, claimIds[0], token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, claimIds, token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n};\nconst claimERC1155AirToken = (contractAddress, connectorData, rewardCertificate, token)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ERC1155AirbaseController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    if (claimIds.length === 1) {\n        return contract.claim(account, claimIds[0], token.address, token.tokenId, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, claimIds, token.address, token.tokenId, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n};\nconst claimERC20AirToken = (contractAddress, connectorData, rewardCertificate, token)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ERC20AirbaseController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    if (claimIds.length === 1) {\n        return contract.claim(account, claimIds[0], token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, claimIds, token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n};\nfunction useAirTokenGiveawayClaim(projectId, projectEventId, giveawayId, blockchain, airToken) {\n    const { update } = (0,_Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_2__.useGiveawayTxHash)(giveawayId);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [getAirTokenRewardCertificate] = (0,_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useGetAirTokenRewardCertificate)();\n    const [sync] = (0,_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useSyncClaimAirTokenGiveaway)();\n    const assetType = airToken?.assetType;\n    const contractType = assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC1155 ? _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.ContractType.ERC1155_AIRBASE : assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC20 ? _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.ContractType.ERC20_AIRBASE : assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC721 ? _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.ContractType.ERC721_AIRBASE : undefined;\n    const { data: contractData, loading: contractLoading } = (0,_hooks_useGetContract__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(blockchain?.id, contractType);\n    const contractAddress = contractData?.contract?.address;\n    const mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        mountedRef.current = true;\n        return ()=>{\n            mountedRef.current = false;\n        };\n    }, []);\n    const claim = async ({ connectorData, onError, onSuccess, gasLess = false, captcha })=>{\n        const { provider, account } = connectorData;\n        if (!gasLess && !provider || !account || !contractAddress || !airToken) return;\n        mountedRef.current && setLoading(true);\n        try {\n            const { data } = await getAirTokenRewardCertificate({\n                variables: {\n                    projectId,\n                    eventId: projectEventId,\n                    giveawayId,\n                    userAddress: account,\n                    captcha\n                },\n                context: {\n                    gasLess\n                }\n            });\n            if (gasLess) {\n                const txHash = data?.claimAirTokenGiveaway?.txHash;\n                if (!txHash) throw new Error(\"Claim Failed\");\n                update(txHash);\n                onSuccess?.({\n                    contractAddress,\n                    txHash\n                });\n            } else {\n                const result = data?.claimAirTokenGiveaway;\n                if (!result || !result.certificate) throw new Error(\"Invalid certificate\");\n                let tx;\n                if (airToken.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC20) {\n                    tx = await claimERC20AirToken(contractAddress, connectorData, result, airToken);\n                } else if (airToken.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC1155) {\n                    tx = await claimERC1155AirToken(contractAddress, connectorData, result, airToken);\n                } else if (airToken.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC721) {\n                    tx = await claimERC721AirToken(contractAddress, connectorData, result, airToken);\n                } else {\n                    throw new Error(\"Invalid asset\");\n                }\n                update(tx.hash);\n                await tx.wait();\n                await sync({\n                    variables: {\n                        ids: result.raw.map((item)=>item.id),\n                        giveawayId: giveawayId\n                    },\n                    context: {\n                        eventId: projectEventId\n                    }\n                });\n                onSuccess?.({\n                    contractAddress,\n                    tx\n                });\n            }\n        } catch (err) {\n            let error;\n            if (err?.code === \"ACTION_REJECTED\") {\n                error = new Error(\"Tx Signature: User denied transaction signature.\");\n            }\n            onError?.(error || err);\n        } finally{\n            mountedRef.current && setLoading(false);\n        }\n    };\n    return {\n        claim,\n        loading: loading || contractLoading\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/useAirTokenGiveawayClaim.tsx\n");

/***/ }),

/***/ "./components/Giveaways/Distribution/TokenFCFSTaskIdDistribution.tsx":
/*!***************************************************************************!*\
  !*** ./components/Giveaways/Distribution/TokenFCFSTaskIdDistribution.tsx ***!
  \***************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_List__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/List */ \"./components/List/index.tsx\");\n/* harmony import */ var _Components_Paragraph__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Paragraph */ \"./components/Paragraph.tsx\");\n/* harmony import */ var _Components_Tasks_TaskSummary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Tasks/TaskSummary */ \"./components/Tasks/TaskSummary.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_TokenFCFSUserProgress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/TokenFCFSUserProgress */ \"./components/Giveaways/components/TokenFCFSUserProgress.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Tasks_TaskSummary__WEBPACK_IMPORTED_MODULE_3__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__, _components_TokenFCFSUserProgress__WEBPACK_IMPORTED_MODULE_6__]);\n([_Components_Tasks_TaskSummary__WEBPACK_IMPORTED_MODULE_3__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__, _components_TokenFCFSUserProgress__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst TokenFCFSTaskIdDistribution = ({ projectEvent, rules, rewardType, stats, token, showDetails, hasWhitelist })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation)(\"translation\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Paragraph__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__.Flask, {\n                    weight: \"fill\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSTaskIdDistribution.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 17\n                }, void 0),\n                title: t(\"giveaway.distribution.title\"),\n                description: hasWhitelist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        t(\"giveaway.distribution.descriptionFCFSTask.hasWhitelist.pre\"),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                            children: t(\"giveaway.distribution.descriptionFCFSTask.hasWhitelist.mid1\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSTaskIdDistribution.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 17\n                        }, void 0),\n                        t(\"giveaway.distribution.descriptionFCFSTask.hasWhitelist.mid2\"),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                            children: t(\"giveaway.distribution.descriptionFCFSTask.hasWhitelist.mid3\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSTaskIdDistribution.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 17\n                        }, void 0),\n                        t(\"giveaway.distribution.descriptionFCFSTask.hasWhitelist.post\")\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        t(\"giveaway.distribution.descriptionFCFSTask.hasNoWhitelist.pre\", {\n                            event: globalT(\"event_one\")\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                            children: t(\"giveaway.distribution.descriptionFCFSTask.hasNoWhitelist.mid\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSTaskIdDistribution.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 17\n                        }, void 0),\n                        t(\"giveaway.distribution.descriptionFCFSTask.hasNoWhitelist.post\")\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSTaskIdDistribution.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_List__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"space-y-5\",\n                data: stats || [],\n                render: (item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenFCFSUserProgress__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        data: item,\n                        token: token,\n                        showDetails: showDetails,\n                        rule: item.raw,\n                        description: showDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_TaskSummary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                projectEvent: projectEvent,\n                                taskIds: item.ids\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSTaskIdDistribution.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 19\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSTaskIdDistribution.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 17\n                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_5__.Fragment, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSTaskIdDistribution.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 17\n                        }, void 0)\n                    }, key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSTaskIdDistribution.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\Distribution\\\\TokenFCFSTaskIdDistribution.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TokenFCFSTaskIdDistribution);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/Distribution/TokenFCFSTaskIdDistribution.tsx\n");

/***/ }),

/***/ "./components/Giveaways/GiveawayTransactionHash.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/GiveawayTransactionHash.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_TransactionResult__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/TransactionResult */ \"./components/TransactionResult.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst GiveawayTransactionHash = ({ txHash, blockchain })=>{\n    if (!txHash) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n        lineNumber: 12,\n        columnNumber: 23\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"!my-0 flex justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TransactionResult__WEBPACK_IMPORTED_MODULE_1__.TransactionResult, {\n            txHash: txHash,\n            blockchain: blockchain\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GiveawayTransactionHash);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9HaXZlYXdheVRyYW5zYWN0aW9uSGFzaC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNrRTtBQUNqQztBQUVqQyxNQUFNRSwwQkFBMEIsQ0FBQyxFQUMvQkMsTUFBTSxFQUNOQyxVQUFVLEVBSVg7SUFDQyxJQUFJLENBQUNELFFBQVEscUJBQU8sOERBQUNGLDJDQUFRQTs7Ozs7SUFDN0IscUJBQ0UsOERBQUNJO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNOLDRFQUFpQkE7WUFBQ0csUUFBUUE7WUFBUUMsWUFBWUE7Ozs7Ozs7Ozs7O0FBR3JEO0FBRUEsaUVBQWVGLHVCQUF1QkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvR2l2ZWF3YXlzL0dpdmVhd2F5VHJhbnNhY3Rpb25IYXNoLnRzeD84NmNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJsb2NrY2hhaW4gfSBmcm9tICdAYWlybHlmdC90eXBlcyc7XHJcbmltcG9ydCB7IFRyYW5zYWN0aW9uUmVzdWx0IH0gZnJvbSAnQENvbXBvbmVudHMvVHJhbnNhY3Rpb25SZXN1bHQnO1xyXG5pbXBvcnQgeyBGcmFnbWVudCB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmNvbnN0IEdpdmVhd2F5VHJhbnNhY3Rpb25IYXNoID0gKHtcclxuICB0eEhhc2gsXHJcbiAgYmxvY2tjaGFpbixcclxufToge1xyXG4gIHR4SGFzaD86IHN0cmluZyB8IG51bGw7XHJcbiAgYmxvY2tjaGFpbjogQmxvY2tjaGFpbjtcclxufSkgPT4ge1xyXG4gIGlmICghdHhIYXNoKSByZXR1cm4gPEZyYWdtZW50IC8+O1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIiFteS0wIGZsZXgganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgPFRyYW5zYWN0aW9uUmVzdWx0IHR4SGFzaD17dHhIYXNofSBibG9ja2NoYWluPXtibG9ja2NoYWlufSAvPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEdpdmVhd2F5VHJhbnNhY3Rpb25IYXNoO1xyXG4iXSwibmFtZXMiOlsiVHJhbnNhY3Rpb25SZXN1bHQiLCJGcmFnbWVudCIsIkdpdmVhd2F5VHJhbnNhY3Rpb25IYXNoIiwidHhIYXNoIiwiYmxvY2tjaGFpbiIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/Giveaways/GiveawayTransactionHash.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/GiveawayFCFSRangeTag.tsx":
/*!******************************************************************!*\
  !*** ./components/Giveaways/components/GiveawayFCFSRangeTag.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawayFCFSRangeTag),\n/* harmony export */   formatUserProgressTitle: () => (/* binding */ formatUserProgressTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Tag__WEBPACK_IMPORTED_MODULE_2__]);\n_Components_Tag__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction formatUserProgressTitle({ rule, completed, total, pointsSuffix, pointsCompletedText }) {\n    const isCompleted = total === completed;\n    let suffix;\n    let completedText;\n    switch(rule.ruleType){\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.POINTS:\n            suffix = pointsSuffix;\n            completedText = pointsCompletedText;\n            break;\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.TASK_COUNT:\n        default:\n            suffix = \"Quest Completed\";\n            completedText = \"Task Completed\";\n    }\n    return isCompleted ? completedText : `${completed} / ${total} ${suffix}`;\n}\nfunction GiveawayFCFSRangeTag({ rule }) {\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    switch(rule.ruleType){\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.TASK_COUNT:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        \" Complete any \",\n                        rule.min,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 18\n                }, void 0),\n                className: \"!inline-flex\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this);\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.POINTS:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        \" \",\n                        globalT(\"projectPoints\"),\n                        \" greater than \",\n                        rule.min,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 13\n                }, void 0),\n                className: \"!inline-flex !bg-primary/10 !border-primary/60 !font-semibold\",\n                rounded: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this);\n        default:\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n        lineNumber: 64,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawayFCFSRangeTag.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/GiveawaySummaryItem.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryItem.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawaySummaryItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction GiveawaySummaryItem({ banner, children, bannerTag, onClick, size = \"default\", actionText, className, actionSuccess }) {\n    const isVideo = banner?.endsWith(\".mp4\") || banner?.endsWith(\".webm\") || banner?.endsWith(\".mov\");\n    const isGif = banner?.endsWith(\".gif\");\n    if (size === \"small\" || size == \"default\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative gap-4 grid grid-cols-[120px_1fr] items-center ${size === \"default\" ? \"sm:grid-cols-[170px_1fr]\" : \"\"} ${className || \"\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            autoPlay: true,\n                            playsInline: true,\n                            loop: true,\n                            muted: true,\n                            className: \"object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: banner,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 200,\n                            width: 200,\n                            src: banner,\n                            className: `object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]`,\n                            alt: \"giveaway-image\",\n                            unoptimized: isGif\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 max-w-sm m-auto\",\n                            children: [\n                                isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    autoPlay: true,\n                                    playsInline: true,\n                                    loop: true,\n                                    muted: true,\n                                    className: \"rounded-xl w-full object-cover\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: banner,\n                                        type: \"video/mp4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    height: 200,\n                                    width: 200,\n                                    src: banner,\n                                    className: \"object-cover w-full aspect-square rounded-xl\",\n                                    alt: \"giveaway-image\",\n                                    loading: \"lazy\",\n                                    unoptimized: isGif\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-1 left-0 w-full flex justify-center\",\n                                    children: bannerTag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-20 gap-2 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: children\n                        }, void 0, false),\n                        actionText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm flex items-center gap-1 text-blue-500 font-medium cursor-pointer\",\n                            onClick: onClick,\n                            children: [\n                                actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                    weight: \"bold\",\n                                    size: 15\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"line-clamp-1 break-all\",\n                                    children: actionText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    \"aria-hidden\": \"true\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute transform-gpu -z-10 rounded-3xl w-full blur-3xl`,\n                        children: isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            autoPlay: true,\n                            playsInline: true,\n                            loop: true,\n                            muted: true,\n                            className: \"rounded-xl w-full object-cover\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: banner,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 400,\n                            width: 400,\n                            src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                            className: `object-cover w-full aspect-square rounded-xl`,\n                            alt: \"giveaway-image\",\n                            loading: \"lazy\",\n                            unoptimized: isGif\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-2 relative z-10\",\n                        children: [\n                            isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                autoPlay: true,\n                                playsInline: true,\n                                loop: true,\n                                muted: true,\n                                className: \"rounded-xl w-full object-cover\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                    src: banner,\n                                    type: \"video/mp4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                height: 400,\n                                width: 400,\n                                src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                                className: `object-cover aspect-square w-full rounded-xl`,\n                                alt: \"giveaway-image\",\n                                loading: \"lazy\",\n                                unoptimized: isGif\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2\",\n                                children: bannerTag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20 space-y-2\",\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"sm\",\n                            rounded: \"full\",\n                            className: \"!font-semibold mt-2\",\n                            block: true,\n                            onClick: onClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                        weight: \"bold\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"line-clamp-1 break-all\",\n                                        children: actionText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryItem.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenAddress.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/components/TokenAddress.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenAddress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction TokenAddress({ token, blockchain, className, showBlockchain = false, addressChars = 3 }) {\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    if (token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AssetType.NATIVE) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex gap-1.5 text-sm text-cl items-center ${className || \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.shortenAddress)(token.address, addressChars)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Copy, {\n                className: \"cursor-pointer text-ch\",\n                size: 16,\n                onClick: (event)=>{\n                    event.stopPropagation();\n                    navigator.clipboard.writeText(token.address);\n                    setCopied(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"inline-flex text-ch\",\n                target: \"_blank\",\n                href: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.address, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.TOKEN),\n                rel: \"noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.ArrowSquareOut, {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this),\n            showBlockchain && blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                src: blockchain?.icon || \"\",\n                height: 20,\n                width: 20,\n                className: \"h-5 w-5\",\n                alt: \"blockchain-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenAddress.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenFCFSUserProgress.tsx":
/*!*******************************************************************!*\
  !*** ./components/Giveaways/components/TokenFCFSUserProgress.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenGiveawayUserProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_List_ListItem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/List/ListItem */ \"./components/List/ListItem.tsx\");\n/* harmony import */ var _Components_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Loaders/SpinLoader */ \"./components/Loaders/SpinLoader.tsx\");\n/* harmony import */ var _Components_RadialProgress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/RadialProgress */ \"./components/RadialProgress/index.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _GiveawayFCFSRangeTag__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./GiveawayFCFSRangeTag */ \"./components/Giveaways/components/GiveawayFCFSRangeTag.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_10__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Tag__WEBPACK_IMPORTED_MODULE_4__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__, _GiveawayFCFSRangeTag__WEBPACK_IMPORTED_MODULE_9__]);\n([_Components_Tag__WEBPACK_IMPORTED_MODULE_4__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__, _GiveawayFCFSRangeTag__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\nfunction TokenGiveawayUserProgress({ showDetails, token, description, data, rule }) {\n    const { claimed, percentage, amount, isCompleted, completed, total, status } = data;\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_10__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    //User Progress only makes sense if total is non-zero, else this giveaway probably just has a whitelist\n    if (total == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_List_ListItem__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        icon: isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Check, {\n            size: 22,\n            className: \"text-primary-foreground bg-primary rounded-full p-1\",\n            weight: \"bold\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 42,\n            columnNumber: 11\n        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RadialProgress__WEBPACK_IMPORTED_MODULE_3__.RadialProgress, {\n            progress: percentage,\n            text: ``,\n            circleSize: 26,\n            barSize: 3\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 48,\n            columnNumber: 11\n        }, void 0),\n        title: `${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__.formatAmount)(amount?.toString(), token.decimals)} ${token.ticker}`,\n        subtitle: (0,_GiveawayFCFSRangeTag__WEBPACK_IMPORTED_MODULE_9__.formatUserProgressTitle)({\n            rule,\n            total,\n            completed,\n            pointsSuffix: `${globalT(\"projectPoints\")} Scored`,\n            pointsCompletedText: `All ${globalT(\"projectPoints\")} Scored`\n        }),\n        extra: showDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_8__.Fragment, {\n            children: [\n                claimed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-primary text-xs text-primary-foreground rounded-full py-1 px-2 flex items-center space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Check, {\n                            className: \"h-3\",\n                            weight: \"bold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 17\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Claimed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 17\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 15\n                }, void 0),\n                status && status === _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.RewardStatus.PROCESSING ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 15\n                }, void 0) : isCompleted && !claimed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_4__.Tag, {\n                    title: \"Claimable\",\n                    size: \"small\",\n                    className: \"!font-bold\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 17\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 68,\n            columnNumber: 11\n        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_8__.Fragment, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 86,\n            columnNumber: 11\n        }, void 0),\n        description: description\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenFCFSUserProgress.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenGiveawayClaimStats.tsx":
/*!*********************************************************************!*\
  !*** ./components/Giveaways/components/TokenGiveawayClaimStats.tsx ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenGiveawayClaimStats: () => (/* binding */ TokenGiveawayClaimStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction TokenGiveawayClaimStats({ totalClaimable, totalClaimed, token, blockchain }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2 text-sm text-ch font-medium \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Coins, {\n                        size: 20,\n                        className: \"flex-shrink-0\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"break-all\",\n                        children: [\n                            (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(totalClaimed?.toString(), token.decimals),\n                            \" \",\n                            token.ticker,\n                            \" Claimed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Coins, {\n                        size: 20,\n                        className: \"flex-shrink-0\",\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"break-all\",\n                        children: [\n                            (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(totalClaimable?.toString(), token.decimals),\n                            \" \",\n                            token.ticker,\n                            \" Claimable\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayClaimStats.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenGiveawayClaimStats.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenGiveawayDetails.tsx":
/*!******************************************************************!*\
  !*** ./components/Giveaways/components/TokenGiveawayDetails.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenGiveawayDetails)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Panel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Panel */ \"./components/Panel.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/TokenGiveawayClaimStats */ \"./components/Giveaways/components/TokenGiveawayClaimStats.tsx\");\n/* harmony import */ var _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./GiveawaySummaryItem */ \"./components/Giveaways/components/GiveawaySummaryItem.tsx\");\n/* harmony import */ var _TokenAddress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TokenAddress */ \"./components/Giveaways/components/TokenAddress.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__, _Components_Panel__WEBPACK_IMPORTED_MODULE_3__, _Components_Tag__WEBPACK_IMPORTED_MODULE_4__, _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__, _TokenAddress__WEBPACK_IMPORTED_MODULE_8__]);\n([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__, _Components_Panel__WEBPACK_IMPORTED_MODULE_3__, _Components_Tag__WEBPACK_IMPORTED_MODULE_4__, _components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__, _TokenAddress__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction TokenGiveawayDetails({ token, blockchain, amount, claimableAmount, claimedAmount, expandable, expanded, children, onClick, title }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Panel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            banner: token.icon || \"\",\n            className: expanded ? \"!grid-cols-1 sm:!grid-cols-[170px_1fr]\" : \"\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_4__.Tag, {\n                        title: title,\n                        className: \"!inline-flex !text-xs !font-semibold\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: `text-lg text-ch font-semibold break-all`,\n                    children: [\n                        \"Win up to \",\n                        (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(amount?.toString() || \"\", token.decimals),\n                        \" \",\n                        token.ticker,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, void 0),\n                token.address && token.address != ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.constants.AddressZero ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAddress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    token: token,\n                    blockchain: blockchain,\n                    showBlockchain: true,\n                    className: \"mb-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 13\n                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenGiveawayClaimStats__WEBPACK_IMPORTED_MODULE_6__.TokenGiveawayClaimStats, {\n                    token: token,\n                    totalClaimable: claimableAmount,\n                    totalClaimed: claimedAmount,\n                    blockchain: blockchain\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n            lineNumber: 41,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 relative z-50\",\n            children: [\n                children,\n                amount && claimedAmount.eq(amount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_2__.SuccessAlertBox, {\n                    title: \"Congrats!\",\n                    subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Your transaction was submitted successfully, sometimes it takes 30-60 seconds for the explorer to index it.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawayDetails.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenGiveawayDetails.tsx\n");

/***/ }),

/***/ "./components/Giveaways/hooks/useGetContract.ts":
/*!******************************************************!*\
  !*** ./components/Giveaways/hooks/useGetContract.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useGetContract)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst GET_CONTRACT = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql)`\r\n  query contract($blockchainId: ID!, $contractType: ContractType!) {\r\n    contract(blockchainId: $blockchainId, contractType: $contractType) {\r\n      address\r\n    }\r\n  }\r\n`;\nfunction useGetContract(blockchainId, contractType) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.useQuery)(GET_CONTRACT, {\n        variables: {\n            blockchainId: blockchainId,\n            contractType: contractType\n        },\n        skip: !blockchainId || !contractType\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9ob29rcy91c2VHZXRDb250cmFjdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDK0M7QUFFL0MsTUFBTUUsZUFBZUYsbURBQUcsQ0FBQzs7Ozs7O0FBTXpCLENBQUM7QUFFYyxTQUFTRyxlQUN0QkMsWUFBZ0MsRUFDaENDLFlBQXNDO0lBRXRDLE9BQU9KLHdEQUFRQSxDQUE2Q0MsY0FBYztRQUN4RUksV0FBVztZQUNURixjQUFjQTtZQUNkQyxjQUFjQTtRQUNoQjtRQUNBRSxNQUFNLENBQUNILGdCQUFnQixDQUFDQztJQUMxQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9HaXZlYXdheXMvaG9va3MvdXNlR2V0Q29udHJhY3QudHM/NDJmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDb250cmFjdCwgQ29udHJhY3RUeXBlLCBRdWVyeV9jb250cmFjdEFyZ3MgfSBmcm9tICdAYWlybHlmdC90eXBlcyc7XHJcbmltcG9ydCB7IGdxbCwgdXNlUXVlcnkgfSBmcm9tICdAYXBvbGxvL2NsaWVudCc7XHJcblxyXG5jb25zdCBHRVRfQ09OVFJBQ1QgPSBncWxgXHJcbiAgcXVlcnkgY29udHJhY3QoJGJsb2NrY2hhaW5JZDogSUQhLCAkY29udHJhY3RUeXBlOiBDb250cmFjdFR5cGUhKSB7XHJcbiAgICBjb250cmFjdChibG9ja2NoYWluSWQ6ICRibG9ja2NoYWluSWQsIGNvbnRyYWN0VHlwZTogJGNvbnRyYWN0VHlwZSkge1xyXG4gICAgICBhZGRyZXNzXHJcbiAgICB9XHJcbiAgfVxyXG5gO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlR2V0Q29udHJhY3QoXHJcbiAgYmxvY2tjaGFpbklkOiBzdHJpbmcgfCB1bmRlZmluZWQsXHJcbiAgY29udHJhY3RUeXBlOiBDb250cmFjdFR5cGUgfCB1bmRlZmluZWQsXHJcbikge1xyXG4gIHJldHVybiB1c2VRdWVyeTx7IGNvbnRyYWN0OiBDb250cmFjdCB9LCBRdWVyeV9jb250cmFjdEFyZ3M+KEdFVF9DT05UUkFDVCwge1xyXG4gICAgdmFyaWFibGVzOiB7XHJcbiAgICAgIGJsb2NrY2hhaW5JZDogYmxvY2tjaGFpbklkIGFzIHN0cmluZyxcclxuICAgICAgY29udHJhY3RUeXBlOiBjb250cmFjdFR5cGUgYXMgQ29udHJhY3RUeXBlLFxyXG4gICAgfSxcclxuICAgIHNraXA6ICFibG9ja2NoYWluSWQgfHwgIWNvbnRyYWN0VHlwZSxcclxuICB9KTtcclxufVxyXG4iXSwibmFtZXMiOlsiZ3FsIiwidXNlUXVlcnkiLCJHRVRfQ09OVFJBQ1QiLCJ1c2VHZXRDb250cmFjdCIsImJsb2NrY2hhaW5JZCIsImNvbnRyYWN0VHlwZSIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Giveaways/hooks/useGetContract.ts\n");

/***/ }),

/***/ "./components/Giveaways/hooks/useGetTokenWindowInfo.ts":
/*!*************************************************************!*\
  !*** ./components/Giveaways/hooks/useGetTokenWindowInfo.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGetTokenWindowInfo: () => (/* binding */ useGetTokenWindowInfo)\n/* harmony export */ });\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction useGetTokenWindowInfo(contractAddress, giveawayId, totalAmount, amount, capped, blockchain) {\n    const { windowsClaimed, loading: windowsLoading } = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0__.useGetBatchWindowClaimed)(blockchain, contractAddress, [\n        giveawayId\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!capped) return {\n            limitReached: false\n        };\n        const giveawayWindowLeft = totalAmount.sub(windowsClaimed?.[giveawayId] || ethers__WEBPACK_IMPORTED_MODULE_1__.BigNumber.from(0));\n        if (giveawayWindowLeft.lte(ethers__WEBPACK_IMPORTED_MODULE_1__.BigNumber.from(0))) return {\n            limitReached: true\n        };\n        if (amount.gt(giveawayWindowLeft)) return {\n            limitReached: true\n        };\n        return {\n            limitReached: false\n        };\n    }, [\n        windowsLoading,\n        totalAmount,\n        giveawayId,\n        amount\n    ]);\n    return {\n        ...stats,\n        loading: windowsLoading\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/hooks/useGetTokenWindowInfo.ts\n");

/***/ }),

/***/ "./components/Loaders/SingleDot.tsx":
/*!******************************************!*\
  !*** ./components/Loaders/SingleDot.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-content-loader */ \"react-content-loader\");\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_content_loader__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst SingleDotLoader = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_content_loader__WEBPACK_IMPORTED_MODULE_2___default()), {\n        speed: 2,\n        width: 32,\n        height: 32,\n        viewBox: \"0 0 32 32\",\n        uniqueKey: \"single-dot-loader\",\n        backgroundColor: \"#cdcdcd\",\n        foregroundColor: \"#ddd\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n            cx: \"16\",\n            cy: \"16\",\n            r: \"16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n            lineNumber: 15,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SingleDotLoader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvU2luZ2xlRG90LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUN1QjtBQUVqRCxNQUFNRSxrQkFBa0IsQ0FBQ0Msc0JBQ3ZCLDhEQUFDRiw2REFBYUE7UUFDWkcsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsU0FBUTtRQUNSQyxXQUFVO1FBQ1ZDLGlCQUFnQjtRQUNoQkMsaUJBQWdCO1FBQ2YsR0FBR1AsS0FBSztrQkFFVCw0RUFBQ1E7WUFBT0MsSUFBRztZQUFLQyxJQUFHO1lBQUtDLEdBQUU7Ozs7Ozs7Ozs7O0FBSTlCLGlFQUFlWixlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdC50c3g/YTE5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgQ29udGVudExvYWRlciBmcm9tICdyZWFjdC1jb250ZW50LWxvYWRlcic7XHJcblxyXG5jb25zdCBTaW5nbGVEb3RMb2FkZXIgPSAocHJvcHM6IGFueSkgPT4gKFxyXG4gIDxDb250ZW50TG9hZGVyXHJcbiAgICBzcGVlZD17Mn1cclxuICAgIHdpZHRoPXszMn1cclxuICAgIGhlaWdodD17MzJ9XHJcbiAgICB2aWV3Qm94PVwiMCAwIDMyIDMyXCJcclxuICAgIHVuaXF1ZUtleT1cInNpbmdsZS1kb3QtbG9hZGVyXCJcclxuICAgIGJhY2tncm91bmRDb2xvcj1cIiNjZGNkY2RcIlxyXG4gICAgZm9yZWdyb3VuZENvbG9yPVwiI2RkZFwiXHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgPlxyXG4gICAgPGNpcmNsZSBjeD1cIjE2XCIgY3k9XCIxNlwiIHI9XCIxNlwiIC8+XHJcbiAgPC9Db250ZW50TG9hZGVyPlxyXG4pO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU2luZ2xlRG90TG9hZGVyO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb250ZW50TG9hZGVyIiwiU2luZ2xlRG90TG9hZGVyIiwicHJvcHMiLCJzcGVlZCIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsInVuaXF1ZUtleSIsImJhY2tncm91bmRDb2xvciIsImZvcmVncm91bmRDb2xvciIsImNpcmNsZSIsImN4IiwiY3kiLCJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Loaders/SingleDot.tsx\n");

/***/ }),

/***/ "./components/Panel.tsx":
/*!******************************!*\
  !*** ./components/Panel.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Panel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__]);\n_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction Panel({ expandable, expanded, header, children, onClick }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`rounded-xl p-4 transition`, expandable ? expanded ? \"cursor-pointer\" : \"hover:bg-foreground/10 cursor-pointer\" : \"\"),\n                onClick: ()=>{\n                    expandable && onClick?.();\n                },\n                children: header\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Panel.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`p-4`, expandable && !expanded ? \"hidden\" : \"\"),\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Panel.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF1QztBQUV4QixTQUFTQyxNQUFNLEVBQzVCQyxVQUFVLEVBQ1ZDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyxRQUFRLEVBQ1JDLE9BQU8sRUFPUjtJQUNDLHFCQUNFOzswQkFDRSw4REFBQ0M7Z0JBQ0NDLFdBQVdSLHFEQUFFQSxDQUNYLENBQUMseUJBQXlCLENBQUMsRUFDM0JFLGFBQ0lDLFdBQ0UsbUJBQ0EsMENBQ0Y7Z0JBRU5HLFNBQVM7b0JBQ1BKLGNBQWNJO2dCQUNoQjswQkFFQ0Y7Ozs7OzswQkFFSCw4REFBQ0c7Z0JBQUlDLFdBQVdSLHFEQUFFQSxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUVFLGNBQWMsQ0FBQ0MsV0FBVyxXQUFXOzBCQUM1REU7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL1BhbmVsLnRzeD9lMDM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNuIH0gZnJvbSAnQFJvb3QvdXRpbHMvdXRpbHMnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFuZWwoe1xyXG4gIGV4cGFuZGFibGUsXHJcbiAgZXhwYW5kZWQsXHJcbiAgaGVhZGVyLFxyXG4gIGNoaWxkcmVuLFxyXG4gIG9uQ2xpY2ssXHJcbn06IHtcclxuICBleHBhbmRhYmxlPzogYm9vbGVhbjtcclxuICBleHBhbmRlZD86IGJvb2xlYW47XHJcbiAgaGVhZGVyOiBSZWFjdC5SZWFjdE5vZGU7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxuICBvbkNsaWNrPzogKCkgPT4gdm9pZDtcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8ZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgIGByb3VuZGVkLXhsIHAtNCB0cmFuc2l0aW9uYCxcclxuICAgICAgICAgIGV4cGFuZGFibGVcclxuICAgICAgICAgICAgPyBleHBhbmRlZFxyXG4gICAgICAgICAgICAgID8gJ2N1cnNvci1wb2ludGVyJ1xyXG4gICAgICAgICAgICAgIDogJ2hvdmVyOmJnLWZvcmVncm91bmQvMTAgY3Vyc29yLXBvaW50ZXInXHJcbiAgICAgICAgICAgIDogJycsXHJcbiAgICAgICAgKX1cclxuICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICBleHBhbmRhYmxlICYmIG9uQ2xpY2s/LigpO1xyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICB7aGVhZGVyfVxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKGBwLTRgLCBleHBhbmRhYmxlICYmICFleHBhbmRlZCA/ICdoaWRkZW4nIDogJycpfT5cclxuICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiY24iLCJQYW5lbCIsImV4cGFuZGFibGUiLCJleHBhbmRlZCIsImhlYWRlciIsImNoaWxkcmVuIiwib25DbGljayIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/Panel.tsx\n");

/***/ }),

/***/ "./components/Paragraph.tsx":
/*!**********************************!*\
  !*** ./components/Paragraph.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Paragraph = ({ icon, title, description })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1 mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 w-4 text-ch\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-bold text-ch\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm break-words text-cs\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Paragraph);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BhcmFncmFwaC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLE1BQU1BLFlBQVksQ0FBQyxFQUNqQkMsSUFBSSxFQUNKQyxLQUFLLEVBQ0xDLFdBQVcsRUFLWjtJQUNDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FBbUJKOzs7Ozs7a0NBQ2xDLDhEQUFDSzt3QkFBR0QsV0FBVTtrQ0FBNkJIOzs7Ozs7Ozs7Ozs7MEJBRTdDLDhEQUFDSztnQkFBRUYsV0FBVTswQkFBK0JGOzs7Ozs7Ozs7Ozs7QUFHbEQ7QUFFQSxpRUFBZUgsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvUGFyYWdyYXBoLnRzeD80YjNkIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFBhcmFncmFwaCA9ICh7XHJcbiAgaWNvbixcclxuICB0aXRsZSxcclxuICBkZXNjcmlwdGlvbixcclxufToge1xyXG4gIGljb246IFJlYWN0LlJlYWN0Tm9kZTtcclxuICB0aXRsZTogc3RyaW5nIHwgUmVhY3QuUmVhY3ROb2RlO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgbWItNFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWNoXCI+e2ljb259PC9kaXY+XHJcbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtY2hcIj57dGl0bGV9PC9oMz5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gYnJlYWstd29yZHMgdGV4dC1jc1wiPntkZXNjcmlwdGlvbn08L3A+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgUGFyYWdyYXBoO1xyXG4iXSwibmFtZXMiOlsiUGFyYWdyYXBoIiwiaWNvbiIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Paragraph.tsx\n");

/***/ }),

/***/ "./components/RadialProgress/index.tsx":
/*!*********************************************!*\
  !*** ./components/RadialProgress/index.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RadialProgress: () => (/* binding */ RadialProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst RadialProgress = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/recharts\"), __webpack_require__.e(\"components_RadialProgress_RadialProgress_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! @Components/RadialProgress/RadialProgress */ \"./components/RadialProgress/RadialProgress.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\RadialProgress\\\\index.tsx -> \" + \"@Components/RadialProgress/RadialProgress\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            height: 66,\n            width: 66\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RadialProgress\\\\index.tsx\",\n            lineNumber: 8,\n            columnNumber: 20\n        }, undefined)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTREO0FBQ3pCO0FBRTVCLE1BQU1FLGlCQUFpQkQsbURBQU9BLENBQ25DLElBQU0sMlNBQU87Ozs7OztJQUVYRSxLQUFLO0lBQ0xDLFNBQVMsa0JBQU0sOERBQUNKLHFFQUFlQTtZQUFDSyxRQUFRO1lBQUlDLE9BQU87Ozs7OztHQUVyRCIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvUmFkaWFsUHJvZ3Jlc3MvaW5kZXgudHN4P2ZmMDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFNpbmdsZURvdExvYWRlciBmcm9tICdAQ29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdCc7XHJcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYyc7XHJcblxyXG5leHBvcnQgY29uc3QgUmFkaWFsUHJvZ3Jlc3MgPSBkeW5hbWljKFxyXG4gICgpID0+IGltcG9ydCgnQENvbXBvbmVudHMvUmFkaWFsUHJvZ3Jlc3MvUmFkaWFsUHJvZ3Jlc3MnKSxcclxuICB7XHJcbiAgICBzc3I6IGZhbHNlLFxyXG4gICAgbG9hZGluZzogKCkgPT4gPFNpbmdsZURvdExvYWRlciBoZWlnaHQ9ezY2fSB3aWR0aD17NjZ9IC8+LFxyXG4gIH0sXHJcbik7XHJcbiJdLCJuYW1lcyI6WyJTaW5nbGVEb3RMb2FkZXIiLCJkeW5hbWljIiwiUmFkaWFsUHJvZ3Jlc3MiLCJzc3IiLCJsb2FkaW5nIiwiaGVpZ2h0Iiwid2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/RadialProgress/index.tsx\n");

/***/ }),

/***/ "./components/RecaptchaDeclaration.tsx":
/*!*********************************************!*\
  !*** ./components/RecaptchaDeclaration.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecaptchaDeclaration: () => (/* binding */ RecaptchaDeclaration)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst RecaptchaDeclaration = ({ className = \"text-sm\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: className,\n        children: [\n            \"This site is protected by reCAPTCHA and the Google\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"underline text-link cursor-pointer\",\n                href: \"https://policies.google.com/privacy\",\n                children: \"Privacy Policy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"and\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"underline text-link cursor-pointer\",\n                href: \"https://policies.google.com/terms\",\n                children: \"Terms of Service\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"apply.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JlY2FwdGNoYURlY2xhcmF0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFbkIsTUFBTUMsdUJBQXVCLENBQUMsRUFDbkNDLFlBQVksU0FBUyxFQUd0QjtJQUNDLHFCQUNFLDhEQUFDQztRQUFFRCxXQUFXQTs7WUFBVztZQUM0QjswQkFDbkQsOERBQUNFO2dCQUNDRixXQUFVO2dCQUNWRyxNQUFLOzBCQUNOOzs7Ozs7WUFFSTtZQUFJO1lBQ0w7MEJBQ0osOERBQUNEO2dCQUNDRixXQUFVO2dCQUNWRyxNQUFLOzBCQUNOOzs7Ozs7WUFFSTtZQUFJOzs7Ozs7O0FBSWYsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvUmVjYXB0Y2hhRGVjbGFyYXRpb24udHN4PzBmMWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuXHJcbmV4cG9ydCBjb25zdCBSZWNhcHRjaGFEZWNsYXJhdGlvbiA9ICh7XHJcbiAgY2xhc3NOYW1lID0gJ3RleHQtc20nLFxyXG59OiB7XHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG59KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxwIGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cclxuICAgICAgVGhpcyBzaXRlIGlzIHByb3RlY3RlZCBieSByZUNBUFRDSEEgYW5kIHRoZSBHb29nbGV7JyAnfVxyXG4gICAgICA8YVxyXG4gICAgICAgIGNsYXNzTmFtZT1cInVuZGVybGluZSB0ZXh0LWxpbmsgY3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgIGhyZWY9XCJodHRwczovL3BvbGljaWVzLmdvb2dsZS5jb20vcHJpdmFjeVwiXHJcbiAgICAgID5cclxuICAgICAgICBQcml2YWN5IFBvbGljeVxyXG4gICAgICA8L2E+eycgJ31cclxuICAgICAgYW5keycgJ31cclxuICAgICAgPGFcclxuICAgICAgICBjbGFzc05hbWU9XCJ1bmRlcmxpbmUgdGV4dC1saW5rIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICBocmVmPVwiaHR0cHM6Ly9wb2xpY2llcy5nb29nbGUuY29tL3Rlcm1zXCJcclxuICAgICAgPlxyXG4gICAgICAgIFRlcm1zIG9mIFNlcnZpY2VcclxuICAgICAgPC9hPnsnICd9XHJcbiAgICAgIGFwcGx5LlxyXG4gICAgPC9wPlxyXG4gICk7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlJlY2FwdGNoYURlY2xhcmF0aW9uIiwiY2xhc3NOYW1lIiwicCIsImEiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/RecaptchaDeclaration.tsx\n");

/***/ }),

/***/ "./components/Tasks/TaskSummary.tsx":
/*!******************************************!*\
  !*** ./components/Tasks/TaskSummary.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Hooks/useTaskParticipation */ \"./hooks/useTaskParticipation.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./app-store */ \"./components/Tasks/app-store.ts\");\n/* harmony import */ var _app_store_helper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./app-store.helper */ \"./components/Tasks/app-store.helper.ts\");\n/* harmony import */ var _components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/AppStoreIconRenderer */ \"./components/Tasks/components/AppStoreIconRenderer.tsx\");\n/* harmony import */ var _hooks_useGetTasks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./hooks/useGetTasks */ \"./components/Tasks/hooks/useGetTasks.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__, _app_store__WEBPACK_IMPORTED_MODULE_5__, _components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_7__, _hooks_useGetTasks__WEBPACK_IMPORTED_MODULE_8__]);\n([_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__, _app_store__WEBPACK_IMPORTED_MODULE_5__, _components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_7__, _hooks_useGetTasks__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst TaskSummaryTag = ({ task, verified, onClick })=>{\n    const { appType, taskType, title } = task;\n    const appKey = task.appKey;\n    const taskKey = task.taskKey;\n    const app = (0,_app_store__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_app_store_helper__WEBPACK_IMPORTED_MODULE_6__.selectAppByType)(appType, appKey));\n    const appTask = (0,_app_store__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_app_store_helper__WEBPACK_IMPORTED_MODULE_6__.selectTaskByType)(appType, taskType, {\n        appKey,\n        taskKey\n    }));\n    if (!app || !appTask) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n        lineNumber: 29,\n        columnNumber: 32\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `text-xs cursor-pointer component-bg-hover border border-foreground/20 rounded py-1.5 px-2 flex space-x-1 items-center`,\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                iconKey: task.taskType,\n                className: \"h-4 w-4 flex-shrink-0\",\n                color: appTask.config?.color || app?.config.color,\n                url: appTask?.config?.iconUrl || app?.config.iconUrl\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"line-clamp-1\",\n                children: [\n                    \" \",\n                    title\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                size: 16,\n                className: \"text-primary-foreground bg-primary rounded-full p-1 flex-shrink-0\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\nconst TaskSummaryList = ({ projectEvent, taskIds })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { data, loading: taskLoading } = (0,_hooks_useGetTasks__WEBPACK_IMPORTED_MODULE_8__.useGetTasks)(projectEvent.id);\n    const { data: taskParticipation, loading: isParticipationLoading } = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_1__.useUserCurrentTaskParticipationMap)(projectEvent.id);\n    let tasks = (data?.pTasks || []).filter((task)=>taskIds.findIndex((id)=>task.id === id) >= 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-wrap gap-1\",\n        children: tasks.map((task, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TaskSummaryTag, {\n                task: task,\n                verified: !!(taskParticipation?.get(task.id || \"\")?.status == _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID),\n                onClick: ()=>{\n                    if (!task) return;\n                    router.query[\"taskid\"] = task.id;\n                    router.push(router, undefined, {\n                        shallow: true\n                    });\n                }\n            }, key, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskSummaryList);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/TaskSummary.tsx\n");

/***/ }),

/***/ "./components/TransactionResult.tsx":
/*!******************************************!*\
  !*** ./components/TransactionResult.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionResult: () => (/* binding */ TransactionResult)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _BlockExplorerLink__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BlockExplorerLink */ \"./components/BlockExplorerLink.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Loaders/SpinLoader */ \"./components/Loaders/SpinLoader.tsx\");\n\n\n\n\nfunction TransactionResult({ blockchain, tx, txHash }) {\n    const [broadcasting, setBroadcasting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let mounted = true;\n        if (!tx || !setBroadcasting || !setError) return;\n        const wait = async ()=>{\n            mounted && setBroadcasting(true);\n            try {\n                await tx.wait();\n            } catch (err) {\n                mounted && setError(true);\n            }\n            mounted && setBroadcasting(false);\n        };\n        wait();\n        return ()=>{\n            mounted = false;\n        };\n    }, [\n        tx,\n        setBroadcasting,\n        setError\n    ]);\n    const hash = tx?.hash ?? txHash;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            broadcasting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-bold\",\n                        children: \"Waiting for transaction to be included in the block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"Transaction failed due to \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 52,\n                columnNumber: 17\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BlockExplorerLink__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                hash: hash,\n                blockExplorerUrls: blockchain.blockExplorerUrls\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TransactionResult.tsx\n");

/***/ }),

/***/ "./components/Web3Wallet/Dotsama/DotsamaWallet.tsx":
/*!*********************************************************!*\
  !*** ./components/Web3Wallet/Dotsama/DotsamaWallet.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DotsamaWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/DropdownList/DropdownList */ \"./components/DropdownList/DropdownList.tsx\");\n/* harmony import */ var _Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/DropdownList/DropdownListItem */ \"./components/DropdownList/DropdownListItem.tsx\");\n/* harmony import */ var _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tasks/components/AppStoreIconRenderer */ \"./components/Tasks/components/AppStoreIconRenderer.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @polkadot/extension-dapp */ \"@polkadot/extension-dapp\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../Web3WalletRenderer */ \"./components/Web3Wallet/Web3WalletRenderer.tsx\");\n/* harmony import */ var _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @Root/context/AppContext */ \"./context/AppContext.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__, _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__, _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_6__, _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_9__]);\n([_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__, _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__, _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_6__, _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\nfunction DotsamaWallet({ onError, ...props }) {\n    const wallets = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.useWalletStore)((state)=>state.walletCategory.find((category)=>category.categoryType === _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.Web3WalletCategoryType.DOTSAMA))?.wallets || [];\n    const excludedWallets = props.excludedWallets ?? [];\n    const { state: { isWidget } } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_9__.useAppContext)();\n    if (isWidget) {\n        excludedWallets.push(_airlyft_types__WEBPACK_IMPORTED_MODULE_10__.Web3WalletType.DOTSAMA_POLKADOT_JS, _airlyft_types__WEBPACK_IMPORTED_MODULE_10__.Web3WalletType.DOTSAMA_NOVA);\n    }\n    let filteredWallets = wallets;\n    if (excludedWallets?.length) {\n        filteredWallets = wallets.filter((wallet)=>!excludedWallets?.includes(wallet.walletType));\n    }\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(filteredWallets?.[0]);\n    const [injectedExtensions, setInjectedExtensions] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        if (!window || !_polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_6__.isWeb3Injected || !filteredWallets?.length) {\n            return;\n        }\n        const extensions = window.injectedWeb3;\n        setInjectedExtensions(extensions);\n    }, [\n        window,\n        _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_6__.isWeb3Injected,\n        filteredWallets\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-5 mt-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-[90]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    data: filteredWallets,\n                    selected: selected,\n                    onChange: (item)=>setSelected(item),\n                    renderItem: (item, isButton)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: item.title,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                iconKey: item.walletType,\n                                className: \"h-8 w-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 17\n                            }, void 0),\n                            selected: item.walletType === selected?.walletType,\n                            button: isButton\n                        }, item.title, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            selected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                walletType: selected.walletType,\n                categoryType: _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.Web3WalletCategoryType.DOTSAMA,\n                placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                props: {\n                    wallet: selected,\n                    onError: (err)=>{\n                        (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n                            title: \"Error\",\n                            text: err,\n                            type: \"error\"\n                        });\n                        onError?.(err);\n                    },\n                    ...props,\n                    ...selected.config?.name ? {\n                        injectedExtension: injectedExtensions[selected.config.name]\n                    } : {},\n                    excludedWallets\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\n");

/***/ }),

/***/ "./components/Web3Wallet/Evm/EvmWallet.tsx":
/*!*************************************************!*\
  !*** ./components/Web3Wallet/Evm/EvmWallet.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EvmWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/DropdownList/DropdownList */ \"./components/DropdownList/DropdownList.tsx\");\n/* harmony import */ var _Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/DropdownList/DropdownListItem */ \"./components/DropdownList/DropdownListItem.tsx\");\n/* harmony import */ var _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Tasks/components/AppStoreIconRenderer */ \"./components/Tasks/components/AppStoreIconRenderer.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Web3WalletRenderer */ \"./components/Web3Wallet/Web3WalletRenderer.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_1__, _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_3__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_4__]);\n([_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_1__, _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_3__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction EvmWallet({ onError, ...props }) {\n    let wallets = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__.useWalletStore)((state)=>state.walletCategory.find((category)=>category.categoryType === _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__.Web3WalletCategoryType.EVM))?.wallets || [];\n    if (props.excludedWallets?.length) {\n        wallets = wallets.filter((wallet)=>!props.excludedWallets?.includes(wallet.walletType));\n    }\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(wallets?.[0]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-5 mt-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                data: wallets,\n                selected: selected,\n                onChange: (item)=>setSelected(item),\n                renderItem: (item, isButton)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: item.title,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            iconKey: item.walletType,\n                            className: \"h-8 w-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 15\n                        }, void 0),\n                        selected: item.walletType === selected?.walletType,\n                        button: isButton\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            selected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                walletType: selected.walletType,\n                categoryType: _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__.Web3WalletCategoryType.EVM,\n                placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                props: {\n                    wallet: selected,\n                    onError: (err)=>{\n                        (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n                            title: \"Error\",\n                            text: err,\n                            type: \"error\"\n                        });\n                        onError?.(err);\n                    },\n                    ...props\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1dlYjNXYWxsZXQvRXZtL0V2bVdhbGxldC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFpRTtBQUNRO0FBQ1k7QUFDbkM7QUFNakI7QUFDQTtBQUNrQjtBQUVwQyxTQUFTUSxVQUFVLEVBQ2hDQyxPQUFPLEVBQ1AsR0FBR0MsT0FDNEI7SUFDL0IsSUFBSUMsVUFDRk4sdUVBQWNBLENBQUMsQ0FBQ08sUUFDZEEsTUFBTUMsY0FBYyxDQUFDQyxJQUFJLENBQ3ZCLENBQUNDLFdBQWFBLFNBQVNDLFlBQVksS0FBS1osMkVBQXNCQSxDQUFDYSxHQUFHLElBRW5FTixXQUFXLEVBQUU7SUFFbEIsSUFBSUQsTUFBTVEsZUFBZSxFQUFFQyxRQUFRO1FBQ2pDUixVQUFVQSxRQUFRUyxNQUFNLENBQ3RCLENBQUNDLFNBQVcsQ0FBQ1gsTUFBTVEsZUFBZSxFQUFFSSxTQUFTRCxPQUFPRSxVQUFVO0lBRWxFO0lBRUEsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUduQiwrQ0FBUUEsQ0FDdENLLFNBQVMsQ0FBQyxFQUFFO0lBR2QscUJBQ0UsOERBQUNlO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDM0IsNkVBQVlBO2dCQUNYNEIsTUFBTWpCO2dCQUNOYSxVQUFVQTtnQkFDVkssVUFBVSxDQUFDQyxPQUFTTCxZQUFZSztnQkFDaENDLFlBQVksQ0FBQ0QsTUFBTUUseUJBQ2pCLDhEQUFDL0IsaUZBQWdCQTt3QkFDZmdDLE9BQU9ILEtBQUtHLEtBQUs7d0JBQ2pCQyxvQkFDRSw4REFBQ2hDLHlGQUFvQkE7NEJBQ25CaUMsU0FBU0wsS0FBS1AsVUFBVTs0QkFDeEJJLFdBQVU7Ozs7Ozt3QkFHZEgsVUFBVU0sS0FBS1AsVUFBVSxLQUFLQyxVQUFVRDt3QkFDeENhLFFBQVFKOzs7Ozs7Ozs7OztZQUtiUiwwQkFDQyw4REFBQ2pCLDJEQUFjQTtnQkFDYmdCLFlBQVlDLFNBQVNELFVBQVU7Z0JBQy9CUCxjQUFjWiwyRUFBc0JBLENBQUNhLEdBQUc7Z0JBQ3hDb0IsMkJBQWE7Z0JBQ2IzQixPQUFPO29CQUNMVyxRQUFRRztvQkFDUmYsU0FBUyxDQUFDNkI7d0JBQ1JuQyx1RUFBT0EsQ0FBQzs0QkFDTjhCLE9BQU87NEJBQ1BNLE1BQU1EOzRCQUNORSxNQUFNO3dCQUNSO3dCQUNBL0IsVUFBVTZCO29CQUNaO29CQUNBLEdBQUc1QixLQUFLO2dCQUNWOzs7Ozs7Ozs7Ozs7QUFLViIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvV2ViM1dhbGxldC9Fdm0vRXZtV2FsbGV0LnRzeD8zNWVmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBEcm9wZG93bkxpc3QgZnJvbSAnQENvbXBvbmVudHMvRHJvcGRvd25MaXN0L0Ryb3Bkb3duTGlzdCc7XHJcbmltcG9ydCBEcm9wZG93bkxpc3RJdGVtIGZyb20gJ0BDb21wb25lbnRzL0Ryb3Bkb3duTGlzdC9Ecm9wZG93bkxpc3RJdGVtJztcclxuaW1wb3J0IEFwcFN0b3JlSWNvblJlbmRlcmVyIGZyb20gJ0BDb21wb25lbnRzL1Rhc2tzL2NvbXBvbmVudHMvQXBwU3RvcmVJY29uUmVuZGVyZXInO1xyXG5pbXBvcnQgdG9hc3RlciBmcm9tICdAQ29tcG9uZW50cy9Ub2FzdGVyL1RvYXN0ZXInO1xyXG5pbXBvcnQge1xyXG4gIEV2bVdhbGxldFByb3BzLFxyXG4gIFdlYjNXYWxsZXQsXHJcbiAgV2ViM1dhbGxldENhdGVnb3J5VHlwZSxcclxuICB1c2VXYWxsZXRTdG9yZSxcclxufSBmcm9tICdAYWlybHlmdC93ZWIzLWV2bS1ob29rcyc7XHJcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgV2FsbGV0UmVuZGVyZXIgZnJvbSAnLi4vV2ViM1dhbGxldFJlbmRlcmVyJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEV2bVdhbGxldCh7XHJcbiAgb25FcnJvcixcclxuICAuLi5wcm9wc1xyXG59OiBPbWl0PEV2bVdhbGxldFByb3BzLCAnd2FsbGV0Jz4pIHtcclxuICBsZXQgd2FsbGV0cyA9XHJcbiAgICB1c2VXYWxsZXRTdG9yZSgoc3RhdGUpID0+XHJcbiAgICAgIHN0YXRlLndhbGxldENhdGVnb3J5LmZpbmQoXHJcbiAgICAgICAgKGNhdGVnb3J5KSA9PiBjYXRlZ29yeS5jYXRlZ29yeVR5cGUgPT09IFdlYjNXYWxsZXRDYXRlZ29yeVR5cGUuRVZNLFxyXG4gICAgICApLFxyXG4gICAgKT8ud2FsbGV0cyB8fCBbXTtcclxuXHJcbiAgaWYgKHByb3BzLmV4Y2x1ZGVkV2FsbGV0cz8ubGVuZ3RoKSB7XHJcbiAgICB3YWxsZXRzID0gd2FsbGV0cy5maWx0ZXIoXHJcbiAgICAgICh3YWxsZXQpID0+ICFwcm9wcy5leGNsdWRlZFdhbGxldHM/LmluY2x1ZGVzKHdhbGxldC53YWxsZXRUeXBlKSxcclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBjb25zdCBbc2VsZWN0ZWQsIHNldFNlbGVjdGVkXSA9IHVzZVN0YXRlPFdlYjNXYWxsZXQgfCB1bmRlZmluZWQ+KFxyXG4gICAgd2FsbGV0cz8uWzBdLFxyXG4gICk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBzcGFjZS15LTUgbXQtNFwiPlxyXG4gICAgICA8RHJvcGRvd25MaXN0XHJcbiAgICAgICAgZGF0YT17d2FsbGV0c31cclxuICAgICAgICBzZWxlY3RlZD17c2VsZWN0ZWR9XHJcbiAgICAgICAgb25DaGFuZ2U9eyhpdGVtKSA9PiBzZXRTZWxlY3RlZChpdGVtKX1cclxuICAgICAgICByZW5kZXJJdGVtPXsoaXRlbSwgaXNCdXR0b24pID0+IChcclxuICAgICAgICAgIDxEcm9wZG93bkxpc3RJdGVtXHJcbiAgICAgICAgICAgIHRpdGxlPXtpdGVtLnRpdGxlfVxyXG4gICAgICAgICAgICBpY29uPXtcclxuICAgICAgICAgICAgICA8QXBwU3RvcmVJY29uUmVuZGVyZXJcclxuICAgICAgICAgICAgICAgIGljb25LZXk9e2l0ZW0ud2FsbGV0VHlwZX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LThcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgc2VsZWN0ZWQ9e2l0ZW0ud2FsbGV0VHlwZSA9PT0gc2VsZWN0ZWQ/LndhbGxldFR5cGV9XHJcbiAgICAgICAgICAgIGJ1dHRvbj17aXNCdXR0b259XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICl9XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICB7c2VsZWN0ZWQgJiYgKFxyXG4gICAgICAgIDxXYWxsZXRSZW5kZXJlcjxFdm1XYWxsZXRQcm9wcz5cclxuICAgICAgICAgIHdhbGxldFR5cGU9e3NlbGVjdGVkLndhbGxldFR5cGV9XHJcbiAgICAgICAgICBjYXRlZ29yeVR5cGU9e1dlYjNXYWxsZXRDYXRlZ29yeVR5cGUuRVZNfVxyXG4gICAgICAgICAgcGxhY2Vob2xkZXI9ezw+PC8+fVxyXG4gICAgICAgICAgcHJvcHM9e3tcclxuICAgICAgICAgICAgd2FsbGV0OiBzZWxlY3RlZCxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycikgPT4ge1xyXG4gICAgICAgICAgICAgIHRvYXN0ZXIoe1xyXG4gICAgICAgICAgICAgICAgdGl0bGU6ICdFcnJvcicsXHJcbiAgICAgICAgICAgICAgICB0ZXh0OiBlcnIsXHJcbiAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLFxyXG4gICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgIG9uRXJyb3I/LihlcnIpO1xyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAuLi5wcm9wcyxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgLz5cclxuICAgICAgKX1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIkRyb3Bkb3duTGlzdCIsIkRyb3Bkb3duTGlzdEl0ZW0iLCJBcHBTdG9yZUljb25SZW5kZXJlciIsInRvYXN0ZXIiLCJXZWIzV2FsbGV0Q2F0ZWdvcnlUeXBlIiwidXNlV2FsbGV0U3RvcmUiLCJ1c2VTdGF0ZSIsIldhbGxldFJlbmRlcmVyIiwiRXZtV2FsbGV0Iiwib25FcnJvciIsInByb3BzIiwid2FsbGV0cyIsInN0YXRlIiwid2FsbGV0Q2F0ZWdvcnkiLCJmaW5kIiwiY2F0ZWdvcnkiLCJjYXRlZ29yeVR5cGUiLCJFVk0iLCJleGNsdWRlZFdhbGxldHMiLCJsZW5ndGgiLCJmaWx0ZXIiLCJ3YWxsZXQiLCJpbmNsdWRlcyIsIndhbGxldFR5cGUiLCJzZWxlY3RlZCIsInNldFNlbGVjdGVkIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGF0YSIsIm9uQ2hhbmdlIiwiaXRlbSIsInJlbmRlckl0ZW0iLCJpc0J1dHRvbiIsInRpdGxlIiwiaWNvbiIsImljb25LZXkiLCJidXR0b24iLCJwbGFjZWhvbGRlciIsImVyciIsInRleHQiLCJ0eXBlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Evm/EvmWallet.tsx\n");

/***/ }),

/***/ "./components/Web3Wallet/Web3WalletRenderer.tsx":
/*!******************************************************!*\
  !*** ./components/Web3Wallet/Web3WalletRenderer.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WalletRenderer),\n/* harmony export */   rendererPath: () => (/* binding */ rendererPath)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/utils/string-utils */ \"./utils/string-utils.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Loaders/ParagraphLoader */ \"./components/Loaders/ParagraphLoader.tsx\");\n\n\n\n\n\nconst importProvider = (path, placeholder)=>next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__(\"./components/Web3Wallet lazy recursive ^\\\\.\\\\/.*$\")(`./${path}`), {\n        ssr: false,\n        loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n                lineNumber: 20,\n                columnNumber: 20\n            }, undefined)\n    });\nconst rendererPath = (categoryType, walletType)=>{\n    const path = (0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.capitalizeFirstLetter)((0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.camelCased)(categoryType));\n    const fileName = (0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.capitalizeFirstLetter)((0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.camelCased)(walletType));\n    return `${path}/${fileName}`;\n};\nfunction WalletRenderer({ categoryType, walletType, placeholder, props }) {\n    const View = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>importProvider(rendererPath(categoryType, walletType), placeholder), [\n        walletType,\n        categoryType\n    ]);\n    return View ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(View, {\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n        lineNumber: 44,\n        columnNumber: 17\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n        lineNumber: 44,\n        columnNumber: 48\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Web3WalletRenderer.tsx\n");

/***/ }),

/***/ "./hooks/useGetBlockchain.ts":
/*!***********************************!*\
  !*** ./hooks/useGetBlockchain.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGetBlockchain: () => (/* binding */ useGetBlockchain)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst GET_BLOCKCHAIN = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql)`\r\n  query blockchain($id: ID!) {\r\n    blockchain(id: $id) {\r\n      id\r\n      name\r\n      chainId\r\n      icon\r\n      blockExplorerUrls\r\n      rpcUrls\r\n      nativeCurrency\r\n      decimals\r\n      type\r\n    }\r\n  }\r\n`;\nfunction useGetBlockchain(id) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.useQuery)(GET_BLOCKCHAIN, {\n        variables: {\n            id\n        },\n        skip: !id\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VHZXRCbG9ja2NoYWluLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQztBQUcvQyxNQUFNRSxpQkFBaUJGLG1EQUFHLENBQUM7Ozs7Ozs7Ozs7Ozs7O0FBYzNCLENBQUM7QUFFTSxTQUFTRyxpQkFBaUJDLEVBQVU7SUFDekMsT0FBT0gsd0RBQVFBLENBQ2JDLGdCQUNBO1FBQ0VHLFdBQVc7WUFDVEQ7UUFDRjtRQUNBRSxNQUFNLENBQUNGO0lBQ1Q7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2hvb2tzL3VzZUdldEJsb2NrY2hhaW4udHM/ZGMxYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBncWwsIHVzZVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnO1xyXG5pbXBvcnQgeyBCbG9ja2NoYWluLCBRdWVyeV9ibG9ja2NoYWluQXJncyB9IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuXHJcbmNvbnN0IEdFVF9CTE9DS0NIQUlOID0gZ3FsYFxyXG4gIHF1ZXJ5IGJsb2NrY2hhaW4oJGlkOiBJRCEpIHtcclxuICAgIGJsb2NrY2hhaW4oaWQ6ICRpZCkge1xyXG4gICAgICBpZFxyXG4gICAgICBuYW1lXHJcbiAgICAgIGNoYWluSWRcclxuICAgICAgaWNvblxyXG4gICAgICBibG9ja0V4cGxvcmVyVXJsc1xyXG4gICAgICBycGNVcmxzXHJcbiAgICAgIG5hdGl2ZUN1cnJlbmN5XHJcbiAgICAgIGRlY2ltYWxzXHJcbiAgICAgIHR5cGVcclxuICAgIH1cclxuICB9XHJcbmA7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlR2V0QmxvY2tjaGFpbihpZDogc3RyaW5nKSB7XHJcbiAgcmV0dXJuIHVzZVF1ZXJ5PHsgYmxvY2tjaGFpbjogQmxvY2tjaGFpbiB9LCBRdWVyeV9ibG9ja2NoYWluQXJncz4oXHJcbiAgICBHRVRfQkxPQ0tDSEFJTixcclxuICAgIHtcclxuICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgaWQsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNraXA6ICFpZCxcclxuICAgIH0sXHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiZ3FsIiwidXNlUXVlcnkiLCJHRVRfQkxPQ0tDSEFJTiIsInVzZUdldEJsb2NrY2hhaW4iLCJpZCIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./hooks/useGetBlockchain.ts\n");

/***/ }),

/***/ "./hooks/useGiveawayTxHash.ts":
/*!************************************!*\
  !*** ./hooks/useGiveawayTxHash.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGiveawayTxHash: () => (/* binding */ useGiveawayTxHash)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LS_GIVEAWAY_HASH_KEY = \"air_gth\";\nfunction useGiveawayTxHash(id) {\n    const [txHash, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const [initialized, setInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handle = ()=>{\n            if (!initialized) return;\n            setLoading(true);\n            const tx = getAll();\n            setState(tx || \"\");\n            setLoading(false);\n        };\n        addEventListener(\"storage\", handle);\n        return ()=>{\n            removeEventListener(\"storage\", handle);\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setInitialized(false);\n        setLoading(true);\n        const tx = getAll();\n        setState(tx || \"\");\n        setInitialized(true);\n        setLoading(false);\n    }, [\n        id\n    ]);\n    const getAll = ()=>{\n        try {\n            const parsedFs = JSON.parse(localStorage.getItem(LS_GIVEAWAY_HASH_KEY) || \"{}\");\n            return parsedFs[id];\n        } catch (err) {}\n    };\n    const update = (txHash)=>{\n        if (!initialized) return;\n        sync(txHash);\n    };\n    const sync = (txHash)=>{\n        try {\n            const encodedFs = JSON.stringify({\n                ...JSON.parse(localStorage.getItem(LS_GIVEAWAY_HASH_KEY) || \"{}\"),\n                [id]: txHash\n            });\n            localStorage.setItem(LS_GIVEAWAY_HASH_KEY, encodedFs);\n            dispatchEvent(new Event(\"storage\"));\n        } catch (err) {}\n    };\n    return {\n        txHash,\n        initialized,\n        loading,\n        update\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VHaXZlYXdheVR4SGFzaC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEM7QUFJNUMsTUFBTUUsdUJBQXVCO0FBRXRCLFNBQVNDLGtCQUFrQkMsRUFBVTtJQUMxQyxNQUFNLENBQUNDLFFBQVFDLFNBQVMsR0FBR0wsK0NBQVFBO0lBRW5DLE1BQU0sQ0FBQ00sYUFBYUMsZUFBZSxHQUFHUCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNRLFNBQVNDLFdBQVcsR0FBR1QsK0NBQVFBLENBQUM7SUFFdkNELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTVcsU0FBUztZQUNiLElBQUksQ0FBQ0osYUFBYTtZQUNsQkcsV0FBVztZQUNYLE1BQU1FLEtBQUtDO1lBQ1hQLFNBQVNNLE1BQU07WUFFZkYsV0FBVztRQUNiO1FBQ0FJLGlCQUFpQixXQUFXSDtRQUU1QixPQUFPO1lBQ0xJLG9CQUFvQixXQUFXSjtRQUNqQztJQUNGO0lBRUFYLGdEQUFTQSxDQUFDO1FBQ1JRLGVBQWU7UUFDZkUsV0FBVztRQUNYLE1BQU1FLEtBQUtDO1FBQ1hQLFNBQVNNLE1BQU07UUFFZkosZUFBZTtRQUNmRSxXQUFXO0lBQ2IsR0FBRztRQUFDTjtLQUFHO0lBRVAsTUFBTVMsU0FBUztRQUNiLElBQUk7WUFDRixNQUFNRyxXQUFXQyxLQUFLQyxLQUFLLENBQ3pCQyxhQUFhQyxPQUFPLENBQUNsQix5QkFBeUI7WUFHaEQsT0FBT2MsUUFBUSxDQUFDWixHQUFHO1FBQ3JCLEVBQUUsT0FBT2lCLEtBQUssQ0FBQztJQUNqQjtJQUVBLE1BQU1DLFNBQVMsQ0FBQ2pCO1FBQ2QsSUFBSSxDQUFDRSxhQUFhO1FBRWxCZ0IsS0FBS2xCO0lBQ1A7SUFFQSxNQUFNa0IsT0FBTyxDQUFDbEI7UUFDWixJQUFJO1lBQ0YsTUFBTW1CLFlBQVlQLEtBQUtRLFNBQVMsQ0FBQztnQkFDL0IsR0FBR1IsS0FBS0MsS0FBSyxDQUFDQyxhQUFhQyxPQUFPLENBQUNsQix5QkFBeUIsS0FBSztnQkFDakUsQ0FBQ0UsR0FBRyxFQUFFQztZQUNSO1lBRUFjLGFBQWFPLE9BQU8sQ0FBQ3hCLHNCQUFzQnNCO1lBQzNDRyxjQUFjLElBQUlDLE1BQU07UUFDMUIsRUFBRSxPQUFPUCxLQUFLLENBQUM7SUFDakI7SUFFQSxPQUFPO1FBQUVoQjtRQUFRRTtRQUFhRTtRQUFTYTtJQUFPO0FBQ2hEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vaG9va3MvdXNlR2l2ZWF3YXlUeEhhc2gudHM/MmJiMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5cclxuZXhwb3J0IHR5cGUgR2l2ZWF3YXlUeEhhc2hTdGF0ZSA9IFJlY29yZDxzdHJpbmcsIHN0cmluZz47XHJcblxyXG5jb25zdCBMU19HSVZFQVdBWV9IQVNIX0tFWSA9ICdhaXJfZ3RoJztcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiB1c2VHaXZlYXdheVR4SGFzaChpZDogc3RyaW5nKSB7XHJcbiAgY29uc3QgW3R4SGFzaCwgc2V0U3RhdGVdID0gdXNlU3RhdGU8c3RyaW5nPigpO1xyXG5cclxuICBjb25zdCBbaW5pdGlhbGl6ZWQsIHNldEluaXRpYWxpemVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGUgPSAoKSA9PiB7XHJcbiAgICAgIGlmICghaW5pdGlhbGl6ZWQpIHJldHVybjtcclxuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcclxuICAgICAgY29uc3QgdHggPSBnZXRBbGwoKTtcclxuICAgICAgc2V0U3RhdGUodHggfHwgJycpO1xyXG5cclxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICB9O1xyXG4gICAgYWRkRXZlbnRMaXN0ZW5lcignc3RvcmFnZScsIGhhbmRsZSk7XHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgcmVtb3ZlRXZlbnRMaXN0ZW5lcignc3RvcmFnZScsIGhhbmRsZSk7XHJcbiAgICB9O1xyXG4gIH0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgc2V0SW5pdGlhbGl6ZWQoZmFsc2UpO1xyXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcclxuICAgIGNvbnN0IHR4ID0gZ2V0QWxsKCk7XHJcbiAgICBzZXRTdGF0ZSh0eCB8fCAnJyk7XHJcblxyXG4gICAgc2V0SW5pdGlhbGl6ZWQodHJ1ZSk7XHJcbiAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICB9LCBbaWRdKTtcclxuXHJcbiAgY29uc3QgZ2V0QWxsID0gKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcGFyc2VkRnMgPSBKU09OLnBhcnNlKFxyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKExTX0dJVkVBV0FZX0hBU0hfS0VZKSB8fCAne30nLFxyXG4gICAgICApIGFzIEdpdmVhd2F5VHhIYXNoU3RhdGU7XHJcblxyXG4gICAgICByZXR1cm4gcGFyc2VkRnNbaWRdO1xyXG4gICAgfSBjYXRjaCAoZXJyKSB7fVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IHVwZGF0ZSA9ICh0eEhhc2g6IHN0cmluZykgPT4ge1xyXG4gICAgaWYgKCFpbml0aWFsaXplZCkgcmV0dXJuO1xyXG5cclxuICAgIHN5bmModHhIYXNoKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBzeW5jID0gKHR4SGFzaDogc3RyaW5nKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBlbmNvZGVkRnMgPSBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgLi4uSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbShMU19HSVZFQVdBWV9IQVNIX0tFWSkgfHwgJ3t9JyksXHJcbiAgICAgICAgW2lkXTogdHhIYXNoLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKExTX0dJVkVBV0FZX0hBU0hfS0VZLCBlbmNvZGVkRnMpO1xyXG4gICAgICBkaXNwYXRjaEV2ZW50KG5ldyBFdmVudCgnc3RvcmFnZScpKTtcclxuICAgIH0gY2F0Y2ggKGVycikge31cclxuICB9O1xyXG5cclxuICByZXR1cm4geyB0eEhhc2gsIGluaXRpYWxpemVkLCBsb2FkaW5nLCB1cGRhdGUgfTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJMU19HSVZFQVdBWV9IQVNIX0tFWSIsInVzZUdpdmVhd2F5VHhIYXNoIiwiaWQiLCJ0eEhhc2giLCJzZXRTdGF0ZSIsImluaXRpYWxpemVkIiwic2V0SW5pdGlhbGl6ZWQiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImhhbmRsZSIsInR4IiwiZ2V0QWxsIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJwYXJzZWRGcyIsIkpTT04iLCJwYXJzZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJlcnIiLCJ1cGRhdGUiLCJzeW5jIiwiZW5jb2RlZEZzIiwic3RyaW5naWZ5Iiwic2V0SXRlbSIsImRpc3BhdGNoRXZlbnQiLCJFdmVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./hooks/useGiveawayTxHash.ts\n");

/***/ })

};
;
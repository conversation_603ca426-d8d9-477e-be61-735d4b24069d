"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Tasks_Twitter_Ugc_TwitterUgcBody_tsx";
exports.ids = ["components_Tasks_Twitter_Ugc_TwitterUgcBody_tsx"];
exports.modules = {

/***/ "./components/Tasks/Provider/ProviderButton.tsx":
/*!******************************************************!*\
  !*** ./components/Tasks/Provider/ProviderButton.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProviderButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Hooks/useUserEventConnection */ \"./hooks/useUserEventConnection.ts\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/usePreferredEventConnection */ \"./components/Tasks/hooks/usePreferredEventConnection.ts\");\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__, _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_7__]);\n([_Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__, _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction ProviderButton({ projectEventId, providerType, providerData, buttonText = \"Verify using\", ...props }) {\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const { connection, loading } = (0,_Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__.useUserEventConnection)(projectEventId, providerType);\n    const { unsetPreferredEventConnection } = (0,_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__.usePreferredEventConnection)();\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n        ...props,\n        loading: true,\n        children: \"Initializing...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n        lineNumber: 39,\n        columnNumber: 7\n    }, this);\n    const buttonTpl = ()=>{\n        if (!connection) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: \" Verify \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                lineNumber: 46,\n                columnNumber: 14\n            }, this);\n        }\n        const buttonTextTpl = ()=>{\n            switch(providerType){\n                case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AuthProvider.EVM_BLOCKCHAIN:\n                    return (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.shortenAddress)(connection.providerId, 8);\n                case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AuthProvider.DOTSAMA_BLOCKCHAIN:\n                    return (0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__.formatDotsamaAuth)(connection, providerData.blockchainId);\n                default:\n                    return connection.username || connection.firstName || connection.providerId;\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex space-x-2 items-center\",\n            children: [\n                connection.picture && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: connection.picture,\n                    className: \"h-8 w-8 flex-shrink-0 border-2 border-white rounded-full\",\n                    alt: \"profile image\",\n                    onError: ()=>setError(true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        buttonText,\n                        \" \",\n                        buttonTextTpl()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                ...props,\n                children: buttonTpl()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            connection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-primary text-sm cursor-pointer text-center\",\n                onClick: ()=>{\n                    unsetPreferredEventConnection(projectEventId, providerType);\n                },\n                children: \"Use another account\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Provider/ProviderButton.tsx\n");

/***/ }),

/***/ "./components/Tasks/Twitter/Ugc/TwitterUgcBody.tsx":
/*!*********************************************************!*\
  !*** ./components/Tasks/Twitter/Ugc/TwitterUgcBody.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Apps/components/TaskCompletedCard */ \"./components/Tasks/components/TaskCompletedCard.tsx\");\n/* harmony import */ var _Components_SocialIcons_Twitter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/SocialIcons/Twitter */ \"./components/SocialIcons/Twitter.tsx\");\n/* harmony import */ var _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/TextEditor */ \"./components/TextEditor.tsx\");\n/* harmony import */ var _Components_TimeLine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/TimeLine */ \"./components/TimeLine/index.tsx\");\n/* harmony import */ var _Components_TimeLine_TimeLineItem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/TimeLine/TimeLineItem */ \"./components/TimeLine/TimeLineItem.tsx\");\n/* harmony import */ var _Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Components/Tasks/components/TaskToaster */ \"./components/Tasks/components/TaskToaster.tsx\");\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @heroicons/react/solid */ \"@heroicons/react/solid\");\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _Root_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @Root/constants */ \"./constants/index.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _Components_Tasks_components_InstructionCard__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @Components/Tasks/components/InstructionCard */ \"./components/Tasks/components/InstructionCard.tsx\");\n/* harmony import */ var _gql_twitter_ugc_gql__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../gql/twitter-ugc.gql */ \"./components/Tasks/Twitter/gql/twitter-ugc.gql.ts\");\n/* harmony import */ var _twitter_helper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../twitter-helper */ \"./components/Tasks/Twitter/twitter-helper.ts\");\n/* harmony import */ var _Components_Tasks_Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @Components/Tasks/Provider/ProviderButton */ \"./components/Tasks/Provider/ProviderButton.tsx\");\n/* harmony import */ var _Components_InputField__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @Components/InputField */ \"./components/InputField.tsx\");\n/* harmony import */ var _Components_Tasks_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @Components/Tasks/hooks/usePreferredEventConnection */ \"./components/Tasks/hooks/usePreferredEventConnection.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_17__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__, _Components_TimeLine_TimeLineItem__WEBPACK_IMPORTED_MODULE_5__, _Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_6__, _gql_twitter_ugc_gql__WEBPACK_IMPORTED_MODULE_12__, _Components_Tasks_Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_14__, _Components_InputField__WEBPACK_IMPORTED_MODULE_15__, _Components_Tasks_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_16__]);\n([_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__, _Components_TimeLine_TimeLineItem__WEBPACK_IMPORTED_MODULE_5__, _Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_6__, _gql_twitter_ugc_gql__WEBPACK_IMPORTED_MODULE_12__, _Components_Tasks_Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_14__, _Components_InputField__WEBPACK_IMPORTED_MODULE_15__, _Components_Tasks_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_16__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TwitterUgcBody = ({ projectEventId, task, verified, taskParticipation, scoredPoints, onSuccess, onError })=>{\n    const [tweetUrl, setTweetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [participateUgc, { loading: verifying }] = (0,_gql_twitter_ugc_gql__WEBPACK_IMPORTED_MODULE_12__.useParticipateTwitterUgc)();\n    const { getPreferredEventConnection } = (0,_Components_Tasks_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_16__.usePreferredEventConnection)();\n    const providerId = getPreferredEventConnection(projectEventId, _airlyft_types__WEBPACK_IMPORTED_MODULE_9__.AuthProvider.TWITTER) || \"\";\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(verified ? _Root_constants__WEBPACK_IMPORTED_MODULE_8__.MAX_TIMELINE_STEPS : 0);\n    const { points, id, description = \"\" } = task;\n    const { hashtags, mentionsCount, userMentions, optionalUrl, tweetSubmitType, minimumMediaEntries } = task.info;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_17__.useTranslation)(\"translation\", {\n        keyPrefix: \"tasks.twitter.ugc\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_17__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const handleTweetClick = ()=>{\n        const intentParams = {\n            text: \"\",\n            url: \"\",\n            hashtags: \"\",\n            in_reply_to: \"\"\n        };\n        let tweetIntent = `https://twitter.com/intent/tweet?`;\n        const mentions = userMentions?.map((mention)=>`@${mention}`).join(\" \").trimEnd();\n        intentParams.text = mentions?.length ? encodeURIComponent(`${mentions}\\n`) : \"\";\n        intentParams.hashtags = hashtags?.length ? hashtags.join(\",\") : \"\";\n        if (optionalUrl) {\n            if (tweetSubmitType == _airlyft_types__WEBPACK_IMPORTED_MODULE_9__.TweetSubmitType.QUOTE) {\n                intentParams.url = optionalUrl;\n            } else if (tweetSubmitType === _airlyft_types__WEBPACK_IMPORTED_MODULE_9__.TweetSubmitType.REPLY) {\n                const tweetId = (0,_twitter_helper__WEBPACK_IMPORTED_MODULE_13__.tweetIdFromUrl)(optionalUrl);\n                intentParams.in_reply_to = tweetId;\n            }\n        }\n        for (const [key, value] of Object.entries(intentParams)){\n            if (value) {\n                tweetIntent += `${key}=${value}&`;\n            }\n        }\n        tweetIntent += `${tweetIntent[tweetIntent.length - 1] !== \"&\" ? \"&\" : \"\"}related=airlyftone,kyteone`;\n        window.open(tweetIntent, \"_blank\");\n    };\n    const handleTweetVerifyClick = ()=>{\n        participateUgc({\n            variables: {\n                eventId: projectEventId,\n                taskId: id,\n                providerId,\n                data: {\n                    url: tweetUrl\n                }\n            },\n            context: {\n                points: task.points\n            },\n            onCompleted: ()=>{\n                onSuccess?.();\n            },\n            onError: (error)=>{\n                (0,_Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({\n                    title: \"Verification Failed\",\n                    defaultText: `We were not able to verify that you have tweeted, so please make sure you have tweeted from your linked account & verify again. (${error?.message})`,\n                    type: \"error\",\n                    error\n                });\n                onError?.();\n            }\n        });\n    };\n    const instructions = [];\n    if (mentionsCount) {\n        instructions.push(`Tag at least ${mentionsCount} friends`);\n    }\n    if (minimumMediaEntries) {\n        instructions.push(`Include ${minimumMediaEntries} media files`);\n    }\n    if (hashtags?.length) {\n        instructions.push(`Must include hashtags :-  ${hashtags.map((item)=>`#${item}`).join(\", \")} `);\n    }\n    if (userMentions?.length) {\n        instructions.push(`Must mention :- ${userMentions.map((item)=>`@${item}`).join(\", \")}`);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                defaultValue: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                lineNumber: 157,\n                columnNumber: 23\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TimeLine__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                data: [\n                    {\n                        title: t(\"title\"),\n                        extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_InstructionCard__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    instructions: instructions\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        handleTweetClick();\n                                        setCurrentStep(1);\n                                    },\n                                    className: \"modal-clickable-list-item-rounded mt-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_SocialIcons_Twitter__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"flex-shrink-0 h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex-1 text-left font-semibold\",\n                                            children: t(\"actionTitle\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_7__.ExternalLinkIcon, {\n                                            className: \"h-5 w-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true)\n                    },\n                    {\n                        title: t(\"description\", {\n                            platform: globalT(\"platform\")\n                        }),\n                        extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_InputField__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    label: \"Tweet URL\",\n                                    onChange: (e)=>setTweetUrl(e.target.value),\n                                    autoComplete: \"off\",\n                                    placeholder: \"Please add the tweet URL below\",\n                                    className: \"mb-5\",\n                                    disabled: verified,\n                                    value: taskParticipation?.info?.url\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, void 0),\n                                verified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            points: scoredPoints,\n                                            frequency: task.frequency,\n                                            status: taskParticipation?.status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        \" \"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    providerType: _airlyft_types__WEBPACK_IMPORTED_MODULE_9__.AuthProvider.TWITTER,\n                                    projectEventId: projectEventId,\n                                    rounded: \"full\",\n                                    block: true,\n                                    disabled: !tweetUrl,\n                                    onClick: (e)=>{\n                                        handleTweetVerifyClick();\n                                    },\n                                    loading: verifying,\n                                    children: \"Verify\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 15\n                        }, void 0)\n                    }\n                ],\n                render: (item, key, status)=>{\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TimeLine_TimeLineItem__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: item.title,\n                        status: status,\n                        icon: key + 1,\n                        children: item.extra\n                    }, key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                currentStep: currentStep\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\Ugc\\\\TwitterUgcBody.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TwitterUgcBody);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Twitter/Ugc/TwitterUgcBody.tsx\n");

/***/ }),

/***/ "./components/Tasks/Twitter/twitter-helper.ts":
/*!****************************************************!*\
  !*** ./components/Tasks/Twitter/twitter-helper.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TWEET_URL_REGEX: () => (/* binding */ TWEET_URL_REGEX),\n/* harmony export */   TWITTER_ACCOUNT_URL_REGEX: () => (/* binding */ TWITTER_ACCOUNT_URL_REGEX),\n/* harmony export */   TWITTER_HANDLE_REGEX: () => (/* binding */ TWITTER_HANDLE_REGEX),\n/* harmony export */   handleLikeClick: () => (/* binding */ handleLikeClick),\n/* harmony export */   handleRetweetClick: () => (/* binding */ handleRetweetClick),\n/* harmony export */   tweetIdFromUrl: () => (/* binding */ tweetIdFromUrl),\n/* harmony export */   twitterHandleFromAccountUrl: () => (/* binding */ twitterHandleFromAccountUrl),\n/* harmony export */   twitterHandleFromUrl: () => (/* binding */ twitterHandleFromUrl)\n/* harmony export */ });\nconst TWEET_URL_REGEX = /^https?:\\/\\/(twitter|x)\\.com\\/(?:#!\\/)?(\\w+)\\/status(es)?\\/(\\d+)/;\n//ONLY matches account URLs\nconst TWITTER_ACCOUNT_URL_REGEX = /(twitter|x)\\.com\\/(\\w+)\\/?/;\nconst TWITTER_HANDLE_REGEX = /@([a-zA-Z0-9_]){1,15}/g;\nconst matchTweetUrl = (url)=>url.match(TWEET_URL_REGEX);\nconst matchAccountUrl = (url)=>url.match(TWITTER_ACCOUNT_URL_REGEX);\nfunction twitterHandleFromAccountUrl(url) {\n    const matchedArray = matchAccountUrl(url);\n    return matchedArray?.[2] || \"\";\n}\nfunction twitterHandleFromUrl(url) {\n    const matchedArray = matchTweetUrl(url);\n    return matchedArray?.[2] || \"\";\n}\nfunction tweetIdFromUrl(url) {\n    const matchedArray = matchTweetUrl(url);\n    return matchedArray?.[4] || \"\";\n}\nconst handleLikeClick = (tweetId)=>{\n    window.open(`https://twitter.com/intent/like?tweet_id=${tweetId}`, \"_blank\");\n};\nconst handleRetweetClick = (tweetId)=>{\n    window.open(`https://twitter.com/intent/retweet?tweet_id=${tweetId}`, \"_blank\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Twitter/twitter-helper.ts\n");

/***/ }),

/***/ "./components/Tasks/components/CountDownTimer.tsx":
/*!********************************************************!*\
  !*** ./components/Tasks/components/CountDownTimer.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountdownTimer: () => (/* binding */ CountdownTimer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"moment\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction CountdownTimer({ target }) {\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let intervalId = setInterval(()=>{\n            const now = moment__WEBPACK_IMPORTED_MODULE_2___default()();\n            const duration = moment__WEBPACK_IMPORTED_MODULE_2___default().duration(target.diff(now));\n            const days = Math.floor(duration.asDays());\n            const hours = Math.floor(duration.hours());\n            const minutes = Math.floor(duration.minutes());\n            const seconds = Math.floor(duration.seconds());\n            setTimeLeft(`${days ? days.toString() + \" days \" : \"\"}${hours.toString().padStart(2, \"0\")}:${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`);\n        }, 1000);\n        return ()=>clearInterval(intervalId);\n    }, [\n        target\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: timeLeft\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CountDownTimer.tsx\",\n        lineNumber: 29,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1Rhc2tzL2NvbXBvbmVudHMvQ291bnREb3duVGltZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0csZUFBZSxFQUFFQyxNQUFNLEVBQXNCO0lBQzNELE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHTCwrQ0FBUUEsQ0FBQztJQUV6Q0QsZ0RBQVNBLENBQUM7UUFDUixJQUFJTyxhQUFhQyxZQUFZO1lBQzNCLE1BQU1DLE1BQU1QLDZDQUFNQTtZQUNsQixNQUFNUSxXQUFXUixzREFBZSxDQUFDRSxPQUFPTyxJQUFJLENBQUNGO1lBRTdDLE1BQU1HLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ0osU0FBU0ssTUFBTTtZQUN2QyxNQUFNQyxRQUFRSCxLQUFLQyxLQUFLLENBQUNKLFNBQVNNLEtBQUs7WUFDdkMsTUFBTUMsVUFBVUosS0FBS0MsS0FBSyxDQUFDSixTQUFTTyxPQUFPO1lBQzNDLE1BQU1DLFVBQVVMLEtBQUtDLEtBQUssQ0FBQ0osU0FBU1EsT0FBTztZQUUzQ1osWUFDRSxDQUFDLEVBQUVNLE9BQU9BLEtBQUtPLFFBQVEsS0FBSyxXQUFXLEdBQUcsRUFBRUgsTUFDekNHLFFBQVEsR0FDUkMsUUFBUSxDQUFDLEdBQUcsS0FBSyxDQUFDLEVBQUVILFFBQVFFLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUcsS0FBSyxDQUFDLEVBQUVGLFFBQzNEQyxRQUFRLEdBQ1JDLFFBQVEsQ0FBQyxHQUFHLEtBQUssQ0FBQztRQUV6QixHQUFHO1FBRUgsT0FBTyxJQUFNQyxjQUFjZDtJQUM3QixHQUFHO1FBQUNIO0tBQU87SUFFWCxxQkFBTyw4REFBQ2tCO2tCQUFNakI7Ozs7OztBQUNoQiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvVGFza3MvY29tcG9uZW50cy9Db3VudERvd25UaW1lci50c3g/MzA5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgbW9tZW50LCB7IE1vbWVudCB9IGZyb20gJ21vbWVudCc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gQ291bnRkb3duVGltZXIoeyB0YXJnZXQgfTogeyB0YXJnZXQ6IE1vbWVudCB9KSB7XHJcbiAgY29uc3QgW3RpbWVMZWZ0LCBzZXRUaW1lTGVmdF0gPSB1c2VTdGF0ZSgnJyk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBsZXQgaW50ZXJ2YWxJZCA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgY29uc3Qgbm93ID0gbW9tZW50KCk7XHJcbiAgICAgIGNvbnN0IGR1cmF0aW9uID0gbW9tZW50LmR1cmF0aW9uKHRhcmdldC5kaWZmKG5vdykpO1xyXG5cclxuICAgICAgY29uc3QgZGF5cyA9IE1hdGguZmxvb3IoZHVyYXRpb24uYXNEYXlzKCkpO1xyXG4gICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IoZHVyYXRpb24uaG91cnMoKSk7XHJcbiAgICAgIGNvbnN0IG1pbnV0ZXMgPSBNYXRoLmZsb29yKGR1cmF0aW9uLm1pbnV0ZXMoKSk7XHJcbiAgICAgIGNvbnN0IHNlY29uZHMgPSBNYXRoLmZsb29yKGR1cmF0aW9uLnNlY29uZHMoKSk7XHJcblxyXG4gICAgICBzZXRUaW1lTGVmdChcclxuICAgICAgICBgJHtkYXlzID8gZGF5cy50b1N0cmluZygpICsgJyBkYXlzICcgOiAnJ30ke2hvdXJzXHJcbiAgICAgICAgICAudG9TdHJpbmcoKVxyXG4gICAgICAgICAgLnBhZFN0YXJ0KDIsICcwJyl9OiR7bWludXRlcy50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyl9OiR7c2Vjb25kc1xyXG4gICAgICAgICAgLnRvU3RyaW5nKClcclxuICAgICAgICAgIC5wYWRTdGFydCgyLCAnMCcpfWAsXHJcbiAgICAgICk7XHJcbiAgICB9LCAxMDAwKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbElkKTtcclxuICB9LCBbdGFyZ2V0XSk7XHJcblxyXG4gIHJldHVybiA8c3Bhbj57dGltZUxlZnR9PC9zcGFuPjtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJtb21lbnQiLCJDb3VudGRvd25UaW1lciIsInRhcmdldCIsInRpbWVMZWZ0Iiwic2V0VGltZUxlZnQiLCJpbnRlcnZhbElkIiwic2V0SW50ZXJ2YWwiLCJub3ciLCJkdXJhdGlvbiIsImRpZmYiLCJkYXlzIiwiTWF0aCIsImZsb29yIiwiYXNEYXlzIiwiaG91cnMiLCJtaW51dGVzIiwic2Vjb25kcyIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJjbGVhckludGVydmFsIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/Tasks/components/CountDownTimer.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/InstructionCard.tsx":
/*!*********************************************************!*\
  !*** ./components/Tasks/components/InstructionCard.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InstructionCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @heroicons/react/solid */ \"@heroicons/react/solid\");\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction InstructionCard({ title, instructions }) {\n    if (instructions.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col bg-primary p-5 rounded-xl relative overflow-hidden\",\n        style: {\n            background: \"linear-gradient(90deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\InstructionCard.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\InstructionCard.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-primary-foreground flex space-x-1 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1__.InformationCircleIcon, {\n                                    className: \"h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\InstructionCard.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: title || \"Instructions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\InstructionCard.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\InstructionCard.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc text-primary-foreground text-opacity-80 ml-4 mt-1\",\n                            children: instructions.map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: item\n                                }, key, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\InstructionCard.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\InstructionCard.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\InstructionCard.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\InstructionCard.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\InstructionCard.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/InstructionCard.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/TaskCompletedCard.tsx":
/*!***********************************************************!*\
  !*** ./components/Tasks/components/TaskCompletedCard.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/helpers/frequency */ \"./helpers/frequency.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _CountDownTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CountDownTimer */ \"./components/Tasks/components/CountDownTimer.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nconst TaskCompletedCard = ({ points, title, subTitle, frequency, status })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"tasks.completedCard\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const getBackground = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return \"linear-gradient(90deg, #c2410c 0%, #c2640c 50%, #c2800c 100%)\";\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return \"linear-gradient(90deg, #f16363 0%, #f65c5c 50%, #ef4646 100%)\";\n            default:\n                return \"\";\n        }\n    };\n    const getIcon = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Warning, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    function getTitle(title, points, globalT) {\n        if (title) {\n            return title;\n        }\n        if (points) {\n            if ((0,_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__.isFeatureEnabled)(\"POINTS\")) {\n                return `${t(\"title.points\", {\n                    points: points,\n                    projectPoints: globalT(\"projectPoints\")\n                })}`;\n            }\n            return `${t(\"title.noPoints\")}`;\n        }\n        return `${t(\"title.noPoints\")}`;\n    }\n    const getSubTitle = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n                return `${t(\"subtitle.inReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return `${t(\"subtitle.inAIReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return `${t(\"subtitle.valid\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return `${t(\"subtitle.invalid\")}`;\n            default:\n                return `${t(\"subtitle.valid\")}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col p-4 py-6 bg-primary rounded-xl relative overflow-hidden gradient-primary text-primary-foreground shadow\",\n        style: {\n            background: getBackground()\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg  mb-0\",\n                                children: getTitle(title, points, globalT)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: frequency && frequency !== _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.Frequency.NONE ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        \"Resets in \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountDownTimer__WEBPACK_IMPORTED_MODULE_4__.CountdownTimer, {\n                                                target: _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__.frequencyConfig[frequency].cutOff()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : subTitle ? subTitle : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: getSubTitle()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskCompletedCard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/TaskCompletedCard.tsx\n");

/***/ }),

/***/ "./components/TextEditor.tsx":
/*!***********************************!*\
  !*** ./components/TextEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! draft-js */ \"draft-js\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(draft_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! draft-js/dist/Draft.css */ \"./node_modules/draft-js/dist/Draft.css\");\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction TextEditor({ readonly = true, defaultValue, expandable = false, maxHeight = 100, className }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n    const [isOverflow, setIsOverflow] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        const container = ref.current;\n        if (!container || !setIsOverflow) return;\n        if (expandable && container.offsetHeight >= maxHeight) {\n            setIsOverflow(true);\n        }\n    }, [\n        ref,\n        setIsOverflow\n    ]);\n    function Link(props) {\n        const { url } = props.contentState.getEntity(props.entityKey).getData();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: url,\n            target: \"_blank\",\n            rel: \"noreferrer nofollow\",\n            referrerPolicy: \"no-referrer\",\n            style: {\n                color: currentTheme === \"dark\" ? \"#93cefe\" : \"#3b5998\",\n                textDecoration: \"underline\"\n            },\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    function findLinkEntities(contentBlock, callback, contentState) {\n        contentBlock.findEntityRanges((character)=>{\n            const entityKey = character.getEntity();\n            return entityKey !== null && contentState.getEntity(entityKey).getType() === \"LINK\";\n        }, callback);\n    }\n    const decorator = new draft_js__WEBPACK_IMPORTED_MODULE_3__.CompositeDecorator([\n        {\n            strategy: findLinkEntities,\n            component: Link\n        }\n    ]);\n    let editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createEmpty(decorator);\n    if (!defaultValue) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    if (defaultValue) {\n        try {\n            const parsedJson = JSON.parse(defaultValue);\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createWithContent((0,draft_js__WEBPACK_IMPORTED_MODULE_3__.convertFromRaw)(parsedJson), decorator);\n        } catch (err) {\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createWithContent(draft_js__WEBPACK_IMPORTED_MODULE_3__.ContentState.createFromText(defaultValue), decorator);\n        }\n    }\n    if (!editorState.getCurrentContent().hasText()) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            isOverflow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 z-10 h-[40px] w-full bg-gradient-to-t from-background/60 to-background/0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute z-20 flex items-center w-full justify-center -bottom-3`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-2 z-10 rounded-full border bg-background/90 text-cs text-sm font-extrabold\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.CaretUp, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.CaretDown, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`text-base text-ch leading-normal overflow-hidden`, className, isExpanded ? \"mb-10\" : \"\"),\n                ref: ref,\n                style: expandable && !isExpanded ? {\n                    maxHeight,\n                    marginBottom: 0\n                } : {},\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(draft_js__WEBPACK_IMPORTED_MODULE_3__.Editor, {\n                    editorState: editorState,\n                    readOnly: readonly,\n                    onChange: ()=>{}\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TextEditor.tsx\n");

/***/ }),

/***/ "./components/TimeLine/TimeLineItem.tsx":
/*!**********************************************!*\
  !*** ./components/TimeLine/TimeLineItem.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index */ \"./components/TimeLine/index.tsx\");\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_1__, _Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_1__, _Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst TimeLineItem = ({ icon, title, children, className, extraChildMargin = true, status = _index__WEBPACK_IMPORTED_MODULE_2__.TimeLineStatus.NotStarted, ring = false, pulse = false })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(`flex gap-2 text-base text-ch font-medium leading-tight`, extraChildMargin ? \"mb-4\" : \"mb-1\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(`flex flex-shrink-0 text-sm font-medium items-center justify-center w-6 h-6 rounded-full`, status === _index__WEBPACK_IMPORTED_MODULE_2__.TimeLineStatus.Active ? `bg-primary text-primary-foreground ${ring ? \"ring-8 ring-gray-200\" : \"\"} ${pulse && \"pulse\"}` : \"\", status === _index__WEBPACK_IMPORTED_MODULE_2__.TimeLineStatus.NotStarted ? \"bg-foreground/10 text-cl\" : \"\"),\n                        children: status === _index__WEBPACK_IMPORTED_MODULE_2__.TimeLineStatus.Completed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_1__.Check, {\n                            size: 20,\n                            className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                            weight: \"bold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TimeLine\\\\TimeLineItem.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, undefined) : icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TimeLine\\\\TimeLineItem.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    title\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TimeLine\\\\TimeLineItem.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TimeLine\\\\TimeLineItem.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TimeLineItem);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TimeLine/TimeLineItem.tsx\n");

/***/ }),

/***/ "./components/TimeLine/index.tsx":
/*!***************************************!*\
  !*** ./components/TimeLine/index.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimeLineStatus: () => (/* binding */ TimeLineStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nvar TimeLineStatus;\n(function(TimeLineStatus) {\n    TimeLineStatus[TimeLineStatus[\"Active\"] = 0] = \"Active\";\n    TimeLineStatus[TimeLineStatus[\"NotStarted\"] = 1] = \"NotStarted\";\n    TimeLineStatus[TimeLineStatus[\"Completed\"] = 2] = \"Completed\";\n})(TimeLineStatus || (TimeLineStatus = {}));\nconst TimeLine = ({ data, currentStep, render })=>{\n    const getItemStatus = (step)=>{\n        if (currentStep === step) return 0;\n        if (currentStep < step) return 1;\n        return 2;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n        className: \"relative space-y-10\",\n        children: data.map((item, step)=>render(item, step, getItemStatus(step)))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TimeLine\\\\index.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TimeLine);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1RpbWVMaW5lL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7VUFBWUE7Ozs7R0FBQUEsbUJBQUFBO0FBTVosTUFBTUMsV0FBVyxDQUFLLEVBQ3BCQyxJQUFJLEVBQ0pDLFdBQVcsRUFDWEMsTUFBTSxFQUtQO0lBQ0MsTUFBTUMsZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlILGdCQUFnQkcsTUFBTTtRQUMxQixJQUFJSCxjQUFjRyxNQUFNO1FBQ3hCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFBR0MsV0FBVTtrQkFDWE4sS0FBS08sR0FBRyxDQUFDLENBQUNDLE1BQU1KLE9BQVNGLE9BQU9NLE1BQU1KLE1BQU1ELGNBQWNDOzs7Ozs7QUFHakU7QUFFQSxpRUFBZUwsUUFBUUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvVGltZUxpbmUvaW5kZXgudHN4PzdmMzgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGVudW0gVGltZUxpbmVTdGF0dXMge1xyXG4gIEFjdGl2ZSxcclxuICBOb3RTdGFydGVkLFxyXG4gIENvbXBsZXRlZCxcclxufVxyXG5cclxuY29uc3QgVGltZUxpbmUgPSA8VCw+KHtcclxuICBkYXRhLFxyXG4gIGN1cnJlbnRTdGVwLFxyXG4gIHJlbmRlcixcclxufToge1xyXG4gIGRhdGE6IEFycmF5PFQ+O1xyXG4gIGN1cnJlbnRTdGVwOiBudW1iZXI7XHJcbiAgcmVuZGVyOiAoaXRlbTogVCwgc3RlcDogbnVtYmVyLCBzdGF0dXM6IFRpbWVMaW5lU3RhdHVzKSA9PiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pID0+IHtcclxuICBjb25zdCBnZXRJdGVtU3RhdHVzID0gKHN0ZXA6IG51bWJlcikgPT4ge1xyXG4gICAgaWYgKGN1cnJlbnRTdGVwID09PSBzdGVwKSByZXR1cm4gVGltZUxpbmVTdGF0dXMuQWN0aXZlO1xyXG4gICAgaWYgKGN1cnJlbnRTdGVwIDwgc3RlcCkgcmV0dXJuIFRpbWVMaW5lU3RhdHVzLk5vdFN0YXJ0ZWQ7XHJcbiAgICByZXR1cm4gVGltZUxpbmVTdGF0dXMuQ29tcGxldGVkO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8b2wgY2xhc3NOYW1lPVwicmVsYXRpdmUgc3BhY2UteS0xMFwiPlxyXG4gICAgICB7ZGF0YS5tYXAoKGl0ZW0sIHN0ZXApID0+IHJlbmRlcihpdGVtLCBzdGVwLCBnZXRJdGVtU3RhdHVzKHN0ZXApKSl9XHJcbiAgICA8L29sPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBUaW1lTGluZTtcclxuIl0sIm5hbWVzIjpbIlRpbWVMaW5lU3RhdHVzIiwiVGltZUxpbmUiLCJkYXRhIiwiY3VycmVudFN0ZXAiLCJyZW5kZXIiLCJnZXRJdGVtU3RhdHVzIiwic3RlcCIsIm9sIiwiY2xhc3NOYW1lIiwibWFwIiwiaXRlbSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/TimeLine/index.tsx\n");

/***/ })

};
;
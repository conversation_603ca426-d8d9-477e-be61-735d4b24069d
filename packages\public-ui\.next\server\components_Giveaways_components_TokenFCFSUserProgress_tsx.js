"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Giveaways_components_TokenFCFSUserProgress_tsx";
exports.ids = ["components_Giveaways_components_TokenFCFSUserProgress_tsx"];
exports.modules = {

/***/ "./components/Giveaways/components/GiveawayFCFSRangeTag.tsx":
/*!******************************************************************!*\
  !*** ./components/Giveaways/components/GiveawayFCFSRangeTag.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawayFCFSRangeTag),\n/* harmony export */   formatUserProgressTitle: () => (/* binding */ formatUserProgressTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Tag__WEBPACK_IMPORTED_MODULE_2__]);\n_Components_Tag__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction formatUserProgressTitle({ rule, completed, total, pointsSuffix, pointsCompletedText }) {\n    const isCompleted = total === completed;\n    let suffix;\n    let completedText;\n    switch(rule.ruleType){\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.POINTS:\n            suffix = pointsSuffix;\n            completedText = pointsCompletedText;\n            break;\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.TASK_COUNT:\n        default:\n            suffix = \"Quest Completed\";\n            completedText = \"Task Completed\";\n    }\n    return isCompleted ? completedText : `${completed} / ${total} ${suffix}`;\n}\nfunction GiveawayFCFSRangeTag({ rule }) {\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    switch(rule.ruleType){\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.TASK_COUNT:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        \" Complete any \",\n                        rule.min,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 18\n                }, void 0),\n                className: \"!inline-flex\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this);\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.GiveawayRuleType.POINTS:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        \" \",\n                        globalT(\"projectPoints\"),\n                        \" greater than \",\n                        rule.min,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 13\n                }, void 0),\n                className: \"!inline-flex !bg-primary/10 !border-primary/60 !font-semibold\",\n                rounded: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this);\n        default:\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawayFCFSRangeTag.tsx\",\n        lineNumber: 64,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawayFCFSRangeTag.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenFCFSUserProgress.tsx":
/*!*******************************************************************!*\
  !*** ./components/Giveaways/components/TokenFCFSUserProgress.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenGiveawayUserProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_List_ListItem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/List/ListItem */ \"./components/List/ListItem.tsx\");\n/* harmony import */ var _Components_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Loaders/SpinLoader */ \"./components/Loaders/SpinLoader.tsx\");\n/* harmony import */ var _Components_RadialProgress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/RadialProgress */ \"./components/RadialProgress/index.tsx\");\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _GiveawayFCFSRangeTag__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./GiveawayFCFSRangeTag */ \"./components/Giveaways/components/GiveawayFCFSRangeTag.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_10__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Tag__WEBPACK_IMPORTED_MODULE_4__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__, _GiveawayFCFSRangeTag__WEBPACK_IMPORTED_MODULE_9__]);\n([_Components_Tag__WEBPACK_IMPORTED_MODULE_4__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__, _GiveawayFCFSRangeTag__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\nfunction TokenGiveawayUserProgress({ showDetails, token, description, data, rule }) {\n    const { claimed, percentage, amount, isCompleted, completed, total, status } = data;\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_10__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    //User Progress only makes sense if total is non-zero, else this giveaway probably just has a whitelist\n    if (total == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_List_ListItem__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        icon: isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Check, {\n            size: 22,\n            className: \"text-primary-foreground bg-primary rounded-full p-1\",\n            weight: \"bold\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 42,\n            columnNumber: 11\n        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RadialProgress__WEBPACK_IMPORTED_MODULE_3__.RadialProgress, {\n            progress: percentage,\n            text: ``,\n            circleSize: 26,\n            barSize: 3\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 48,\n            columnNumber: 11\n        }, void 0),\n        title: `${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__.formatAmount)(amount?.toString(), token.decimals)} ${token.ticker}`,\n        subtitle: (0,_GiveawayFCFSRangeTag__WEBPACK_IMPORTED_MODULE_9__.formatUserProgressTitle)({\n            rule,\n            total,\n            completed,\n            pointsSuffix: `${globalT(\"projectPoints\")} Scored`,\n            pointsCompletedText: `All ${globalT(\"projectPoints\")} Scored`\n        }),\n        extra: showDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_8__.Fragment, {\n            children: [\n                claimed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-primary text-xs text-primary-foreground rounded-full py-1 px-2 flex items-center space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Check, {\n                            className: \"h-3\",\n                            weight: \"bold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 17\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Claimed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 17\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 15\n                }, void 0),\n                status && status === _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.RewardStatus.PROCESSING ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 15\n                }, void 0) : isCompleted && !claimed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_4__.Tag, {\n                    title: \"Claimable\",\n                    size: \"small\",\n                    className: \"!font-bold\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 17\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 68,\n            columnNumber: 11\n        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_8__.Fragment, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n            lineNumber: 86,\n            columnNumber: 11\n        }, void 0),\n        description: description\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenFCFSUserProgress.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenFCFSUserProgress.tsx\n");

/***/ }),

/***/ "./components/Loaders/SingleDot.tsx":
/*!******************************************!*\
  !*** ./components/Loaders/SingleDot.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-content-loader */ \"react-content-loader\");\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_content_loader__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst SingleDotLoader = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_content_loader__WEBPACK_IMPORTED_MODULE_2___default()), {\n        speed: 2,\n        width: 32,\n        height: 32,\n        viewBox: \"0 0 32 32\",\n        uniqueKey: \"single-dot-loader\",\n        backgroundColor: \"#cdcdcd\",\n        foregroundColor: \"#ddd\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n            cx: \"16\",\n            cy: \"16\",\n            r: \"16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n            lineNumber: 15,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SingleDotLoader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvU2luZ2xlRG90LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUN1QjtBQUVqRCxNQUFNRSxrQkFBa0IsQ0FBQ0Msc0JBQ3ZCLDhEQUFDRiw2REFBYUE7UUFDWkcsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsU0FBUTtRQUNSQyxXQUFVO1FBQ1ZDLGlCQUFnQjtRQUNoQkMsaUJBQWdCO1FBQ2YsR0FBR1AsS0FBSztrQkFFVCw0RUFBQ1E7WUFBT0MsSUFBRztZQUFLQyxJQUFHO1lBQUtDLEdBQUU7Ozs7Ozs7Ozs7O0FBSTlCLGlFQUFlWixlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdC50c3g/YTE5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgQ29udGVudExvYWRlciBmcm9tICdyZWFjdC1jb250ZW50LWxvYWRlcic7XHJcblxyXG5jb25zdCBTaW5nbGVEb3RMb2FkZXIgPSAocHJvcHM6IGFueSkgPT4gKFxyXG4gIDxDb250ZW50TG9hZGVyXHJcbiAgICBzcGVlZD17Mn1cclxuICAgIHdpZHRoPXszMn1cclxuICAgIGhlaWdodD17MzJ9XHJcbiAgICB2aWV3Qm94PVwiMCAwIDMyIDMyXCJcclxuICAgIHVuaXF1ZUtleT1cInNpbmdsZS1kb3QtbG9hZGVyXCJcclxuICAgIGJhY2tncm91bmRDb2xvcj1cIiNjZGNkY2RcIlxyXG4gICAgZm9yZWdyb3VuZENvbG9yPVwiI2RkZFwiXHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgPlxyXG4gICAgPGNpcmNsZSBjeD1cIjE2XCIgY3k9XCIxNlwiIHI9XCIxNlwiIC8+XHJcbiAgPC9Db250ZW50TG9hZGVyPlxyXG4pO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU2luZ2xlRG90TG9hZGVyO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb250ZW50TG9hZGVyIiwiU2luZ2xlRG90TG9hZGVyIiwicHJvcHMiLCJzcGVlZCIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsInVuaXF1ZUtleSIsImJhY2tncm91bmRDb2xvciIsImZvcmVncm91bmRDb2xvciIsImNpcmNsZSIsImN4IiwiY3kiLCJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Loaders/SingleDot.tsx\n");

/***/ }),

/***/ "./components/RadialProgress/index.tsx":
/*!*********************************************!*\
  !*** ./components/RadialProgress/index.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RadialProgress: () => (/* binding */ RadialProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst RadialProgress = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/recharts\"), __webpack_require__.e(\"components_RadialProgress_RadialProgress_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! @Components/RadialProgress/RadialProgress */ \"./components/RadialProgress/RadialProgress.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\RadialProgress\\\\index.tsx -> \" + \"@Components/RadialProgress/RadialProgress\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            height: 66,\n            width: 66\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RadialProgress\\\\index.tsx\",\n            lineNumber: 8,\n            columnNumber: 20\n        }, undefined)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTREO0FBQ3pCO0FBRTVCLE1BQU1FLGlCQUFpQkQsbURBQU9BLENBQ25DLElBQU0sMlNBQU87Ozs7OztJQUVYRSxLQUFLO0lBQ0xDLFNBQVMsa0JBQU0sOERBQUNKLHFFQUFlQTtZQUFDSyxRQUFRO1lBQUlDLE9BQU87Ozs7OztHQUVyRCIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvUmFkaWFsUHJvZ3Jlc3MvaW5kZXgudHN4P2ZmMDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFNpbmdsZURvdExvYWRlciBmcm9tICdAQ29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdCc7XHJcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYyc7XHJcblxyXG5leHBvcnQgY29uc3QgUmFkaWFsUHJvZ3Jlc3MgPSBkeW5hbWljKFxyXG4gICgpID0+IGltcG9ydCgnQENvbXBvbmVudHMvUmFkaWFsUHJvZ3Jlc3MvUmFkaWFsUHJvZ3Jlc3MnKSxcclxuICB7XHJcbiAgICBzc3I6IGZhbHNlLFxyXG4gICAgbG9hZGluZzogKCkgPT4gPFNpbmdsZURvdExvYWRlciBoZWlnaHQ9ezY2fSB3aWR0aD17NjZ9IC8+LFxyXG4gIH0sXHJcbik7XHJcbiJdLCJuYW1lcyI6WyJTaW5nbGVEb3RMb2FkZXIiLCJkeW5hbWljIiwiUmFkaWFsUHJvZ3Jlc3MiLCJzc3IiLCJsb2FkaW5nIiwiaGVpZ2h0Iiwid2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/RadialProgress/index.tsx\n");

/***/ })

};
;
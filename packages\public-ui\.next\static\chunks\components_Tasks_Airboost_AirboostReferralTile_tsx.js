"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Tasks_Airboost_AirboostReferralTile_tsx"],{

/***/ "./components/Loaders/SingleDot.tsx":
/*!******************************************!*\
  !*** ./components/Loaders/SingleDot.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-content-loader */ \"./node_modules/react-content-loader/dist/react-content-loader.es.js\");\n\n\n\nconst SingleDotLoader = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_content_loader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        speed: 2,\n        width: 32,\n        height: 32,\n        viewBox: \"0 0 32 32\",\n        uniqueKey: \"single-dot-loader\",\n        backgroundColor: \"#cdcdcd\",\n        foregroundColor: \"#ddd\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n            cx: \"16\",\n            cy: \"16\",\n            r: \"16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n            lineNumber: 15,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SingleDot.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\n_c = SingleDotLoader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SingleDotLoader);\nvar _c;\n$RefreshReg$(_c, \"SingleDotLoader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvU2luZ2xlRG90LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTBCO0FBQ3VCO0FBRWpELE1BQU1FLGtCQUFrQixDQUFDQyxzQkFDdkIsOERBQUNGLDREQUFhQTtRQUNaRyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxTQUFRO1FBQ1JDLFdBQVU7UUFDVkMsaUJBQWdCO1FBQ2hCQyxpQkFBZ0I7UUFDZixHQUFHUCxLQUFLO2tCQUVULDRFQUFDUTtZQUFPQyxJQUFHO1lBQUtDLElBQUc7WUFBS0MsR0FBRTs7Ozs7Ozs7Ozs7S0FYeEJaO0FBZU4sK0RBQWVBLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9Mb2FkZXJzL1NpbmdsZURvdC50c3g/YTE5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgQ29udGVudExvYWRlciBmcm9tICdyZWFjdC1jb250ZW50LWxvYWRlcic7XHJcblxyXG5jb25zdCBTaW5nbGVEb3RMb2FkZXIgPSAocHJvcHM6IGFueSkgPT4gKFxyXG4gIDxDb250ZW50TG9hZGVyXHJcbiAgICBzcGVlZD17Mn1cclxuICAgIHdpZHRoPXszMn1cclxuICAgIGhlaWdodD17MzJ9XHJcbiAgICB2aWV3Qm94PVwiMCAwIDMyIDMyXCJcclxuICAgIHVuaXF1ZUtleT1cInNpbmdsZS1kb3QtbG9hZGVyXCJcclxuICAgIGJhY2tncm91bmRDb2xvcj1cIiNjZGNkY2RcIlxyXG4gICAgZm9yZWdyb3VuZENvbG9yPVwiI2RkZFwiXHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgPlxyXG4gICAgPGNpcmNsZSBjeD1cIjE2XCIgY3k9XCIxNlwiIHI9XCIxNlwiIC8+XHJcbiAgPC9Db250ZW50TG9hZGVyPlxyXG4pO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU2luZ2xlRG90TG9hZGVyO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb250ZW50TG9hZGVyIiwiU2luZ2xlRG90TG9hZGVyIiwicHJvcHMiLCJzcGVlZCIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsInVuaXF1ZUtleSIsImJhY2tncm91bmRDb2xvciIsImZvcmVncm91bmRDb2xvciIsImNpcmNsZSIsImN4IiwiY3kiLCJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Loaders/SingleDot.tsx\n"));

/***/ }),

/***/ "./components/RadialProgress/index.tsx":
/*!*********************************************!*\
  !*** ./components/RadialProgress/index.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RadialProgress: function() { return /* binding */ RadialProgress; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst RadialProgress = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c = ()=>__webpack_require__.e(/*! import() */ \"components_RadialProgress_RadialProgress_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @Components/RadialProgress/RadialProgress */ \"./components/RadialProgress/RadialProgress.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\RadialProgress\\\\index.tsx -> \" + \"@Components/RadialProgress/RadialProgress\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            height: 66,\n            width: 66\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RadialProgress\\\\index.tsx\",\n            lineNumber: 8,\n            columnNumber: 20\n        }, undefined)\n});\n_c1 = RadialProgress;\nvar _c, _c1;\n$RefreshReg$(_c, \"RadialProgress$dynamic\");\n$RefreshReg$(_c1, \"RadialProgress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTREO0FBQ3pCO0FBRTVCLE1BQU1FLGlCQUFpQkQsbURBQU9BLE1BQ25DLElBQU0sNE9BQU87Ozs7OztJQUVYRSxLQUFLO0lBQ0xDLFNBQVMsa0JBQU0sOERBQUNKLHFFQUFlQTtZQUFDSyxRQUFRO1lBQUlDLE9BQU87Ozs7OztHQUVyRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL2luZGV4LnRzeD9mZjA5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTaW5nbGVEb3RMb2FkZXIgZnJvbSAnQENvbXBvbmVudHMvTG9hZGVycy9TaW5nbGVEb3QnO1xyXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnO1xyXG5cclxuZXhwb3J0IGNvbnN0IFJhZGlhbFByb2dyZXNzID0gZHluYW1pYyhcclxuICAoKSA9PiBpbXBvcnQoJ0BDb21wb25lbnRzL1JhZGlhbFByb2dyZXNzL1JhZGlhbFByb2dyZXNzJyksXHJcbiAge1xyXG4gICAgc3NyOiBmYWxzZSxcclxuICAgIGxvYWRpbmc6ICgpID0+IDxTaW5nbGVEb3RMb2FkZXIgaGVpZ2h0PXs2Nn0gd2lkdGg9ezY2fSAvPixcclxuICB9LFxyXG4pO1xyXG4iXSwibmFtZXMiOlsiU2luZ2xlRG90TG9hZGVyIiwiZHluYW1pYyIsIlJhZGlhbFByb2dyZXNzIiwic3NyIiwibG9hZGluZyIsImhlaWdodCIsIndpZHRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/RadialProgress/index.tsx\n"));

/***/ }),

/***/ "./components/Tasks/Airboost/AirboostReferralTile.tsx":
/*!************************************************************!*\
  !*** ./components/Tasks/Airboost/AirboostReferralTile.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SingleDot */ \"./components/Loaders/SingleDot.tsx\");\n/* harmony import */ var _Components_RadialProgress__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/RadialProgress */ \"./components/RadialProgress/index.tsx\");\n/* harmony import */ var _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Hooks/useTaskParticipation */ \"./hooks/useTaskParticipation.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var _components_TaskTile__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/TaskTile */ \"./components/Tasks/components/TaskTile.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst AirboostReferralTile = (param)=>{\n    let { projectEventId, eventTask, onClick, locked, lockedReason } = param;\n    _s();\n    const { title, points, xp, iconUrl } = eventTask;\n    const { max } = eventTask === null || eventTask === void 0 ? void 0 : eventTask.info;\n    const maxPoints = points * max;\n    const maxXp = xp * max;\n    const { data, loading } = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__.useUserCurrentTaskParticipationMap)(projectEventId);\n    const participation = data === null || data === void 0 ? void 0 : data.get(eventTask === null || eventTask === void 0 ? void 0 : eventTask.id);\n    const referredCount = ((participation === null || participation === void 0 ? void 0 : participation.points) || 0) / points;\n    const subtitleTpl = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: [\n                \"Refer up to \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                    children: max\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Airboost\\\\AirboostReferralTile.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 21\n                }, undefined),\n                \" friends\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Airboost\\\\AirboostReferralTile.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TaskTile__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        onClick: onClick,\n        title: title,\n        subtitle: subtitleTpl(),\n        locked: locked,\n        lockedReason: lockedReason,\n        isVerified: (participation === null || participation === void 0 ? void 0 : participation.points) === maxPoints,\n        points: maxPoints,\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.PaperPlaneTilt, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Airboost\\\\AirboostReferralTile.tsx\",\n            lineNumber: 41,\n            columnNumber: 13\n        }, void 0),\n        xp: maxXp,\n        renderExtra: ()=>loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SingleDot__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                width: 54,\n                height: 54\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Airboost\\\\AirboostReferralTile.tsx\",\n                lineNumber: 45,\n                columnNumber: 11\n            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RadialProgress__WEBPACK_IMPORTED_MODULE_2__.RadialProgress, {\n                progress: (referredCount || 0) / (max || 1) * 100,\n                text: \"\".concat(referredCount || 0, \"/\").concat(max),\n                circleSize: 54,\n                textClass: \"fill-foreground\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Airboost\\\\AirboostReferralTile.tsx\",\n                lineNumber: 47,\n                columnNumber: 11\n            }, void 0),\n        iconUrl: iconUrl\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Airboost\\\\AirboostReferralTile.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AirboostReferralTile, \"5365NlexBM2L8vkA33DWw2cBxU4=\", false, function() {\n    return [\n        _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_3__.useUserCurrentTaskParticipationMap\n    ];\n});\n_c = AirboostReferralTile;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AirboostReferralTile);\nvar _c;\n$RefreshReg$(_c, \"AirboostReferralTile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Airboost/AirboostReferralTile.tsx\n"));

/***/ })

}]);
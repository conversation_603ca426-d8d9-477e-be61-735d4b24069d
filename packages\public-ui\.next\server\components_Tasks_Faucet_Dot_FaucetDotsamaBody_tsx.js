"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Tasks_Faucet_Dot_FaucetDotsamaBody_tsx";
exports.ids = ["components_Tasks_Faucet_Dot_FaucetDotsamaBody_tsx"];
exports.modules = {

/***/ "./components/BlockExplorerLink.tsx":
/*!******************************************!*\
  !*** ./components/BlockExplorerLink.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlockExplorerLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction BlockExplorerLink({ hash, blockExplorerUrls }) {\n    return hash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        className: \"underline inline-flex text-sm\",\n        target: \"_blank\",\n        href: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getExplorerLink)(blockExplorerUrls, hash, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ExplorerDataType.TRANSACTION),\n        rel: \"noreferrer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-1 inline-block\",\n                children: \"View on Explorer \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-4 w-4 inline-block\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0Jsb2NrRXhwbG9yZXJMaW5rLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFpQztBQUNxQztBQUV2RCxTQUFTRyxrQkFBa0IsRUFDeENDLElBQUksRUFDSkMsaUJBQWlCLEVBSWxCO0lBQ0MsT0FBT0QscUJBQ0wsOERBQUNFO1FBQ0NDLFdBQVU7UUFDVkMsUUFBTztRQUNQQyxNQUFNUCxrRUFBZUEsQ0FDbkJHLG1CQUNBRCxNQUNBSCwrREFBZ0JBLENBQUNTLFdBQVc7UUFFOUJDLEtBQUk7OzBCQUVKLDhEQUFDQztnQkFBS0wsV0FBVTswQkFBb0I7Ozs7OztZQUF5QjswQkFDN0QsOERBQUNNO2dCQUNDQyxPQUFNO2dCQUNOUCxXQUFVO2dCQUNWUSxNQUFLO2dCQUNMQyxTQUFRO2dCQUNSQyxRQUFPOzBCQUVQLDRFQUFDQztvQkFDQ0MsZUFBYztvQkFDZEMsZ0JBQWU7b0JBQ2ZDLGFBQWE7b0JBQ2JDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7NkJBS1IsOERBQUN0QiwyQ0FBUUE7Ozs7O0FBRWIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL0Jsb2NrRXhwbG9yZXJMaW5rLnRzeD81Yzc1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZyYWdtZW50IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBFeHBsb3JlckRhdGFUeXBlLCBnZXRFeHBsb3JlckxpbmsgfSBmcm9tICdAYWlybHlmdC93ZWIzLWV2bSc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBCbG9ja0V4cGxvcmVyTGluayh7XHJcbiAgaGFzaCxcclxuICBibG9ja0V4cGxvcmVyVXJscyxcclxufToge1xyXG4gIGhhc2g6IHN0cmluZyB8IHVuZGVmaW5lZDtcclxuICBibG9ja0V4cGxvcmVyVXJsczogQXJyYXk8c3RyaW5nPjtcclxufSkge1xyXG4gIHJldHVybiBoYXNoID8gKFxyXG4gICAgPGFcclxuICAgICAgY2xhc3NOYW1lPVwidW5kZXJsaW5lIGlubGluZS1mbGV4IHRleHQtc21cIlxyXG4gICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxyXG4gICAgICBocmVmPXtnZXRFeHBsb3JlckxpbmsoXHJcbiAgICAgICAgYmxvY2tFeHBsb3JlclVybHMsXHJcbiAgICAgICAgaGFzaCxcclxuICAgICAgICBFeHBsb3JlckRhdGFUeXBlLlRSQU5TQUNUSU9OLFxyXG4gICAgICApfVxyXG4gICAgICByZWw9XCJub3JlZmVycmVyXCJcclxuICAgID5cclxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMSBpbmxpbmUtYmxvY2tcIj5WaWV3IG9uIEV4cGxvcmVyIDwvc3Bhbj57JyAnfVxyXG4gICAgICA8c3ZnXHJcbiAgICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXHJcbiAgICAgICAgY2xhc3NOYW1lPVwiaC00IHctNCBpbmxpbmUtYmxvY2tcIlxyXG4gICAgICAgIGZpbGw9XCJub25lXCJcclxuICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcclxuICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICA+XHJcbiAgICAgICAgPHBhdGhcclxuICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxyXG4gICAgICAgICAgZD1cIk0xMCA2SDZhMiAyIDAgMDAtMiAydjEwYTIgMiAwIDAwMiAyaDEwYTIgMiAwIDAwMi0ydi00TTE0IDRoNm0wIDB2Nm0wLTZMMTAgMTRcIlxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvc3ZnPlxyXG4gICAgPC9hPlxyXG4gICkgOiAoXHJcbiAgICA8RnJhZ21lbnQgLz5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJGcmFnbWVudCIsIkV4cGxvcmVyRGF0YVR5cGUiLCJnZXRFeHBsb3JlckxpbmsiLCJCbG9ja0V4cGxvcmVyTGluayIsImhhc2giLCJibG9ja0V4cGxvcmVyVXJscyIsImEiLCJjbGFzc05hbWUiLCJ0YXJnZXQiLCJocmVmIiwiVFJBTlNBQ1RJT04iLCJyZWwiLCJzcGFuIiwic3ZnIiwieG1sbnMiLCJmaWxsIiwidmlld0JveCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/BlockExplorerLink.tsx\n");

/***/ }),

/***/ "./components/RecaptchaDeclaration.tsx":
/*!*********************************************!*\
  !*** ./components/RecaptchaDeclaration.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecaptchaDeclaration: () => (/* binding */ RecaptchaDeclaration)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst RecaptchaDeclaration = ({ className = \"text-sm\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: className,\n        children: [\n            \"This site is protected by reCAPTCHA and the Google\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"underline text-link cursor-pointer\",\n                href: \"https://policies.google.com/privacy\",\n                children: \"Privacy Policy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"and\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"underline text-link cursor-pointer\",\n                href: \"https://policies.google.com/terms\",\n                children: \"Terms of Service\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"apply.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JlY2FwdGNoYURlY2xhcmF0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFbkIsTUFBTUMsdUJBQXVCLENBQUMsRUFDbkNDLFlBQVksU0FBUyxFQUd0QjtJQUNDLHFCQUNFLDhEQUFDQztRQUFFRCxXQUFXQTs7WUFBVztZQUM0QjswQkFDbkQsOERBQUNFO2dCQUNDRixXQUFVO2dCQUNWRyxNQUFLOzBCQUNOOzs7Ozs7WUFFSTtZQUFJO1lBQ0w7MEJBQ0osOERBQUNEO2dCQUNDRixXQUFVO2dCQUNWRyxNQUFLOzBCQUNOOzs7Ozs7WUFFSTtZQUFJOzs7Ozs7O0FBSWYsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvUmVjYXB0Y2hhRGVjbGFyYXRpb24udHN4PzBmMWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuXHJcbmV4cG9ydCBjb25zdCBSZWNhcHRjaGFEZWNsYXJhdGlvbiA9ICh7XHJcbiAgY2xhc3NOYW1lID0gJ3RleHQtc20nLFxyXG59OiB7XHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG59KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxwIGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cclxuICAgICAgVGhpcyBzaXRlIGlzIHByb3RlY3RlZCBieSByZUNBUFRDSEEgYW5kIHRoZSBHb29nbGV7JyAnfVxyXG4gICAgICA8YVxyXG4gICAgICAgIGNsYXNzTmFtZT1cInVuZGVybGluZSB0ZXh0LWxpbmsgY3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgIGhyZWY9XCJodHRwczovL3BvbGljaWVzLmdvb2dsZS5jb20vcHJpdmFjeVwiXHJcbiAgICAgID5cclxuICAgICAgICBQcml2YWN5IFBvbGljeVxyXG4gICAgICA8L2E+eycgJ31cclxuICAgICAgYW5keycgJ31cclxuICAgICAgPGFcclxuICAgICAgICBjbGFzc05hbWU9XCJ1bmRlcmxpbmUgdGV4dC1saW5rIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICBocmVmPVwiaHR0cHM6Ly9wb2xpY2llcy5nb29nbGUuY29tL3Rlcm1zXCJcclxuICAgICAgPlxyXG4gICAgICAgIFRlcm1zIG9mIFNlcnZpY2VcclxuICAgICAgPC9hPnsnICd9XHJcbiAgICAgIGFwcGx5LlxyXG4gICAgPC9wPlxyXG4gICk7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlJlY2FwdGNoYURlY2xhcmF0aW9uIiwiY2xhc3NOYW1lIiwicCIsImEiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/RecaptchaDeclaration.tsx\n");

/***/ }),

/***/ "./components/Tasks/Faucet/BaseFaucetBody.tsx":
/*!****************************************************!*\
  !*** ./components/Tasks/Faucet/BaseFaucetBody.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_BlockExplorerLink__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/BlockExplorerLink */ \"./components/BlockExplorerLink.tsx\");\n/* harmony import */ var _Components_Tasks_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Tasks/components/TaskCompletedCard */ \"./components/Tasks/components/TaskCompletedCard.tsx\");\n/* harmony import */ var _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/TextEditor */ \"./components/TextEditor.tsx\");\n/* harmony import */ var _Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Hooks/useGetBlockchain */ \"./hooks/useGetBlockchain.ts\");\n/* harmony import */ var _Hooks_useValueByKeyGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Hooks/useValueByKeyGroup */ \"./hooks/useValueByKeyGroup.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"react-google-recaptcha-v3\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../Provider/ProviderButton */ \"./components/Tasks/Provider/ProviderButton.tsx\");\n/* harmony import */ var _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../hooks/usePreferredEventConnection */ \"./components/Tasks/hooks/usePreferredEventConnection.ts\");\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Tasks_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_2__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_10__, _Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_11__, _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_12__]);\n([_Components_Tasks_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_2__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_10__, _Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_11__, _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_12__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction BaseFaucetBody({ projectEventId, authProvider, task, verified, scoredPoints, taskParticipation, onClaim, claiming }) {\n    const { id, description } = task;\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_9__.useGoogleReCaptcha)();\n    let { blockchainId, amountPerUser, allocationCSVExists } = task.info;\n    const { getPreferredEventConnection } = (0,_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_12__.usePreferredEventConnection)();\n    const providerId = getPreferredEventConnection(projectEventId, authProvider) || \"\";\n    const taskParticipationData = taskParticipation?.info;\n    const { data: claimableAmount } = (0,_Hooks_useValueByKeyGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(id, providerId, !allocationCSVExists);\n    const amount = allocationCSVExists ? claimableAmount?.valueByKeyGroup : amountPerUser;\n    const { data: blockchainData, loading: blockchainLoading } = (0,_Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_4__.useGetBlockchain)(blockchainId);\n    const blockchain = blockchainData?.blockchain;\n    const handleClaim = async ()=>{\n        if (!executeRecaptcha) {\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_10__[\"default\"])({\n                title: \"Failed\",\n                text: \"Recaptcha not initialized\",\n                type: \"error\"\n            });\n            return;\n        }\n        const captcha = await executeRecaptcha(\"faucet_claim\");\n        onClaim(providerId, captcha);\n    };\n    const taskCard = ()=>{\n        if (taskParticipation?.status === _airlyft_types__WEBPACK_IMPORTED_MODULE_6__.ParticipationStatus.IN_REVIEW) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                points: 0,\n                title: \"Your token transfer is in the queue\",\n                subTitle: \"Please wait for the transaction to be sent to the blockchain. Come back here later to check the status of your claim.\",\n                status: taskParticipation?.status\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            points: 0,\n            title: \"Your tokens have been transferred\",\n            subTitle: \"Delay is expected based on the blockchain status\",\n            status: taskParticipation?.status\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                defaultValue: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n                lineNumber: 100,\n                columnNumber: 23\n            }, this),\n            verified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    taskCard(),\n                    taskParticipationData && taskParticipationData.hash && blockchain && blockchain.blockExplorerUrls.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_BlockExplorerLink__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            hash: taskParticipationData.hash,\n                            blockExplorerUrls: blockchain?.blockExplorerUrls || []\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Claim \",\n                            blockchain?.nativeCurrency,\n                            \" from\",\n                            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                className: \"mx-2 inline-block\",\n                                src: blockchain?.icon,\n                                width: 20,\n                                height: 20,\n                                alt: \"blockchain-icon\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, this),\n                            blockchain?.name,\n                            \" Faucet\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__.RecaptchaDeclaration, {\n                        className: \"text-xs text-cs text-center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        providerType: authProvider,\n                        providerData: {\n                            blockchainId: blockchain.chainId || 0\n                        },\n                        projectEventId: projectEventId,\n                        disabled: !providerId,\n                        rounded: \"full\",\n                        block: true,\n                        type: \"submit\",\n                        loading: claiming,\n                        onClick: handleClaim,\n                        buttonText: `\r\n              ${amount ? `Claim ${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_7__.formatAmount)(amount.toString(), blockchain?.decimals || 0)} ${blockchain?.nativeCurrency} using` : \"Claim using\"}\r\n              `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\BaseFaucetBody.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BaseFaucetBody);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Faucet/BaseFaucetBody.tsx\n");

/***/ }),

/***/ "./components/Tasks/Faucet/Dot/FaucetDotsamaBody.tsx":
/*!***********************************************************!*\
  !*** ./components/Tasks/Faucet/Dot/FaucetDotsamaBody.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Tasks/components/TaskToaster */ \"./components/Tasks/components/TaskToaster.tsx\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"react-google-recaptcha-v3\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _BaseFaucetBody__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../BaseFaucetBody */ \"./components/Tasks/Faucet/BaseFaucetBody.tsx\");\n/* harmony import */ var _faucet_gql__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../faucet.gql */ \"./components/Tasks/Faucet/faucet.gql.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_2__, _BaseFaucetBody__WEBPACK_IMPORTED_MODULE_5__, _faucet_gql__WEBPACK_IMPORTED_MODULE_6__]);\n([_Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_2__, _BaseFaucetBody__WEBPACK_IMPORTED_MODULE_5__, _faucet_gql__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction FaucetDotsamaBody({ projectEventId, task, verified, scoredPoints, taskParticipation, onSuccess, onError }) {\n    const { id } = task;\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_4__.useGoogleReCaptcha)();\n    const [participateFaucet, { loading: verifying }] = (0,_faucet_gql__WEBPACK_IMPORTED_MODULE_6__.useParticipateFaucetDotsama)();\n    const handleVerify = async (address)=>{\n        if (!(0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_2__.isValidPolkadotAddress)(address)) {\n            (0,_Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n                title: \"Valid Wallet Address\",\n                defaultText: `Enter a valid Polkadot address`,\n                type: \"error\"\n            });\n            return;\n        }\n        if (!executeRecaptcha) {\n            (0,_Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n                title: \"Failed\",\n                defaultText: \"Recaptcha not initialized\",\n                type: \"error\"\n            });\n            return;\n        }\n        const captcha = await executeRecaptcha(\"faucet_dotsama_claim\");\n        participateFaucet({\n            variables: {\n                eventId: projectEventId,\n                taskId: id,\n                providerId: address,\n                captcha\n            },\n            context: {\n                points: task.points\n            },\n            onCompleted: ()=>{\n                onSuccess?.(\"Your transfer is now queued, please come back later to see the status of your transaction\", \"Transfer Queued\");\n            },\n            onError: (error)=>{\n                (0,_Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n                    title: \"Claim Failed\",\n                    defaultText: `We were not able to claim your tokens. (${error?.message})`,\n                    type: \"error\",\n                    error\n                });\n                onError?.();\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseFaucetBody__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        authProvider: _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AuthProvider.DOTSAMA_BLOCKCHAIN,\n        claiming: verifying,\n        onClaim: handleVerify,\n        task: task,\n        taskParticipation: taskParticipation,\n        projectEventId: projectEventId,\n        verified: verified,\n        scoredPoints: scoredPoints\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Faucet\\\\Dot\\\\FaucetDotsamaBody.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FaucetDotsamaBody);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Faucet/Dot/FaucetDotsamaBody.tsx\n");

/***/ }),

/***/ "./components/Tasks/Provider/ProviderButton.tsx":
/*!******************************************************!*\
  !*** ./components/Tasks/Provider/ProviderButton.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProviderButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Hooks/useUserEventConnection */ \"./hooks/useUserEventConnection.ts\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/usePreferredEventConnection */ \"./components/Tasks/hooks/usePreferredEventConnection.ts\");\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__, _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_7__]);\n([_Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__, _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction ProviderButton({ projectEventId, providerType, providerData, buttonText = \"Verify using\", ...props }) {\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const { connection, loading } = (0,_Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__.useUserEventConnection)(projectEventId, providerType);\n    const { unsetPreferredEventConnection } = (0,_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__.usePreferredEventConnection)();\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n        ...props,\n        loading: true,\n        children: \"Initializing...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n        lineNumber: 39,\n        columnNumber: 7\n    }, this);\n    const buttonTpl = ()=>{\n        if (!connection) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: \" Verify \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                lineNumber: 46,\n                columnNumber: 14\n            }, this);\n        }\n        const buttonTextTpl = ()=>{\n            switch(providerType){\n                case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AuthProvider.EVM_BLOCKCHAIN:\n                    return (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.shortenAddress)(connection.providerId, 8);\n                case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AuthProvider.DOTSAMA_BLOCKCHAIN:\n                    return (0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__.formatDotsamaAuth)(connection, providerData.blockchainId);\n                default:\n                    return connection.username || connection.firstName || connection.providerId;\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex space-x-2 items-center\",\n            children: [\n                connection.picture && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: connection.picture,\n                    className: \"h-8 w-8 flex-shrink-0 border-2 border-white rounded-full\",\n                    alt: \"profile image\",\n                    onError: ()=>setError(true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        buttonText,\n                        \" \",\n                        buttonTextTpl()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                ...props,\n                children: buttonTpl()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            connection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-primary text-sm cursor-pointer text-center\",\n                onClick: ()=>{\n                    unsetPreferredEventConnection(projectEventId, providerType);\n                },\n                children: \"Use another account\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Provider/ProviderButton.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/CountDownTimer.tsx":
/*!********************************************************!*\
  !*** ./components/Tasks/components/CountDownTimer.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountdownTimer: () => (/* binding */ CountdownTimer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"moment\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction CountdownTimer({ target }) {\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let intervalId = setInterval(()=>{\n            const now = moment__WEBPACK_IMPORTED_MODULE_2___default()();\n            const duration = moment__WEBPACK_IMPORTED_MODULE_2___default().duration(target.diff(now));\n            const days = Math.floor(duration.asDays());\n            const hours = Math.floor(duration.hours());\n            const minutes = Math.floor(duration.minutes());\n            const seconds = Math.floor(duration.seconds());\n            setTimeLeft(`${days ? days.toString() + \" days \" : \"\"}${hours.toString().padStart(2, \"0\")}:${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`);\n        }, 1000);\n        return ()=>clearInterval(intervalId);\n    }, [\n        target\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: timeLeft\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CountDownTimer.tsx\",\n        lineNumber: 29,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/CountDownTimer.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/TaskCompletedCard.tsx":
/*!***********************************************************!*\
  !*** ./components/Tasks/components/TaskCompletedCard.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/helpers/frequency */ \"./helpers/frequency.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _CountDownTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CountDownTimer */ \"./components/Tasks/components/CountDownTimer.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nconst TaskCompletedCard = ({ points, title, subTitle, frequency, status })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"tasks.completedCard\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const getBackground = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return \"linear-gradient(90deg, #c2410c 0%, #c2640c 50%, #c2800c 100%)\";\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return \"linear-gradient(90deg, #f16363 0%, #f65c5c 50%, #ef4646 100%)\";\n            default:\n                return \"\";\n        }\n    };\n    const getIcon = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Warning, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    function getTitle(title, points, globalT) {\n        if (title) {\n            return title;\n        }\n        if (points) {\n            if ((0,_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__.isFeatureEnabled)(\"POINTS\")) {\n                return `${t(\"title.points\", {\n                    points: points,\n                    projectPoints: globalT(\"projectPoints\")\n                })}`;\n            }\n            return `${t(\"title.noPoints\")}`;\n        }\n        return `${t(\"title.noPoints\")}`;\n    }\n    const getSubTitle = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n                return `${t(\"subtitle.inReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return `${t(\"subtitle.inAIReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return `${t(\"subtitle.valid\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return `${t(\"subtitle.invalid\")}`;\n            default:\n                return `${t(\"subtitle.valid\")}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col p-4 py-6 bg-primary rounded-xl relative overflow-hidden gradient-primary text-primary-foreground shadow\",\n        style: {\n            background: getBackground()\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg  mb-0\",\n                                children: getTitle(title, points, globalT)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: frequency && frequency !== _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.Frequency.NONE ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        \"Resets in \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountDownTimer__WEBPACK_IMPORTED_MODULE_4__.CountdownTimer, {\n                                                target: _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__.frequencyConfig[frequency].cutOff()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : subTitle ? subTitle : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: getSubTitle()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskCompletedCard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/TaskCompletedCard.tsx\n");

/***/ }),

/***/ "./components/TextEditor.tsx":
/*!***********************************!*\
  !*** ./components/TextEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! draft-js */ \"draft-js\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(draft_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! draft-js/dist/Draft.css */ \"./node_modules/draft-js/dist/Draft.css\");\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction TextEditor({ readonly = true, defaultValue, expandable = false, maxHeight = 100, className }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n    const [isOverflow, setIsOverflow] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        const container = ref.current;\n        if (!container || !setIsOverflow) return;\n        if (expandable && container.offsetHeight >= maxHeight) {\n            setIsOverflow(true);\n        }\n    }, [\n        ref,\n        setIsOverflow\n    ]);\n    function Link(props) {\n        const { url } = props.contentState.getEntity(props.entityKey).getData();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: url,\n            target: \"_blank\",\n            rel: \"noreferrer nofollow\",\n            referrerPolicy: \"no-referrer\",\n            style: {\n                color: currentTheme === \"dark\" ? \"#93cefe\" : \"#3b5998\",\n                textDecoration: \"underline\"\n            },\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    function findLinkEntities(contentBlock, callback, contentState) {\n        contentBlock.findEntityRanges((character)=>{\n            const entityKey = character.getEntity();\n            return entityKey !== null && contentState.getEntity(entityKey).getType() === \"LINK\";\n        }, callback);\n    }\n    const decorator = new draft_js__WEBPACK_IMPORTED_MODULE_3__.CompositeDecorator([\n        {\n            strategy: findLinkEntities,\n            component: Link\n        }\n    ]);\n    let editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createEmpty(decorator);\n    if (!defaultValue) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    if (defaultValue) {\n        try {\n            const parsedJson = JSON.parse(defaultValue);\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createWithContent((0,draft_js__WEBPACK_IMPORTED_MODULE_3__.convertFromRaw)(parsedJson), decorator);\n        } catch (err) {\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_3__.EditorState.createWithContent(draft_js__WEBPACK_IMPORTED_MODULE_3__.ContentState.createFromText(defaultValue), decorator);\n        }\n    }\n    if (!editorState.getCurrentContent().hasText()) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            isOverflow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 z-10 h-[40px] w-full bg-gradient-to-t from-background/60 to-background/0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute z-20 flex items-center w-full justify-center -bottom-3`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-2 z-10 rounded-full border bg-background/90 text-cs text-sm font-extrabold\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.CaretUp, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.CaretDown, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`text-base text-ch leading-normal overflow-hidden`, className, isExpanded ? \"mb-10\" : \"\"),\n                ref: ref,\n                style: expandable && !isExpanded ? {\n                    maxHeight,\n                    marginBottom: 0\n                } : {},\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(draft_js__WEBPACK_IMPORTED_MODULE_3__.Editor, {\n                    editorState: editorState,\n                    readOnly: readonly,\n                    onChange: ()=>{}\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TextEditor.tsx\n");

/***/ }),

/***/ "./hooks/useGetBlockchain.ts":
/*!***********************************!*\
  !*** ./hooks/useGetBlockchain.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGetBlockchain: () => (/* binding */ useGetBlockchain)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst GET_BLOCKCHAIN = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql)`\r\n  query blockchain($id: ID!) {\r\n    blockchain(id: $id) {\r\n      id\r\n      name\r\n      chainId\r\n      icon\r\n      blockExplorerUrls\r\n      rpcUrls\r\n      nativeCurrency\r\n      decimals\r\n      type\r\n    }\r\n  }\r\n`;\nfunction useGetBlockchain(id) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.useQuery)(GET_BLOCKCHAIN, {\n        variables: {\n            id\n        },\n        skip: !id\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VHZXRCbG9ja2NoYWluLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQztBQUcvQyxNQUFNRSxpQkFBaUJGLG1EQUFHLENBQUM7Ozs7Ozs7Ozs7Ozs7O0FBYzNCLENBQUM7QUFFTSxTQUFTRyxpQkFBaUJDLEVBQVU7SUFDekMsT0FBT0gsd0RBQVFBLENBQ2JDLGdCQUNBO1FBQ0VHLFdBQVc7WUFDVEQ7UUFDRjtRQUNBRSxNQUFNLENBQUNGO0lBQ1Q7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2hvb2tzL3VzZUdldEJsb2NrY2hhaW4udHM/ZGMxYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBncWwsIHVzZVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnO1xyXG5pbXBvcnQgeyBCbG9ja2NoYWluLCBRdWVyeV9ibG9ja2NoYWluQXJncyB9IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuXHJcbmNvbnN0IEdFVF9CTE9DS0NIQUlOID0gZ3FsYFxyXG4gIHF1ZXJ5IGJsb2NrY2hhaW4oJGlkOiBJRCEpIHtcclxuICAgIGJsb2NrY2hhaW4oaWQ6ICRpZCkge1xyXG4gICAgICBpZFxyXG4gICAgICBuYW1lXHJcbiAgICAgIGNoYWluSWRcclxuICAgICAgaWNvblxyXG4gICAgICBibG9ja0V4cGxvcmVyVXJsc1xyXG4gICAgICBycGNVcmxzXHJcbiAgICAgIG5hdGl2ZUN1cnJlbmN5XHJcbiAgICAgIGRlY2ltYWxzXHJcbiAgICAgIHR5cGVcclxuICAgIH1cclxuICB9XHJcbmA7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlR2V0QmxvY2tjaGFpbihpZDogc3RyaW5nKSB7XHJcbiAgcmV0dXJuIHVzZVF1ZXJ5PHsgYmxvY2tjaGFpbjogQmxvY2tjaGFpbiB9LCBRdWVyeV9ibG9ja2NoYWluQXJncz4oXHJcbiAgICBHRVRfQkxPQ0tDSEFJTixcclxuICAgIHtcclxuICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgaWQsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNraXA6ICFpZCxcclxuICAgIH0sXHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiZ3FsIiwidXNlUXVlcnkiLCJHRVRfQkxPQ0tDSEFJTiIsInVzZUdldEJsb2NrY2hhaW4iLCJpZCIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./hooks/useGetBlockchain.ts\n");

/***/ }),

/***/ "./hooks/useValueByKeyGroup.ts":
/*!*************************************!*\
  !*** ./hooks/useValueByKeyGroup.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useValueByKeyGroup)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst KEY_VALUE_GROUPS = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql)`\r\n  query valueByKeyGroup($groupId: ID!, $key: String!) {\r\n    valueByKeyGroup(groupId: $groupId, key: $key)\r\n  }\r\n`;\nfunction useValueByKeyGroup(groupId, key, disabled) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.useQuery)(KEY_VALUE_GROUPS, {\n        variables: {\n            groupId: groupId,\n            key: key\n        },\n        skip: disabled || !groupId || !key\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VWYWx1ZUJ5S2V5R3JvdXAudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQytDO0FBRS9DLE1BQU1FLG1CQUFtQkYsbURBQUcsQ0FBQzs7OztBQUk3QixDQUFDO0FBRWMsU0FBU0csbUJBQ3RCQyxPQUEyQixFQUMzQkMsR0FBdUIsRUFDdkJDLFFBQWlCO0lBRWpCLE9BQU9MLHdEQUFRQSxDQUNiQyxrQkFDQTtRQUNFSyxXQUFXO1lBQ1RILFNBQVNBO1lBQ1RDLEtBQUtBO1FBQ1A7UUFDQUcsTUFBTUYsWUFBWSxDQUFDRixXQUFXLENBQUNDO0lBQ2pDO0FBRUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9ob29rcy91c2VWYWx1ZUJ5S2V5R3JvdXAudHM/NDUzYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBRdWVyeV92YWx1ZUJ5S2V5R3JvdXBBcmdzIH0gZnJvbSAnQGFpcmx5ZnQvdHlwZXMnO1xyXG5pbXBvcnQgeyBncWwsIHVzZVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnO1xyXG5cclxuY29uc3QgS0VZX1ZBTFVFX0dST1VQUyA9IGdxbGBcclxuICBxdWVyeSB2YWx1ZUJ5S2V5R3JvdXAoJGdyb3VwSWQ6IElEISwgJGtleTogU3RyaW5nISkge1xyXG4gICAgdmFsdWVCeUtleUdyb3VwKGdyb3VwSWQ6ICRncm91cElkLCBrZXk6ICRrZXkpXHJcbiAgfVxyXG5gO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlVmFsdWVCeUtleUdyb3VwKFxyXG4gIGdyb3VwSWQ6IHN0cmluZyB8IHVuZGVmaW5lZCxcclxuICBrZXk6IHN0cmluZyB8IHVuZGVmaW5lZCxcclxuICBkaXNhYmxlZDogYm9vbGVhbixcclxuKSB7XHJcbiAgcmV0dXJuIHVzZVF1ZXJ5PHsgdmFsdWVCeUtleUdyb3VwOiBTdHJpbmcgfSwgUXVlcnlfdmFsdWVCeUtleUdyb3VwQXJncz4oXHJcbiAgICBLRVlfVkFMVUVfR1JPVVBTLFxyXG4gICAge1xyXG4gICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICBncm91cElkOiBncm91cElkIGFzIHN0cmluZyxcclxuICAgICAgICBrZXk6IGtleSBhcyBzdHJpbmcsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNraXA6IGRpc2FibGVkIHx8ICFncm91cElkIHx8ICFrZXksXHJcbiAgICB9LFxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbImdxbCIsInVzZVF1ZXJ5IiwiS0VZX1ZBTFVFX0dST1VQUyIsInVzZVZhbHVlQnlLZXlHcm91cCIsImdyb3VwSWQiLCJrZXkiLCJkaXNhYmxlZCIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./hooks/useValueByKeyGroup.ts\n");

/***/ })

};
;